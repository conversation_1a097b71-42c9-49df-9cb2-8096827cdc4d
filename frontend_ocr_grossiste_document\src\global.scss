/*
 * App Global CSS
 * ----------------------------------------------------------------------------
 * Put style rules here that you want to apply globally. These styles are for
 * the entire app and not just one component. Additionally, this file can be
 * used as an entry point to import other CSS/Sass files to be included in the
 * output CSS.
 * For more information on global stylesheets, visit the documentation:
 * https://ionicframework.com/docs/layout/global-stylesheets
 */

/* Core CSS required for Ionic components to work properly */
@import "@ionic/angular/css/core.css";

/* Basic CSS for apps built with Ionic */
@import "@ionic/angular/css/normalize.css";
@import "@ionic/angular/css/structure.css";
@import "@ionic/angular/css/typography.css";
@import "@ionic/angular/css/display.css";

/* Optional CSS utils that can be commented out */
@import "@ionic/angular/css/padding.css";
@import "@ionic/angular/css/float-elements.css";
@import "@ionic/angular/css/text-alignment.css";
@import "@ionic/angular/css/text-transformation.css";
@import "@ionic/angular/css/flex-utils.css";

/**
 * Ionic Dark Mode
 * -----------------------------------------------------
 * For more info, please see:
 * https://ionicframework.com/docs/theming/dark-mode
 */

/* @import "@ionic/angular/css/palettes/dark.always.css"; */
/* @import "@ionic/angular/css/palettes/dark.class.css"; */
@import "@ionic/angular/css/palettes/dark.system.css";

// Force light mode
:root,
:root[mode=ios],
:root[mode=md] {
  --ion-background-color: #ffffff;
  --background: #ffffff;
  --background-activated: #ffffff;
  --background-focused: #ffffff;
  --background-hover: currentColor;
  --ion-background-color-rgb: 255,255,255;
  --ion-text-color: #000000;
  --ion-text-color-rgb: 0,0,0;
  --ion-color-step-50: #f2f2f2;
  --ion-color-step-100: #e6e6e6;
  --ion-color-step-150: #d9d9d9;
  --ion-color-step-200: #cccccc;
  --ion-color-step-250: #bfbfbf;
  --ion-color-step-300: #b3b3b3;
  --ion-color-step-350: #a6a6a6;
  --ion-color-step-400: #999999;
  --ion-color-step-450: #8c8c8c;
  --ion-color-step-500: #808080;
  --ion-color-step-550: #737373;
  --ion-color-step-600: #666666;
  --ion-color-step-650: #595959;
  --ion-color-step-700: #4d4d4d;
  --ion-color-step-750: #404040;
  --ion-color-step-800: #333333;
  --ion-color-step-850: #262626;
  --ion-color-step-900: #191919;
  --ion-color-step-950: #0d0d0d;
}

// Force all ion-content to use light theme
ion-content {
  --background: var(--ion-background-color);
  --color: var(--ion-text-color);
}

ion-item::part(native) {
  background: #fff;
}

::ng-deep ion-item::part(native) {
  background: #fff;
}

// Force all modals and popups to use light theme
ion-modal,
ion-popover,
ion-action-sheet {
  --background: var(--ion-background-color) !important;
  --color: var(--ion-text-color) !important;
}
.footer-md, .header-md {
  -webkit-box-shadow: none;
  box-shadow: none;
}

::ng-deep ion-alert .alert-message{
  padding: 10px 5px;
}

::ng-deep ion-alert .alert-message h3 {
  font-size: 13px;
  color: red;
  font-weight: bold;
  letter-spacing: 1px;
}

::ng-deep ion-alert .alert-message ul li {
  text-align: left;
  padding-left: 20px;
  color: #333333;
  line-height: 1.5;
  font-size: 13px;
  font-weight: bold;
}


.scanner-modal {
  --height: 100%;
  --border-radius: 16px;
  
  .modal-wrapper {
    border-radius: 16px;
  }
}
// // Swiper Container

// swiper-container {
//     --swiper-pagination-bullet-inactive-color: var(--ion-color-step-200, #cccccc);
//     --swiper-pagination-color: var(--ion-color-primary, #2F4FCD);
//     --swiper-pagination-progressbar-bg-color: rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.25);
//     --swiper-scrollbar-bg-color: rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.1);
//     --swiper-scrollbar-drag-bg-color: rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.5);
//   }
  
//   swiper-slide {
//     display: flex;
//     position: relative;
//     flex-direction: column;
//     flex-shrink: 0;
//     align-items: center;
//     justify-content: center;
//     width: 100%;
//     height: 100%;
//     font-size: 18px;
//     text-align: center;
//     box-sizing: border-box;
//   }
  
//   swiper-slide img {
//     width: auto;
//     max-width: 100%;
//     height: auto;
//     max-height: 100%;
//   }
  
//   .swiper-pagination-bullet-active{
//       width: 50px;
//       height: 5px;
//       border-radius: 20px !important;
//   }