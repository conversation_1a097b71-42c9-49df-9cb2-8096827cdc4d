import { Injectable } from '@angular/core';
import { CanActivate, Router } from '@angular/router';
import { StorageService } from '../services/storage.service';

@Injectable({
  providedIn: 'root',
})
export class OnboardingGuard implements CanActivate {
  constructor(private storageService: StorageService, private router: Router) {}

  async canActivate(): Promise<boolean> {
    const hasSeenOnboarding = await this.storageService.get('hasSeenOnboarding');
    if (hasSeenOnboarding) {
      this.router.navigate(['/welcome']);
      return false;
    }
    return true;
  }
}