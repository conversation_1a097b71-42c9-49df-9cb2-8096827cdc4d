{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"app": {"projectType": "application", "schematics": {}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "www", "index": "src/index.html", "main": "src/main.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "src/assets", "output": "assets"}, {"glob": "**/*.svg", "input": "node_modules/ionicons/dist/ionicons/svg", "output": "./svg"}, {"glob": "**/*", "input": "node_modules/image-cropper-component-custom/dist/image-cropper-component", "output": "assets/image-cropper"}], "styles": ["node_modules/swiper/swiper-bundle.min.css", "src/global.scss", "src/theme/variables.scss"], "scripts": ["node_modules/swiper/swiper-bundle.min.js"]}, "configurations": {"webproduction": {"budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "10kb", "maximumError": "20kb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.web.prod.ts"}]}, "webpreprod": {"budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "10kb", "maximumError": "20kb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.web.preprod.ts"}], "outputHashing": "all"}, "preprod": {"budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "10kb", "maximumError": "20kb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.preprod.ts"}], "outputHashing": "all"}, "production": {"budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "10kb", "maximumError": "20kb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "outputHashing": "all"}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}, "ci": {"progress": false}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "app:build:production"}, "development": {"buildTarget": "app:build:development"}, "ci": {"progress": false}, "webproduction": {"buildTarget": "app:build:webproduction"}, "webpreprod": {"buildTarget": "app:build:webpreprod"}, "preprod": {"buildTarget": "app:build:preprod"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "app:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.spec.json", "karmaConfig": "karma.conf.js", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "src/assets", "output": "assets"}, {"glob": "**/*.svg", "input": "node_modules/ionicons/dist/ionicons/svg", "output": "./svg"}], "styles": ["node_modules/swiper/swiper-bundle.min.css", "src/global.scss", "src/theme/variables.scss"], "scripts": ["node_modules/swiper/swiper-bundle.min.js"]}, "configurations": {"ci": {"progress": false, "watch": false}}}, "lint": {"builder": "@angular-eslint/builder:lint", "options": {"lintFilePatterns": ["src/**/*.ts", "src/**/*.html"]}}}}}, "cli": {"schematicCollections": ["@ionic/angular-toolkit"], "analytics": false}, "schematics": {"@ionic/angular-toolkit:component": {"styleext": "scss"}, "@ionic/angular-toolkit:page": {"styleext": "scss"}}}