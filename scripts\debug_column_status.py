#!/usr/bin/env python3
"""
Debug script to check the actual status of src_app column
"""

import sys
import argparse
from pathlib import Path
from dotenv import load_dotenv
import logging

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.app.database.connection import db_manager
from sqlalchemy import text

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def check_table_structure():
    """Check the actual table structure."""
    try:
        with db_manager.engine.connect() as conn:
            # Get all columns in the table
            result = conn.execute(text("""
                SELECT column_name, data_type, column_default, is_nullable
                FROM information_schema.columns 
                WHERE table_name = 'pre_bl_ocr' 
                AND table_schema = 'public'
                ORDER BY ordinal_position
            """))
            
            columns = result.fetchall()
            
            logger.info("📋 Current table structure for pre_bl_ocr:")
            for col in columns:
                logger.info(f"  - {col[0]} ({col[1]}) | Default: {col[2]} | Nullable: {col[3]}")
            
            # Check specifically for src_app
            src_app_exists = any(col[0] == 'src_app' for col in columns)
            if src_app_exists:
                logger.info("✅ src_app column EXISTS in information_schema")
            else:
                logger.error("❌ src_app column NOT FOUND in information_schema")
            
            return src_app_exists
            
    except Exception as e:
        logger.error(f"❌ Error checking table structure: {e}")
        return False

def test_direct_query():
    """Test direct queries on the table."""
    try:
        with db_manager.engine.connect() as conn:
            # Try to describe the table using PostgreSQL specific command
            logger.info("🔍 Testing direct table access...")
            
            # Count total records
            result = conn.execute(text("SELECT COUNT(*) FROM pre_bl_ocr"))
            count = result.scalar()
            logger.info(f"📊 Total records in pre_bl_ocr: {count}")
            
            # Try to select src_app column
            try:
                result = conn.execute(text("SELECT src_app FROM pre_bl_ocr LIMIT 1"))
                row = result.fetchone()
                if row:
                    logger.info(f"✅ Successfully selected src_app: {row[0]}")
                else:
                    logger.info("✅ src_app column exists but no data")
                return True
            except Exception as e:
                logger.error(f"❌ Cannot select src_app column: {e}")
                return False
                
    except Exception as e:
        logger.error(f"❌ Error in direct query test: {e}")
        return False

def check_postgresql_version_and_schema():
    """Check PostgreSQL version and current schema."""
    try:
        with db_manager.engine.connect() as conn:
            # Check PostgreSQL version
            result = conn.execute(text("SELECT version()"))
            version = result.scalar()
            logger.info(f"🐘 PostgreSQL version: {version}")
            
            # Check current database
            result = conn.execute(text("SELECT current_database()"))
            db_name = result.scalar()
            logger.info(f"🗄️ Current database: {db_name}")
            
            # Check current schema
            result = conn.execute(text("SELECT current_schema()"))
            schema = result.scalar()
            logger.info(f"📁 Current schema: {schema}")
            
            # List all schemas
            result = conn.execute(text("SELECT schema_name FROM information_schema.schemata ORDER BY schema_name"))
            schemas = [row[0] for row in result.fetchall()]
            logger.info(f"📂 Available schemas: {', '.join(schemas)}")
            
    except Exception as e:
        logger.error(f"❌ Error checking PostgreSQL info: {e}")

def check_table_in_all_schemas():
    """Check if pre_bl_ocr table exists in different schemas."""
    try:
        with db_manager.engine.connect() as conn:
            result = conn.execute(text("""
                SELECT table_schema, table_name 
                FROM information_schema.tables 
                WHERE table_name = 'pre_bl_ocr'
                ORDER BY table_schema
            """))
            
            tables = result.fetchall()
            logger.info("🔍 pre_bl_ocr table found in schemas:")
            for table in tables:
                logger.info(f"  - {table[0]}.{table[1]}")
                
                # Check columns in each schema
                col_result = conn.execute(text("""
                    SELECT column_name 
                    FROM information_schema.columns 
                    WHERE table_name = 'pre_bl_ocr' 
                    AND table_schema = :schema
                    ORDER BY ordinal_position
                """), {"schema": table[0]})
                
                columns = [row[0] for row in col_result.fetchall()]
                has_src_app = 'src_app' in columns
                logger.info(f"    Columns: {', '.join(columns)}")
                logger.info(f"    Has src_app: {'✅ YES' if has_src_app else '❌ NO'}")
                
    except Exception as e:
        logger.error(f"❌ Error checking tables in schemas: {e}")

def main():
    """Main diagnostic function."""
    parser = argparse.ArgumentParser(description='Debug src_app column status')
    parser.add_argument('--env-file', default='.env.local', help='Environment file to load (default: .env.local)')
    args = parser.parse_args()
    
    # Load environment file
    env_file_path = project_root / args.env_file
    if env_file_path.exists():
        load_dotenv(env_file_path)
        logger.info(f"✅ Loaded environment from {args.env_file}")
    else:
        logger.error(f"❌ Environment file not found: {args.env_file}")
        sys.exit(1)
    
    logger.info("🔍 Starting comprehensive database diagnostic...")
    
    # Test 1: Check PostgreSQL info
    logger.info("\n1️⃣ Checking PostgreSQL version and schema info...")
    check_postgresql_version_and_schema()
    
    # Test 2: Check table in all schemas
    logger.info("\n2️⃣ Checking pre_bl_ocr table in all schemas...")
    check_table_in_all_schemas()
    
    # Test 3: Check table structure
    logger.info("\n3️⃣ Checking table structure...")
    column_exists = check_table_structure()
    
    # Test 4: Test direct queries
    logger.info("\n4️⃣ Testing direct queries...")
    query_works = test_direct_query()
    
    # Summary
    logger.info("\n📋 DIAGNOSTIC SUMMARY:")
    logger.info(f"  Column in information_schema: {'✅ YES' if column_exists else '❌ NO'}")
    logger.info(f"  Direct query works: {'✅ YES' if query_works else '❌ NO'}")
    
    if column_exists and query_works:
        logger.info("🎉 Everything looks good! The issue might be with PgAdmin cache.")
        logger.info("💡 Try refreshing PgAdmin or reconnecting to the database.")
    elif column_exists and not query_works:
        logger.info("⚠️ Column exists in schema but queries fail - possible transaction issue.")
    elif not column_exists:
        logger.info("❌ Column doesn't exist - migration may have failed silently.")

if __name__ == "__main__":
    main()
