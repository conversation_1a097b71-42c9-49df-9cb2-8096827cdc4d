import { p as promiseResolve, b as bootstrapLazy } from './index-be3234cf.js';
export { s as setNonce } from './index-be3234cf.js';

/*
 Stencil Client Patch Browser v4.12.0 | MIT Licensed | https://stenciljs.com
 */
const patchBrowser = () => {
    const importMeta = import.meta.url;
    const opts = {};
    if (importMeta !== '') {
        opts.resourcesUrl = new URL('.', importMeta).href;
    }
    return promiseResolve(opts);
};

patchBrowser().then(options => {
  return bootstrapLazy([["image-cropper",[[1,"image-cropper",{"img":[16],"rect":[16],"quad":[16],"license":[1],"hidefooter":[1],"handlersize":[1],"inactiveSelections":[16],"rotation":[2],"viewBox":[32],"activeStroke":[32],"inActiveStroke":[32],"selectedHandlerIndex":[32],"points":[32],"offsetX":[32],"offsetY":[32],"scale":[32],"resetStates":[64],"getAllSelections":[64],"getPoints":[64],"getQuad":[64],"getRect":[64],"detect":[64]},null,{"img":["watchImgPropHandler"],"rect":["watchRectPropHandler"],"quad":["watchQuadPropHandler"]}]]]], options);
});

//# sourceMappingURL=image-cropper-component.js.map