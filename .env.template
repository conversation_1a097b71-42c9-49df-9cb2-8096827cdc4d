# OCR Document Grossiste - Environment Configuration Template
# Copy this file to .env.local, .env.preprod, or .env.prod and fill in your values

# Application Environment
# Options: local, preprod, prod
ENVIRONMENT=local
DEBUG=True
SYS_ARGV=main

# Tesseract OCR Configuration
TESSERACT_PATH=C:\Program Files\Tesseract-OCR\tesseract.exe

# API Configuration
API_URL=https://winproduit.sophatel.com:8001
TAP_URL=https://winproduit.sophatel.com:8005

# WinPlus ERP Integration
WINPLUS_AUTH_USER=https://vps5.sophatel.com:5305/api/user/auth
WINPLUS_AUTH_TENANT=https://vps5.sophatel.com:5305/api/user/auth-tenant
WINPLUS_URL=https://vps5.sophatel.com:5305

# Pharmalien Integration
PHARMALIEN_AUTH_URL=https://vps5.sophatel.com:4201/api/user/auth

# JWT Authentication
SECRET_KEY=paython-ocr-insecure-#8!7z!_
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=180 # 3 hours

# PostgreSQL Database Configuration
POSTGRES_HOST=your-postgres-host.com
POSTGRES_PORT=5432
POSTGRES_DB=ocr_document_grossiste
POSTGRES_USER=your_postgres_user
POSTGRES_PASSWORD=your_postgres_password
POSTGRES_SSL_MODE=require

# Example configurations for different environments:

# Local Development (PostgreSQL on localhost)
# POSTGRES_HOST=localhost
# POSTGRES_PORT=5432
# POSTGRES_SSL_MODE=disable

# Production (Cloud PostgreSQL)
# POSTGRES_HOST=your-cloud-postgres.amazonaws.com
# POSTGRES_PORT=5432
# POSTGRES_SSL_MODE=require

# VPS PostgreSQL
# POSTGRES_HOST=your-vps-ip-address
# POSTGRES_PORT=5432
# POSTGRES_SSL_MODE=prefer
