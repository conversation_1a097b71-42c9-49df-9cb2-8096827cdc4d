<ion-header>
  <ion-toolbar>
    <ion-title>Scanner votre BL</ion-title>
    <ion-buttons slot="end">
      <ion-button (click)="scan_bl()">
        <app-custom-icon name="re-scan"></app-custom-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content class="process-doc-content">
  
  <div class="select-supplier-wrapper">
    <ion-select
      [(ngModel)]="selectedSupplier"
      label="Sélection du fournisseur"
      label-placement="floating"
      fill="outline"
      (ionChange)="onSupplierChange($event)"
    >
      <ion-select-option
        *ngFor="let option of suppliers"
        [value]="option.value"
        >{{ option.label }}</ion-select-option
      >
    </ion-select>
  </div>

  <swiper-container
    effect="coverflow"
    grab-cursor="true"
    centered-slides="false"
    slides-per-view="auto"
    coverflow-effect-rotate="50"
    coverflow-effect-stretch="0"
    coverflow-effect-depth="100"
    coverflow-effect-modifier="1"
    coverflow-effect-slide-shadows="true"
    class="swiper top-swiper"
    #swiper_docs
    (swiperslidechange)="onSlideChange()"
  >
    <swiper-slide *ngFor="let item of listDocs; let i = index" class="swiper-slide">
      <div class="process-doc-wrapper">

        <div class="remove-doc" (click)="removeDoc(i)">
          <ion-icon name="close-circle-sharp"></ion-icon>
        </div>
        <div class="image-container">
          <img id="unfilteredImage" [src]="item.cropped_image" alt="Unfiltered Document" loading="lazy" />

          <div
            [ngClass]="{'filter-animation': true, 'd-block-img': isScanning, 'hide-animation': hideAnimation, 'scanning': isScanning}"
          >
            <div class="scan-line" *ngIf="isScanning"></div>
            <div *ngIf="!filteredImageLoaded" class="loading-placeholder">
              <ion-spinner></ion-spinner>
            </div>
            <img
              [src]="item.filtered_image"
              alt="Filtered Document"
              [hidden]="!filteredImageLoaded"
              (load)="onFilteredImageLoad()"
            />
          </div>
        </div>
      </div>
    </swiper-slide>
    <swiper-slide class="swiper-slide last-swiper-slide-scan" [routerLink]="['/scan-bl']">
      <div class="scan-bl-wrapper">
        <app-custom-icon name="file-import" class="file-import-icon"></app-custom-icon>
        <div class="content">
          <h2>Ajouter une autre page</h2>
          <p>La page doit être un même Bon Livraison</p>
        </div>
      </div>
    </swiper-slide>
  </swiper-container>
</ion-content>

<ion-footer>
  <ion-toolbar>
    <ion-buttons>
      <ion-button class="menu-button" size="small" (click)="removeAllDoc()">
        <app-custom-icon name="delete"></app-custom-icon>
      </ion-button>
      <ion-button class="menu-button-middle"(click)="valider()">
        <app-custom-icon name="extract"></app-custom-icon>
        <span>VALIDER</span>
      </ion-button>
      <ion-button class="menu-button active" size="small" (click)="reTakePhoto()">
        <app-custom-icon name="re-scan-2"></app-custom-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-footer>
