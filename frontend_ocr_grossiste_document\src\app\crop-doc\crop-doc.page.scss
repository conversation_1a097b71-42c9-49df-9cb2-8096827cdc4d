@import url("https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap");

* {
  font-family: "Inter", sans-serif;
  font-optical-sizing: auto;
}

:host{
  height: 100dvh;
}

ion-content::part(scroll) {
  overflow-y: hidden !important;
  --overflow: hidden !important;
}

ion-content {
  --offset-top : 0px !important;
}

ion-header {
  height: 70px;
  --border: 0;
  display: flex;
  align-items: center;
}
ion-header ion-toolbar {
  height: 100%;
  --border: 0;
  --border-width: 0;
  display: flex;
  justify-content: center;
  align-items: flex-end;
  flex-direction: row;
}

ion-header ion-toolbar ion-title {
  font-size: 26px;
  font-weight: 700;
  color: #2f4fcd;
  text-align: left;
  width: 100%;
  padding-left: 2rem;
  margin-left: 1rem;
}

ion-header ion-toolbar ion-icon {
  color: #101010;
  padding-right: 1rem;
}

.crop-doc-wrapper {
  background: url("/assets/bg-scan-bl.png") no-repeat center center fixed;
  background-size: cover;
  height: 80%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin-top: 2rem;
}

// #cropper{
//   display: flex;
//   flex-direction: column;
//   justify-content: center;
//   align-items: center;
// }

#cropper {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 20px;
  overflow: hidden;
  
  image-cropper {
    display: flex;
    justify-content: center;
    align-items: center;
    max-width: 100%;
    max-height: calc(100vh - 140px); // Subtract header and footer height was 180px
    
    ::ng-deep {
      canvas {
        max-width: 100%;
        max-height: 100%;
        object-fit: contain;
      }
    }
  }
}


::ng-deep image-cropper {
  max-height: 80vh !important;
  display: flex;
  justify-content: center;
  align-items: center;
  
  // Add responsive scaling
  @media (max-width: 768px) {
    max-height: 70vh !important;
  }
  
  @media (max-width: 480px) {
    max-height: 60vh !important;
  }
}

::ng-deep .crop-doc-wrapper .file-import-icon img {
  width: 150px !important;
  height: 150px !important;
}
::ng-deep .crop-doc-wrapper .arrow-bottom-icon {
  // margin-top: 50px;
  img {
    width: 100px !important;
    height: 100px !important;
  }
}

.crop-doc-wrapper .content {
  padding: 10px 50px 0 50px;
}
.crop-doc-wrapper .content h2 {
  color: #9a9a9a;
  font-size: 22px;
  font-weight: 700;
}

.crop-doc-wrapper .content p {
  color: #9a9a9a;
  font-size: 12px;
  text-align: justify;
  padding: 5px 10px;
}

// .crop-doc-content {
//   --offset-top : -20px; 
//   --background: rgba(0, 0, 0, 0.05);
//   display: flex;
//   justify-content: center;
//   align-items: center;
//   text-align: center;
// }

.crop-doc-content {
  --offset-top: 0px !important;
  --background: rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  overflow: hidden;
  height: 100%;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.document-icon {
  font-size: 100px;
  color: #c4c4c4;
}

h2 {
  color: #555555;
  font-size: 18px;
  margin-top: 20px;
}

p {
  color: #888888;
  font-size: 14px;
  margin-top: 10px;
  margin-bottom: 30px;
}

.arrow-icon {
  font-size: 30px;
  color: #3b82f6; // Adjust the color as needed
}

ion-footer {
  background-color: #e5e7eb; // Adjust the background color as needed
}

ion-footer ion-toolbar ion-buttons,
ion-footer ion-toolbar ion-buttons ion-button {
  display: flex;
  justify-content: space-evenly;
  align-items: center;
  flex-direction: row;
}

::ng-deep ion-button.menu-button app-custom-icon img {
  width: 30px !important;
  height: 30px !important;
  color: #000;
}

::ng-deep .menu-button.active app-custom-icon img {
  color: #2f4fcd;
}

::ng-deep .menu-button-middle {
  background-color: #2f4fcd;
  padding: 2px 12px;
  border-radius: 14px;
  width: 160px;
  height: 60px;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: row;
  margin-bottom: 15px;
  app-custom-icon img {
    width: 35px !important;
    height: 35px !important;
    color: #fff;
  }
  span{
    color: #fff;
    font-weight: 500;
    font-size: 16px;
    padding-left: 10px;
  }
}


ion-toolbar {
  --background: transparent;
  --ion-color-primary: #3b82f6; // Adjust the color as needed
}

ion-icon {
  font-size: 24px;
}

ion-footer {
  position: relative;
  background-color: #dddbff;
  height: 110px;
  // border-top: 1px solid #2563eb;
  width: 100%;

  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: row;
}

ion-footer ion-toolbar {
  --border-width: 0;
}

ion-fab {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}

::ng-deep ion-fab-button.menu-button-middle::part(native) {
  background: none;
  border: 0;
  box-shadow: none;
  width: 100% !important;
  color: #fff;
}

::ng-deep ion-fab ion-fab-list {
  display: flex;
  flex-direction: row !important;
  justify-content: space-around;
  align-items: flex-end;
  width: auto !important;
  // padding: 10px 0;
  padding: 10px 20px;
  margin-bottom: 100px;
  height: 100vh;
  transition: all 0.3s ease;
  &.fab-active {
    // background-color: rgba(0, 0, 0, 0.2);
    visibility: visible;
    opacity: 1;
    pointer-events: all;
  }

  &.fab-hidden {
    visibility: hidden;
    opacity: 0;
    pointer-events: none;
  }
}

.bg-hide{
  background-color: rgba(0, 0, 0, 0.1);
}
.initial-bg{
  --background: none;
}

.content-fab-buttom {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}
::ng-deep .content-fab-buttom app-custom-icon img {
  width: 50px !important;
  height: 50px !important;
}
::ng-deep .content-fab-buttom ion-label {
  color: #2f4fcd;
  font-size: 20px;
}

::ng-deep ion-fab ion-fab-list ion-fab-button {
  padding: 10px;
  width: 200px;
  height: 135px;
  transform: translateY(40px);
  transition: all 0.6s ease-in-out;
  &.fab-active {
    transform: translateY(0);
  }
}
::ng-deep ion-fab ion-fab-list ion-fab-button::part(native) {
  border-radius: 16px !important;
}

// / *** Styles alert dialog ***/
::ng-deep .custom-alert-button, custom-alert-button-rename-doc {
  display: inline-block;
  text-align: center;
  font-size: 14px !important;
  font-weight: bold;
}

::ng-deep .custom-alert-button.cancel, ::ng-deep .custom-alert-button-rename-doc.cancel {
  color: #2563eb;
}::ng-deep .android-specific .custom-alert-button.cancel, ::ng-deep .custom-alert-button-rename-doc.cancel {
  width: 35%;
}::ng-deep .android-specific .custom-alert-button.cancel, ::ng-deep .custom-alert-button-rename-doc.cancel {
  width: 35%;
}

::ng-deep .custom-alert-button-rename-doc.cancel {
  font-size: 16px;
}

::ng-deep .custom-alert-button.danger {
  color: red;
}::ng-deep .ios-specific .custom-alert-button.danger {
  width: 50%;
}::ng-deep .android-specific .custom-alert-button.danger {
  width: 55%;
}

::ng-deep .custom-alert-button-rename-doc.rename {
  font-size: 16px;
  color: #535353;
}

// rotate

// .crop-doc-wrapper {
//   display: flex;
//   justify-content: center;
//   align-items: center;
//   height: 100%;
// }

// #cropper {
//   display: none;
//   position: absolute;
//   top: 0;
//   left: 0;
//   right: 0;
//   bottom: 0;
// }

// image-cropper {
//   width: 100%;
//   height: 100%;
//   transition: transform 0.3s ease;
// }


/**
* Loading alert START
*/

::ng-deep .loading:not(.alert-progress) {
  opacity: 0.5;
  pointer-events: none; /* Makes the page unclickable */
  --background: rgba(0, 0, 0, 0.1);
}

// ion-modal
::ng-deep ion-modal {
  height: 23%;
  width: 90%;
  position: absolute;
  top: 35%;
  left: 5%;
  --background:none;
  --backdrop-opacity: var(--ion-backdrop-opacity, 0);
}

::ng-deep .alert-progress{
  position: absolute;
  width: 100%;
  top: 40%;
  // display: flex;
  justify-content: center;
  align-items: center;
}

::ng-deep .alert-progress app-custom-alert{
  width: 90%;
}

::ng-deep .alert-progress{
  display: none;
}
::ng-deep .alert-progress.loading{
  display: flex;
}


/**
* Loading alert  END
*/



// Rotate button styles
.rotate-button {
  --padding-start: 8px;
  --padding-end: 8px;
  margin-right: 12px;
  
  ion-icon {
    font-size: 24px;
    color: #2f4fcd;
    transition: transform 0.3s ease;
  }

  &:active ion-icon {
    transform: rotate(90deg);
  }
}

// Add animation for rotation
@keyframes rotate-animation {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.rotating {
  animation: rotate-animation 0.5s ease;
}

// Enhance the existing header styles
ion-header {
  ion-toolbar {
    --background: #ffffff;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);

    ion-buttons {
      ion-button {
        --background-hover: rgba(47, 79, 205, 0.1);
        --background-activated: rgba(47, 79, 205, 0.2);
        --border-radius: 50%;
        
        &:hover {
          ion-icon {
            color: #2f4fcd;
          }
        }
      }
    }
  }
}

.rotate-button-container {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 1000;
  width: 50%;
}

.rotate-btn {
  background: rgba(255, 255, 255, 0.95);
  border: none;
  border-radius: 50%;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  
  &:hover {
    background: #ffffff;
    box-shadow: 0 4px 12px rgba(47, 79, 205, 0.2);
    transform: translateY(-2px);
  }
  
  &:active {
    transform: translateY(0) scale(0.95);
  }

  ion-icon {
    font-size: 24px;
    color: #2f4fcd;
    transition: transform 0.3s ease;
  }

  &:active ion-icon {
    transform: rotate(90deg);
  }
}



.rotate-btn {
  position: relative;
  
  &.needs-rotation::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: rgba(47, 79, 205, 0.3);
    animation: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;
  }
}



::ng-deep image-cropper .cropper-controls-visual.dashed {
  pointer-events: all; /* Make polygon clickable */
}

::ng-deep image-cropper .cropper-controls-hit {
  pointer-events: all; /* Enable full click area for rects */
  fill: transparent; /* Allow clicks on the entire rect */
}

::ng-deep image-cropper image {
  pointer-events: none; /* Prevent image from blocking clicks */
}

::ng-deep image-cropper svg.cropper-svg {
  pointer-events: bounding-box; /* Ensure SVG covers entire area */
}


// Animation for rotation
@keyframes rotate-animation {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes ping {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  75%, 100% {
    transform: scale(2);
    opacity: 0;
  }
}

.rotating {
  animation: rotate-animation 0.5s ease;
}

// Add smooth transition for the cropper
#cropper image-cropper {
  transition: transform 0.3s ease;
}

// Add responsive styles
@media (max-width: 768px) {
  .rotate-btn {
    width: 42px;
    height: 42px;
    
    ion-icon {
      font-size: 20px;
    }
  }
  
  .rotate-button-container {
    top: 15px;
    right: 15px;
  }
}


@media (prefers-color-scheme: dark) {
  .crop-doc-content {
    --background: rgba(255, 255, 255, 0.95);
  }
  ion-content {
    --background: #fff
  }
  ion-header ion-toolbar {
    --background: #fff !important;
  }
}

@media (prefers-color-scheme: light) {
  .crop-doc-content {
    --background: rgba(0, 0, 0, 0.05);
    --offset-top: 0px !important;
  }
  ion-content {
    --background: #fff
  }
  ion-header ion-toolbar {
    --background: #fff !important;
  }

}
