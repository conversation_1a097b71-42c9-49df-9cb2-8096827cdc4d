@import url("https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap");

:host {
  // display: block;
  height: 100dvh;
  
}

* {
  font-family: "Inter", sans-serif;
  font-optical-sizing: auto;
}

// ion-content::part(scroll) {
//   overflow-y: hidden !important;
//   --overflow: hidden !important;
// }
ion-content {
  --offset-top : 0px !important;
}

ion-header {
  height: 70px;
  --border: 0;
  display: flex;
  align-items: center;

}
ion-header ion-toolbar {
  --border: 0;
  --border-width: 0;
  display: flex;
  justify-content: center;
  align-items: flex-end;
  flex-direction: row;
  --background: #fff !important;
}

ion-header ion-toolbar ion-title {
  font-size: 26px;
  font-weight: 700;
  color: #2f4fcd;
  text-align: left;
  width: 100%;
  padding-left: 2rem;
}

ion-header ion-toolbar ion-icon {
  color: #101010;
  padding-right: 1rem;
}

.scan-bl-wrapper {
  
  // height: 100%;
  display: flex;
  flex-direction: column;
  // justify-content: center;
  align-items: center;
  margin-top: 2rem;
}

::ng-deep .scan-bl-wrapper .file-import-icon img {
  width: 150px !important;
  height: 150px !important;
}
::ng-deep .scan-bl-wrapper .arrow-bottom-icon {
  // margin-top: 50px;
  img {
    width: 100px !important;
    height: 100px !important;
  }
}

.scan-bl-wrapper .content {
  padding: 10px 50px 0 50px;
}
.scan-bl-wrapper .content h2 {
  color: #9a9a9a;
  font-size: 22px;
  font-weight: 700;
}

.scan-bl-wrapper .content p {
  color: #9a9a9a;
  font-size: 12px;
  text-align: center;
  padding: 5px 10px;
}

.scan-bl-content {
  --background: #ffffff;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.document-icon {
  font-size: 100px;
  color: #c4c4c4;
}

h2 {
  color: #555555;
  font-size: 18px;
  margin-top: 20px;
}

p {
  color: #888888;
  font-size: 14px;
  margin-top: 10px;
  margin-bottom: 30px;
}

.arrow-icon {
  font-size: 30px;
  color: #3b82f6; // Adjust the color as needed
}

ion-footer {
  background-color: #e5e7eb; // Adjust the background color as needed
}

.camera-button {
  --background: #3b82f6; // Adjust the color as needed
  --background-activated: #2563eb; // Adjust the color as needed
  border-radius: 50%;
  width: 60px;
  height: 60px;
  margin-top: -30px; // Position the button above the toolbar
}

ion-toolbar {
  --background: transparent;
  --ion-color-primary: #3b82f6; // Adjust the color as needed
}

ion-button {
  --color: #3b82f6; // Adjust the color as needed
}

ion-button[slot="icon-only"] {
  --color: #3b82f6; // Adjust the color as needed
}

// ion-icon {
//   font-size: 24px;
// }

ion-footer {
  position: relative;
  background-color: #dddbff;
  height: 100px;
  // border-top: 1px solid #2563eb;
  width: 100%;

  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: row;
}

ion-footer ion-toolbar {
  --border-width: 0;
}

ion-footer ion-toolbar ion-buttons,
ion-footer ion-toolbar ion-buttons ion-button {
  display: flex;
  justify-content: space-around;
  align-items: center;
  flex-direction: row;
}

::ng-deep ion-button.menu-button app-custom-icon img {
  width: 30px !important;
  height: 30px !important;
  color: #000;
}
::ng-deep .menu-button.active app-custom-icon img {
  color: #2f4fcd;
}

::ng-deep .menu-button-middle {
  background-color: #2f4fcd;
  padding: 2px 12px;
  border-radius: 14px;
  width: 85px;
  height: 60px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 15px;
  app-custom-icon img {
    width: 45px !important;
    height: 45px !important;
    color: #fff;
  }
}

ion-fab {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}

::ng-deep ion-fab-button.menu-button-middle::part(native) {
  background: none;
  border: 0;
  box-shadow: none;
  width: 100% !important;
  color: #fff;
}

::ng-deep ion-fab ion-fab-list {
  display: flex;
  flex-direction: row !important;
  justify-content: space-around;
  align-items: flex-end;
  width: auto !important;
  // padding: 10px 0;
  padding: 10px 20px;
  margin-bottom: 100px;
  height: 100vh;
  // transition: all 0.3s ease;
  --transition-duration: 300ms;
  &.fab-active {
    // background-color: rgba(0, 0, 0, 0.2);
    visibility: visible;
    opacity: 1;
    // transition: opacity 0.3s ease-in-out, visibility 0.3s ease-in-out;
    transform: translateY(0);
    transition: all var(--transition-duration) ease-in-out;
    pointer-events: all;
    
    ion-fab-button {
      transform: translateY(0);
      opacity: 1;
    }
  }

  &.fab-hidden {
    visibility: hidden;
    opacity: 0;
    transform: translateY(20px);
    transition: all var(--transition-duration) ease-in-out;
    pointer-events: none;
    
    ion-fab-button {
      transform: translateY(40px);
      opacity: 0;
    }
  }

  ion-fab-button {
    transition: all var(--transition-duration) ease-in-out;
  }
}



.content-fab-buttom {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}
::ng-deep .content-fab-buttom app-custom-icon img {
  width: 50px !important;
  height: 50px !important;
}
::ng-deep .content-fab-buttom ion-label {
  color: #2f4fcd;
  font-size: 20px;
}

::ng-deep ion-fab ion-fab-list ion-fab-button {
  padding: 10px;
  width: 190px;
  height: 135px;
  transform: translateY(40px);
  transition: all 0.6s ease-in-out;
  &.fab-active {
    transform: translateY(0);
  }
}
::ng-deep ion-fab ion-fab-list ion-fab-button::part(native) {
  border-radius: 16px !important;
  --background: #f4f5f8 !important;
}

/**
* Loading alert START
*/

::ng-deep .loading:not(.alert-progress) {
  opacity: 0.5;
  pointer-events: none; /* Makes the page unclickable */
  --background: rgba(0, 0, 0, 0.1);
}

// ion-modal
::ng-deep ion-modal:not(.guide-modal) {
  height: 45%;
  width: 100%;
  position: absolute;
  top: 15%;
  left: 0%;
  --background:none;
  --backdrop-opacity: var(--ion-backdrop-opacity, 0);
}

::ng-deep .alert-progress{
  position: absolute;
  width: 100%;
  top: 40%;
  // display: flex;
  justify-content: center;
  align-items: center;
}

::ng-deep .alert-progress app-custom-alert{
  width: 90%;
}

::ng-deep .alert-progress{
  display: none;
}
::ng-deep .alert-progress.loading{
  display: flex;
}


::ng-deep .btn_swal_custom {
  background: var(--ion-color-primary, #0054e9);
  padding: 15px 20px;
  color: #fff;
  text-decoration: none;
  font-weight: bold;
  border-radius: 10px;
}

::ng-deep div:where(.swal2-container) div:where(.swal2-footer) {
  border-top: 0px !important;
  margin: 1em 0 10px;
}

ion-content {
  --offset-top : 0px !important;
  // background: #fff;
  height: 72vh;
}

.div-content{

  background: url("/assets/bg-scan-bl.png") no-repeat center center fixed;
  background-size: cover;
}

::ng-deep .swal2-container {
  height: 100vh !important;
}

::ng-deep .swal2-html-container{
  padding: 1em 0.6em 0.3em !important;
  ul li{
    text-align: left;
    padding: 13px 10px 0 0px;
  }
}

.menu-button {
  &[disabled] {
    opacity: 0.7;
    pointer-events: none;
  }

  &.processing {
    pointer-events: none;
  }
}



.toggle-container {
  padding: 12px 16px;
  
  .toggle-wrapper {
    background: #e0e0e0;
    padding: 4px;
    border-radius: 100px;
    display: flex;
    width: 100%;
    max-width: 300px;
    margin: 0 auto;
    
    button {
      flex: 1;
      border: none;
      padding: 12px 24px;
      border-radius: 100px;
      font-size: 16px;
      font-weight: 500;
      background: transparent;
      color: #666;
      transition: all 0.3s ease;
      
      &.active {
        background: #837de9;
        color: #fff;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      }
      
      &:focus {
        outline: none;
      }
    }
  }
}



.scan-preview {
  margin: 1rem;
  img {
    width: 100%;
    max-width: 300px;
    margin: 0 auto;
    display: block;
  }
}

.custom-loading {
  --background: rgba(255, 255, 255, 0.95);
  --spinner-color: var(--ion-color-primary);
  
  .loading-content {
    font-size: 1.1em;
    font-weight: 500;
    color: var(--ion-color-dark);
  }
}

/**
* Loading alert  END
*/

@media (prefers-color-scheme: dark) {
  .bg-hide{
    --background: rgba(255,255,255,0.9);
  }
}

@media (prefers-color-scheme: light) {
  .bg-hide{
    --background: rgba(0,0,0,0.1);
  }
}



.instructions {
  margin-top: 20px;
  text-align: center;
}

.instruction-text {
  font-size: 18px;
  color: #433f3f;
  margin-bottom: 10px;
  font-weight: 600;
}

.follow-guide-btn {
  --background: #1b41d3;
  --color: #fff;
  font-size: 16px;
  padding: 10px 20px;
  border-radius: 2px;
  width: 100%;
}


.section-title {
  margin-top: 20px;
  margin-bottom: 0;
  color: #4b4b4b;
  font-size: 16px;
  margin-left: 16px;
  opacity: 0.5;
}

.doc-list-wrapper{
  width: 100%;
}

.doc-list{
  overflow-y: auto !important;
  --overflow: auto !important;
}

.page-count {
  font-size: 14px;
  color: #888;
  margin-left: auto;
  padding-right: 8px;
}

ion-item::part(native){
  border-style: auto !important;
  --border-style : auto !important;
}

ion-item-option{
  border-radius: 12px;
  width: 90px;
  margin-left: 5px;
}

ion-thumbnail {
  --border-radius: 2px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  height: 80px;
}

.document-card {
  margin: 14px;
  border-radius: 16px;
  // box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  box-shadow: none;
  border: 1px solid #e5ebfd;
  border-width: 0.55px;
  width: auto;
}

.renameCard{
  background-color: #cccbcb;
}

.confirmDeleteCard{
  background-color: #FFA5A5;
}

.content-item-option{
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  ion-icon{
    margin-bottom: 6px;
    font-size: 26px;
    font-weight: bold;
  }
  span{
    color: #fff;
    font-size: 13px;
    font-weight: 500;
  }
}
