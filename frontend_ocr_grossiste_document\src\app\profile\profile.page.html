<ion-header>
  <ion-toolbar>
    <ion-title style="margin-left:10px">Paramétres</ion-title>
    <ion-buttons slot="start">
      <ion-button (click)="goBack()">
        <ion-icon slot="icon-only" name="chevron-back-outline"></ion-icon>
      </ion-button>
    </ion-buttons>
    <ion-buttons slot="end">
      <ion-button (click)="logout()">
        <ion-icon slot="icon-only" name="log-out-outline"></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>


<ion-content>
  <div class="profile-container">
    <!-- Avatar and user info -->
    <ion-avatar class="profile-avatar">
      <img src="https://gravatar.com/avatar/placeholder?d=mp" alt="profile picture">
    </ion-avatar>
    
    <ion-text color="white" style="display: flex;flex-direction: column;align-items: center;">
      <h2 class="user-name">{{user?.firstname}} {{user?.lastname}}</h2>
      <!-- <p class="user-email">john.doeexample.com</p> -->
      <ion-badge color="light" >{{user?.mainAuthority === 'ROLE_PHARMACIEN' ? 'PHARMACIEN':'AIDE PHARMACIE'}}</ion-badge>
    </ion-text>

    <!-- List of actions -->
    <ion-list>
      <ion-item button detail (click)="onAbout()">
        <ion-icon slot="start" name="information-circle-outline"></ion-icon>
        <ion-label>À propos</ion-label>
      </ion-item>
      <ion-item button detail routerLink="/medicament-ocr">
        <ion-icon slot="start" name="medkit-outline"></ion-icon>
        <ion-label>Recherche de médicaments</ion-label>
      </ion-item>

      <ion-item button detail  (click)="onLogout()">
        <ion-icon slot="start" name="log-out-outline"></ion-icon>
        <ion-label>Déconnexion</ion-label>
      </ion-item>
    </ion-list>
  </div>
</ion-content>