import { Injectable, signal, Signal } from "@angular/core";
import { Tenant, User } from "../profile/user.model";
import { <PERSON>ert<PERSON>ontroller, NavController } from "@ionic/angular";
import { ApiService } from "./api.service";


@Injectable({
    providedIn: 'root'
  })
export class UserService {

    user  = signal<User| null>(null);
    tenant  = signal<Tenant| null>(null);
    private USER_TOKEN_KEY = 'tokenUser';
    private TENANT_TOKEN_KEY = 'tokenTenant';

    constructor(
        private alertController: AlertController,
        private apiService:ApiService,
        private navCtrl: NavController
    ) {
        this.loadAuth()
     }


     private loadAuth(){
         const currentUser = this.getCurrentUser();
         const currentTenant = this.getCurrentTenant();
         this.user.set(currentUser);
         this.tenant.set(currentTenant);
     }


    private getCurrentUser() {
        const currentUser = localStorage.getItem(this.USER_TOKEN_KEY);
        const parsedUser = this.parseUser<User>(currentUser!);
        this.user.set(parsedUser)
        return parsedUser;
    }

    private getCurrentTenant(){
        const tenant = localStorage.getItem(this.TENANT_TOKEN_KEY);
        const parsedUser = this.parseUser<Tenant>(tenant!);
        return parsedUser;
    }


    private parseUser<T>(user: string ) : T | null {
        try {
            return JSON.parse(user);
        } catch (e) {
            this.alertController.create({
                animated : true,
                header: 'une erreur est survenue',
                message: 'on ne peut pas récupérer les informations de l\'utilisateur, Merci de vous reconnecter',
                buttons: [
                {
                    text: 'OK',
                    handler: () => {
                        localStorage.removeItem('tokenUser');
                        localStorage.removeItem('tokenTenant');
                        localStorage.removeItem('token');
                        localStorage.removeItem('ocrMode');
                        localStorage.removeItem('forceSupplierGlobal');
                        localStorage.removeItem('selectedSupplier');
                        
                        this.navCtrl.navigateRoot('/login');
                    }
                }
                ]
            
            }).then(alert => alert.present());
            return null;
        }
    }
}