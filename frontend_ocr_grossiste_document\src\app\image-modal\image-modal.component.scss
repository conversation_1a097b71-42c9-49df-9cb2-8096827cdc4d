ion-toolbar{
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #2f4fcd;
  color: #fff;
  border-bottom-left-radius: 20px;
  border-bottom-right-radius: 20px;
  box-shadow: 0px 0px 10px 0px #000000;
}

.image-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 90%;
  }
  
  ion-header{
    display: flex;
    justify-content: center;
    align-items: center;
    height: 10%;
  }
  .enlarged-image {
    max-width: 100%;
    max-height: 100%;
  }
  
ion-content::part(scroll) {
  overflow-y: hidden !important;
  --overflow: hidden !important;
}
::ng-deep ion-content{
  --background : rgba(255, 255, 255, 0.8) !important;
}

  .footer-md, .header-md {
    -webkit-box-shadow: none;
    box-shadow: none;
  }

  ion-footer {
    position: relative;
    // bottom: -30px;
    width: 100%;
    padding: 10px;
  }
  
  ion-toolbar {
    --background: transparent;
    --ion-color-primary: #2f4fcd;
    --border-width: 0px !important;
  }

  ion-title{
    font-size: 1.8rem;
    color: #fff;
    letter-spacing: 2px;
    text-align: center;
  }
  
  .close-modal{
    position: absolute;
    right: -5px;
    top: -5px;
    --color: red;
    font-weight: bold;
    font-size: 1.8rem;
  }

  ion-button ion-icon{
    background: #fff !important;
    border-radius: 50% !important;
  }

  