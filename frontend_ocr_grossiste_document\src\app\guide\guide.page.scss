@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap');

* {
  font-family: 'Inter', sans-serif;
  font-optical-sizing: auto;
}

.footer-md, .header-md {
  -webkit-box-shadow: none;
  box-shadow: none;
}

// ion-content::part(scroll) {
//   overflow-y: hidden !important;
//   --overflow: hidden !important;
// }

.fix-wrapper{
  --ion-background-color: transparent;
  display: flex;
  flex-direction: column;
  background: url('/assets/onboarding_images/bg_onboarding.png') no-repeat;
  background-size: cover;
  // background-color: red;
  height: 100dvh;
}
.onboarding-wrapper {
  // background: url('/assets/onboarding_images/bg_onboarding.png') no-repeat center center fixed;
  // background-size: cover;
  // height: 100%;
  // height: 100dvh;
  // padding-block: 20px;
}

.slide-content {
  text-align: center;
  padding: 20px;
}

.slide-image {
  width: 60%;
  border-radius: 15px;
}



ion-footer {
  position: relative;
  // bottom: -30px;
  width: 100%;
  // padding: 10px;
}

ion-toolbar {
  --background: transparent;
  --ion-color-primary: #2f4fcd;
}

ion-button {
  margin: 5px;
}

ion-button.next-button {
  --background: #2f4fcd;
  --background-activated: #1e3aa8;
  --border-radius: 8px;
  --color: #fff;
  // width: 80%;
  width: 100%;
}

ion-button.skip-button {
  --color: #2f4fcd;
  width: 20%;
}

.pagination {
  display: flex;
  justify-content: center;
  margin: 20px 0;
  gap: 5px;
}

.pagination span {
  display: block;
  width: 10px;
  height: 10px;
  background-color: #ccc;
  border-radius: 50%;
}

.pagination .active {
  background-color: #2f4fcd;
}

.buttons_nav {
  display: flex;
  justify-content: space-between;
  // margin: 10px 0;
  gap: 5px;
  // padding: 0 10px;
}

/* Swiper Container */
swiper-container {
  height: 100%;
  --swiper-pagination-bullet-inactive-color: var(--ion-color-step-200, #cccccc);
  --swiper-pagination-color: var(--ion-color-primary, #2f4fcd);
  --swiper-pagination-progressbar-bg-color: rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.25);
  --swiper-scrollbar-bg-color: rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.1);
  --swiper-scrollbar-drag-bg-color: rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.5);
}

swiper-slide {
  display: flex;
  position: relative;
  flex-direction: column;
  flex-shrink: 0;
  align-items: center;
  justify-content: center;
  width: 100%;
  // height: 100%;
  font-size: 18px;
  text-align: center;
  box-sizing: border-box;
  height: calc(100vh - 60px);
  overflow-y: auto;
  scrollbar-width: none !important; 
  -ms-overflow-style: none !important;
 
}

swiper-slide ion-row {
  height: 100%;
  padding-bottom: 20px;
}

::ng-deep swiper-slide ion-row ion-col {
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}

::ng-deep swiper-slide ion-col {
  display: flex !important;
  flex-direction: column;
  justify-content: space-evenly;
  align-items: center;
}
 
::ng-deep swiper-slide img {
  width: auto;
  max-width: 100%;
  height: auto;
  max-height: 100%;
  // margin-bottom: 10%;
}

::ng-deep swiper-slide .content-slide {
  color: #2b2b2b;
  text-align: center;
  padding: 0 15px;
}

::ng-deep swiper-slide .content-slide h2 {
  font-size: 1.4rem;
  font-weight: bold;
  margin: 25px 0;
}

::ng-deep swiper-slide .content-slide p {
  // text-align: justify;
  letter-spacing: 0.9px;
  font-size: 16px;
  padding-right: 20px;
}

::ng-deep swiper-slide .img-details-col{
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: flex-start;
}

::ng-deep swiper-slide .img-details-col {
  .image-wrapper{
    margin: 0 20px;
  }
  img{
    margin: 0 20px;
    border-radius: 15px;
    width: 90%;
  }
  // img.img-details-3{
  //   width: 50%;
  // }

  // img.img-details-2{
  //   width: 50%;
  // }
}

.next-button {
  display: flex;
  justify-content: space-between;
  flex-direction: row;
  align-items: center;
}

.skip-button {
  font-size: 16px !important;
  font-weight: bold;
  opacity: 0.7;
}

swiper-container::part(bullet-active) {
  width: 70px !important;
  height: 5px !important;
  border-radius: 8px !important;
}
swiper-container::part(pagination) {
  top: var(--swiper-pagination-top, 97%) !important;
}




// -----------------------------------/

/* Global styles if necessary (src/global.scss) */
/* Global styles if necessary (src/global.scss) */
.img-details-content,
.img-details-row {
  position: relative;
}

.image-wrapper {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: 100%;
  flex-direction: column;
}

// except the first slide
.img-details-content .image-wrapper::before{
  content: attr(data-icon);
  position: absolute;
  width: 45px;
  height: 45px;
  top: 0;
  right: 20%;
  border-radius: 15px;
  font-size: 2rem;
  display: flex;
  justify-content: center;
  align-items: center;
}


.img-details-row .image-wrapper::before{
  content: attr(data-icon);
  position: absolute;
  width: 40px;
  height: 40px;
  top: 0;
  right: 10px;
  border-radius: 15px;
  font-size: 1.8rem;
  display: flex;
  justify-content: center;
  align-items: center;
}


::ng-deep swiper-slide:first-child {
  .content-slide {
    h2 {
      font-size: 1.6rem;
      background: linear-gradient(45deg, #2f4fcd, #1e3aa8);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      margin-bottom: 30px;
    }

    p {
      font-size: 18px;
      line-height: 1.6;
      color: #4a4a4a;
      max-width: 90%;
      margin: 0 auto;
    }
  }

  .image-wrapper {
    margin: 40px 0;
    
    img {
      width: 100%;
      transition: transform 0.3s ease;
      
      &:hover {
        transform: scale(1.02);
      }
    }
  }
}


// Remove icon CSS for the first slide
::ng-deep swiper-slide:first-child .image-wrapper::before {
  display: none !important;
}

// Add enlarge icon for other slides
::ng-deep swiper-slide:not(:first-child) .image-wrapper::after {
  content: "\2922";
  position: absolute;
  bottom: 20%;
  left: 70%;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.5);

  color: white;
  font-size: 20px;
}