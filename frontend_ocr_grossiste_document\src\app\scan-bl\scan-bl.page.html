<ion-header [ngClass]="{'loading': isLoading, 'bg-hide' : isFabOpen }">
  <ion-toolbar>
    <ion-title>Scanner votre BL</ion-title>
    <ion-buttons slot="end">
      <ion-button (click)="logout()">
        <ion-icon slot="icon-only" name="log-out-outline"></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<!-- <div class="toggle-container" style="visibility: hidden;">
  <div class="toggle-wrapper">
    <button 
      [class.active]="currentOcrMode === 'STANDARD'" 
      (click)="setOcrMode('STANDARD')" disabled>
      Basique
    </button>
    <button 
      [class.active]="currentOcrMode === 'MINDEE_ADVANCED'" 
      (click)="setOcrMode('MINDEE_ADVANCED')">
      Avancé
    </button>
  </div>
</div> -->

<ion-content
  class="scan-bl-content"
  [ngClass]="{'loading': isLoading,'bg-hide initial-bg' : isFabOpen }"
>
  <div class="div-content">
    <app-check-network></app-check-network>

    <div class="instructions">
      <p class="instruction-text" style="padding: 10px">
        Pour de meilleurs résultats, suivez les instructions ci-dessous.
      </p>

      <!-- Added button to follow the guide -->
      <ion-button [routerLink]="['/guide']" class="follow-guide-btn">
        Suivre les instructions
      </ion-button>
    </div>

    <div class="scan-bl-wrapper" *ngIf="signalDataIsEmpty">
      <app-custom-icon
        name="file-import"
        class="file-import-icon"
        (click)="triggerFabButtonClick()"
      ></app-custom-icon>
      <div class="content">
        <h2>Vous n'avez aucun document</h2>
        <p >
          Numérisez vos Bons de Livraison et retrouvez-les sur votre espace WinPlusPharma depuis n’importe quel appareil.
        </p>
      </div>

      <app-custom-icon
        name="arrow-bottom"
        class="arrow-bottom-icon"
      ></app-custom-icon>
    </div>

    <div *ngIf="!signalDataIsEmpty" class="doc-list-wrapper">
      <h2 class="section-title">Pages</h2>
      <div class="doc-list">
        <ion-item-sliding
          class="document-card"
          *ngFor="let slide of slidesData; let i = index"
        >
          <ion-item>
            <ion-thumbnail slot="start">
              <img [src]="slide.filtered_image" />
            </ion-thumbnail>
            <ion-label>
              <h3>{{ slide.title }}</h3>
              <p>{{ slide.date }}</p>
            </ion-label>
            <div class="page-count">Page {{ slide.page_index }}</div>
          </ion-item>
          <ion-item-options side="end">
            <ion-item-option
              (click)="confirmDeleteCard(i)"
              class="confirmDeleteCard"
            >
              <div class="content-item-option">
                <ion-icon slot="start" name="trash"></ion-icon>
                <span>Supprimer</span>
              </div>
            </ion-item-option>
          </ion-item-options>
        </ion-item-sliding>
      </div>
    </div>
    <!-- <app-custom-loading *ngIf="progress > 0" [progress]="progress"></app-custom-loading> -->
  </div>
</ion-content>

<div class="alert-progress" [ngClass]="{'loading': isLoading}">
  <app-custom-alert [progress]="progress"></app-custom-alert>
</div>

<ion-fab
  vertical="bottom"
  horizontal="center"
  slot="fixed"
  [ngClass]="{'loading': isLoading}"
>
  <ion-fab-button
    class="menu-button-middle"
    #fabButton
    id="fab-button"
    (click)="onFabToggle()"
    [disabled]="isAnimating"
  >
    <app-custom-icon name="camera-add"></app-custom-icon>
  </ion-fab-button>

  <ion-fab-list
    side="top"
    [class.fab-active]="isFabOpen"
    [class.fab-hidden]="!isFabOpen"
  >
    <ion-fab-button
      (click)="openCamera()"
      [disabled]="!isFabOpen"
      *ngIf="!isWeb"
    >
      <div class="content-fab-buttom">
        <app-custom-icon name="camera-scan"></app-custom-icon>
        <ion-label>Camera</ion-label>
      </div>
    </ion-fab-button>

    <ion-fab-button
      (click)="openCamera()"
      [disabled]="!isFabOpen"
      *ngIf="isWeb"
    >
      <div class="content-fab-buttom">
        <app-custom-icon name="scanner"></app-custom-icon>
        <ion-label>Scanner</ion-label>
      </div>
    </ion-fab-button>

    <ion-fab-button (click)="openGallery()" [disabled]="!isFabOpen">
      <div class="content-fab-buttom">
        <app-custom-icon name="gallery"></app-custom-icon>
        <ion-label>Galerie</ion-label>
      </div>
    </ion-fab-button>
  </ion-fab-list>
</ion-fab>

<ion-footer [ngClass]="{'loading': isLoading}">
  <ion-toolbar>
    <ion-buttons>
      <!-- <ion-button class="menu-button active" size="small" (click)="navToProcessDoc()">
        <app-custom-icon name="files"></app-custom-icon>
      </ion-button> -->
      <ion-button
        class="menu-button active"
        size="small"
        (click)="navToProcessDoc()"
        [disabled]="isProcessing"
      >
        <app-custom-icon name="files"></app-custom-icon>
      </ion-button>
      <ion-button class="menu-button" size="small" [routerLink]="['/profile']">
        <app-custom-icon name="settings"></app-custom-icon>
      </ion-button>

      <!-- <ion-button class="menu-button" size="small" [routerLink]="['/realtime-contours']">
        <app-custom-icon name="settings"></app-custom-icon>
      </ion-button> -->
    </ion-buttons>
  </ion-toolbar>
</ion-footer>
