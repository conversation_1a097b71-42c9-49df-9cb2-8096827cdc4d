"""
Database connection management for PostgreSQL.
This module handles database connections, session management, and initialization.
"""

import logging
from contextlib import contextmanager
from typing import Generator

from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.exc import SQLAlchemyError, OperationalError

from src.app.config import DATABASE_URL
from src.app.database.models import Base

logger = logging.getLogger(__name__)


class DatabaseManager:
    """
    Manages PostgreSQL database connections.
    Provides interface for database operations.
    """

    def __init__(self):
        self.engine = None
        self.session_factory = None
        self._initialize_database()

    def _initialize_database(self):
        """Initialize database connection and session factory."""
        try:
            # Initialize PostgreSQL
            self._initialize_postgresql()

            logger.info("PostgreSQL database connection initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize database: {e}")
            raise
    
    def _initialize_postgresql(self):
        """Initialize PostgreSQL connection."""
        try:
            # Create PostgreSQL engine with connection pooling
            self.engine = create_engine(
                DATABASE_URL,
                pool_size=10,
                max_overflow=20,
                pool_pre_ping=True,
                pool_recycle=3600,
                echo=False  # Set to True for SQL debugging
            )

            # Test connection
            with self.engine.connect() as conn:
                conn.execute(text("SELECT 1"))

            # Create session factory
            self.session_factory = sessionmaker(
                bind=self.engine,
                autocommit=False,
                autoflush=False
            )

            logger.info("PostgreSQL connection initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize PostgreSQL: {e}")
            raise
    

    
    def create_tables(self):
        """Create tables in PostgreSQL database."""
        try:
            Base.metadata.create_all(bind=self.engine)
            logger.info("PostgreSQL tables created successfully")
        except Exception as e:
            logger.error(f"Failed to create PostgreSQL tables: {e}")
            raise
    

    
    @contextmanager
    def get_session(self) -> Generator[Session, None, None]:
        """Get PostgreSQL session with automatic cleanup."""
        if not self.session_factory:
            raise RuntimeError("Database not available")

        session = self.session_factory()
        try:
            yield session
            session.commit()
        except Exception as e:
            session.rollback()
            logger.error(f"Database session error: {e}")
            raise
        finally:
            session.close()
    
    def health_check(self) -> dict:
        """Check the health of the PostgreSQL database connection."""
        status = {'available': False, 'error': None}

        if self.engine:
            try:
                with self.engine.connect() as conn:
                    conn.execute(text("SELECT 1"))
                status['available'] = True
            except Exception as e:
                status['error'] = str(e)

        return status


# Global database manager instance
db_manager = DatabaseManager()


def init_db():
    """Initialize database and create tables."""
    db_manager.create_tables()


# Context manager for easy session management
@contextmanager
def get_session() -> Generator[Session, None, None]:
    """Get database session."""
    with db_manager.get_session() as session:
        yield session
