import { Injectable } from '@angular/core';
import { HttpEvent, HttpInterceptor, HttpHandler, HttpRequest, HttpErrorResponse } from '@angular/common/http';
import { Observable, throwError, from } from 'rxjs';
import { catchError, switchMap } from 'rxjs/operators';
import { NavController } from '@ionic/angular';
import { NetworkService } from '../services/network.service';
import Swal from 'sweetalert2';

@Injectable()
export class HttpErrorInterceptor implements HttpInterceptor {

  constructor(private navCtrl: NavController, private networkService: NetworkService) {}

  intercept(request: HttpRequest<any>, next: <PERSON>ttpHand<PERSON>): Observable<HttpEvent<any>> {
    return from(this.networkService.isConnected()).pipe(
      switchMap(isConnected => {
        if (!isConnected) {
          // Redirect to network error page if no internet connection
          this.navCtrl.navigateRoot('/network-error');
          // Return an empty observable since we don't want to proceed with the request
          return throwError({ status: 0, message: 'No Internet Connection' });
        }
        // Proceed with the request
        return next.handle(request).pipe(
          catchError((error: HttpErrorResponse) => {
            if (error.status === 401) {
              // Redirect to login page if unauthorized

              Swal.fire({
                title: 'Non Autorisé',
                text: 'Vous devez vous connecter pour accéder à cette page.',
                icon: 'error',
                confirmButtonText: 'Login'
              }).then(() => {
                this.navCtrl.navigateRoot('/login');
              });
            } 
            // this.navCtrl.navigateRoot('/request-error');
            return throwError(error);
          })
        );
      })
    );
  }
}