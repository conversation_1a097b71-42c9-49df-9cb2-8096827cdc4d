#!/usr/bin/env python3
"""
Production Database Initialization Script
This script initializes the PostgreSQL database for production with the latest schema including src_app column.

Usage:
    python scripts/init_production_db.py [--env-file .env.prod]

Options:
    --env-file   Environment file to load (default: .env.prod)

Environment Variables Required:
    POSTGRES_HOST - PostgreSQL server host
    POSTGRES_PORT - PostgreSQL server port
    POSTGRES_DB - Database name
    POSTGRES_USER - Database user
    POSTGRES_PASSWORD - Database password
"""

import sys
import os
import argparse
import logging
from pathlib import Path
from dotenv import load_dotenv

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.app.database.connection import db_manager
from src.app.database.models import Base
from sqlalchemy import text

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def check_environment():
    """Check if all required environment variables are set."""
    required_vars = [
        'POSTGRES_HOST',
        'POSTGRES_PORT', 
        'POSTGRES_DB',
        'POSTGRES_USER',
        'POSTGRES_PASSWORD'
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        logger.error(f"❌ Missing environment variables: {', '.join(missing_vars)}")
        logger.error("💡 Make sure to set these in your .env.prod file")
        return False
    
    logger.info("✅ All required environment variables are set")
    return True


def test_connection():
    """Test PostgreSQL database connection."""
    try:
        health = db_manager.health_check()
        if health['postgresql']['available']:
            logger.info("✅ PostgreSQL connection successful")
            return True
        else:
            logger.error(f"❌ PostgreSQL connection failed: {health['postgresql']['error']}")
            return False
    except Exception as e:
        logger.error(f"❌ Database connection test failed: {e}")
        return False


def create_database_tables():
    """Create all database tables with latest schema."""
    try:
        logger.info("Creating PostgreSQL database tables...")
        
        # Create tables using SQLAlchemy (includes src_app column)
        if db_manager.postgres_engine:
            Base.metadata.create_all(bind=db_manager.postgres_engine)
            logger.info("✅ PostgreSQL tables created successfully")
            
            # List created tables
            with db_manager.postgres_engine.connect() as conn:
                result = conn.execute(text("""
                    SELECT table_name
                    FROM information_schema.tables
                    WHERE table_schema = 'public'
                    ORDER BY table_name
                """))
                tables = [row[0] for row in result]
                logger.info(f"Created tables: {', '.join(tables)}")
            
            return True
        else:
            logger.error("❌ PostgreSQL engine not available")
            return False
            
    except Exception as e:
        logger.error(f"❌ Failed to create database tables: {e}")
        return False


def verify_schema():
    """Verify that the database schema includes src_app column."""
    try:
        logger.info("Verifying database schema...")
        
        with db_manager.postgres_engine.connect() as conn:
            # Check pre_bl_ocr table structure
            result = conn.execute(text("""
                SELECT column_name, data_type, column_default, is_nullable
                FROM information_schema.columns 
                WHERE table_name = 'pre_bl_ocr'
                ORDER BY ordinal_position
            """))
            columns = result.fetchall()
            
            logger.info("✅ pre_bl_ocr table structure:")
            for col in columns:
                logger.info(f"  - {col[0]} ({col[1]}) default: {col[2]} nullable: {col[3]}")
            
            # Specifically check for src_app column
            src_app_column = next((col for col in columns if col[0] == 'src_app'), None)
            if src_app_column:
                logger.info("✅ src_app column found in schema")
                return True
            else:
                logger.error("❌ src_app column missing from schema")
                return False
            
    except Exception as e:
        logger.error(f"❌ Error verifying schema: {e}")
        return False


def create_indexes():
    """Create database indexes for better performance."""
    try:
        logger.info("Creating database indexes...")
        
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_pre_bl_ocr_user_id ON pre_bl_ocr(ID_USER);",
            "CREATE INDEX IF NOT EXISTS idx_pre_bl_ocr_tenant_id ON pre_bl_ocr(ID_TENANT);", 
            "CREATE INDEX IF NOT EXISTS idx_pre_bl_ocr_random_id ON pre_bl_ocr(random_id);",
            "CREATE INDEX IF NOT EXISTS idx_pre_bl_ocr_src_app ON pre_bl_ocr(src_app);",
            "CREATE INDEX IF NOT EXISTS idx_pre_bl_ocr_status ON pre_bl_ocr(status);",
            "CREATE INDEX IF NOT EXISTS idx_pre_bl_ocr_date ON pre_bl_ocr(date);"
        ]
        
        with db_manager.postgres_engine.connect() as conn:
            for index_sql in indexes:
                conn.execute(text(index_sql))
            conn.commit()
        
        logger.info("✅ Database indexes created successfully")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error creating indexes: {e}")
        return False


def main():
    """Main initialization function."""
    parser = argparse.ArgumentParser(description='Initialize PostgreSQL database for production')
    parser.add_argument('--env-file', default='.env.prod', help='Environment file to load (default: .env.prod)')
    args = parser.parse_args()

    # Load environment file
    env_file_path = project_root / args.env_file
    if env_file_path.exists():
        load_dotenv(env_file_path)
        logger.info(f"✅ Loaded environment from {args.env_file}")
    else:
        logger.error(f"❌ Environment file not found: {args.env_file}")
        sys.exit(1)

    logger.info("🚀 Starting Production PostgreSQL database initialization...")

    # Check environment variables
    if not check_environment():
        sys.exit(1)
    
    # Test connection
    if not test_connection():
        sys.exit(1)
    
    # Create tables
    if not create_database_tables():
        sys.exit(1)
    
    # Verify schema
    if not verify_schema():
        sys.exit(1)
    
    # Create indexes
    if not create_indexes():
        sys.exit(1)
    
    logger.info("🎉 Production database initialization completed successfully!")
    logger.info("✅ Database is ready with src_app column support")
    logger.info("💡 You can now deploy your application to production")


if __name__ == "__main__":
    main()
