# Pre-Production Environment Setup - Frontend

This document explains how to build and serve the OCR Document Grossiste frontend application in pre-production mode.

## 🌍 **Available Environments**

### 1. **Development** (`development`)
- **Environment File**: `src/environments/environment.ts`
- **Purpose**: Local development
- **API URL**: Local development server

### 2. **Pre-Production Mobile** (`preprod`)
- **Environment File**: `src/environments/environment.preprod.ts`
- **Purpose**: Testing mobile app before production deployment
- **API URL**: `https://winproduit.sophatel.com:8000` (Port 8000)

### 3. **Pre-Production Web** (`webpreprod`)
- **Environment File**: `src/environments/environment.web.preprod.ts`
- **Purpose**: Testing web app before production deployment
- **API URL**: `https://winproduit.sophatel.com:8000` (Port 8000)

### 4. **Production Mobile** (`production`)
- **Environment File**: `src/environments/environment.prod.ts`
- **Purpose**: Live production mobile app
- **API URL**: Production server

### 5. **Production Web** (`webproduction`)
- **Environment File**: `src/environments/environment.web.prod.ts`
- **Purpose**: Live production web app
- **API URL**: Production server

## 🚀 **Building the Application**

### **Pre-Production Builds**

```bash
# Build mobile app for pre-production
npm run build:preprod

# Build web app for pre-production
npm run build:webpreprod
```

### **Development Builds**

```bash
# Standard development build
npm run build

# Development build with watch mode
npm run watch
```

### **Production Builds**

```bash
# Build mobile app for production
ng build --configuration production

# Build web app for production
ng build --configuration webproduction
```

## 🔧 **Serving the Application**

### **Pre-Production Serve**

```bash
# Serve mobile app in pre-production mode
npm run serve:preprod

# Serve web app in pre-production mode
npm run serve:webpreprod
```

### **Development Serve**

```bash
# Standard development serve
npm start
# or
ng serve
```

## 📋 **Environment Configuration**

### **Pre-Production Environment Variables**

Both preprod environment files (`environment.preprod.ts` and `environment.web.preprod.ts`) are configured to:

- **API URL**: `https://winproduit.sophatel.com:8000`
- **WebSocket URL**: `wss://winproduit.sophatel.com:8000/ws`
- **Real-time WebSocket**: `wss://winproduit.sophatel.com:8000/websocket/realtime_processing`
- **Production Mode**: `false` (enables debugging)
- **Platform**: `mobile` or `web` respectively

## 🔄 **Angular Build Configurations**

The `angular.json` file includes the following build configurations:

- `preprod`: Mobile pre-production build
- `webpreprod`: Web pre-production build
- `production`: Mobile production build
- `webproduction`: Web production build

## 📱 **Platform-Specific Builds**

### **Mobile Platform**
- Uses `environment.preprod.ts` for preprod
- Uses `environment.prod.ts` for production
- Platform setting: `'mobile'`

### **Web Platform**
- Uses `environment.web.preprod.ts` for preprod
- Uses `environment.web.prod.ts` for production
- Platform setting: `'web'`

## 🔍 **Troubleshooting**

### **Environment Not Loading**
1. Check that the correct environment file exists
2. Verify the build configuration in `angular.json`
3. Ensure the API server is running on the correct port (8000 for preprod)

### **API Connection Issues**
1. Verify the API server is running: `https://winproduit.sophatel.com:8000`
2. Check network connectivity
3. Verify SSL certificates if using HTTPS

## 📝 **Next Steps**

1. **Test the preprod build**: `npm run build:preprod`
2. **Serve the preprod app**: `npm run serve:preprod`
3. **Verify API connectivity**: Check browser console for connection errors
4. **Test all features**: Ensure all functionality works with the preprod API

## 🔗 **Related Documentation**

- Backend preprod setup: See `../ENVIRONMENT_SETUP.md`
- API documentation: Check the backend API docs
- Deployment guide: See deployment documentation
