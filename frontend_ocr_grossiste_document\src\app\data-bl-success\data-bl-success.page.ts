import {
  Component,
  ViewChild,
  AfterViewInit,
  ElementRef,
  OnInit,
  inject,
} from '@angular/core';
import {
  NavController,
  LoadingController,
  AlertController,
  IonInput,
} from '@ionic/angular';
import { SignalService } from '../services/signal.service';
import { TransformedDocData } from 'src/models/TransformedDocData';
import { ApiService } from '../services/api.service';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-data-bl-success',
  templateUrl: './data-bl-success.page.html',
  styleUrls: ['./data-bl-success.page.scss'],
})
export class DataBlSuccessPage implements OnInit {
  slidesData: TransformedDocData[] = [];
  navCtrl = inject(NavController);
  signalService = inject(SignalService);
  loadingController = inject(LoadingController);
  alertController = inject(AlertController);
  BL_id?: string;
  supplier_name?: string = '';
  apiService = inject(ApiService);
  id_BL_origine?: string;
  date_BL_origine?: string = '';

  @ViewChild('idBlInput') idBlInput!: IonInput;

  constructor() {}

  ngOnInit() {
    this.BL_id = history.state.BL_id;
    this.supplier_name = history.state.supplier_name;
    console.log('BL_id:', this.BL_id);

    this.slidesData = this.signalService.getTransformedData();
    if (this.slidesData.length === 0) {
      this.navCtrl.navigateBack('/scan-bl');
    }
    console.log('Received data:', this.slidesData);

    localStorage.removeItem('forceSupplierGlobal');
  }

  focusInput() {
    if (this.idBlInput) {
      setTimeout(() => {
        this.idBlInput.setFocus();
      }, 150);
    }
  }

  onInputClick(event: Event) {
    event.preventDefault();
    const input = event.target as HTMLInputElement;
    input.focus();
  }


  async IgnorerBL(){
    Swal.fire({
      icon: 'warning',
      title: "vous allez ignorer le BL",
      html: 'Veuillez confirmer ?',
      footer: '<span class="btn_swal_custom">Scanner un autre BL</span>',
      showConfirmButton: false, // Remove the confirm button
      showCloseButton: true, // Add a close button
      customClass: {
        closeButton: 'custom-close-button', // Custom class for the close button
        popup: 'custom-popup', // Custom class for the popup for additional styling if needed
        footer: 'custom-footer', // Custom class for the footer
      },
      didOpen: () => {
        const footerLink =
          Swal.getFooter()?.querySelector('.btn_swal_custom');
        if (footerLink) {
          footerLink.addEventListener('click', () => {
            Swal.close();
            this.signalService.removeAllData();
            localStorage.removeItem('selectedSupplier');
            this.navCtrl.navigateRoot('/scan-bl');
          });
        }
      },
    });
  }


  async sendBL() {
    if (this.BL_id) {
      if (this.id_BL_origine) {
        try {
          await this.apiService
            .updateBLStatus(
              this.BL_id,
              'EN_COURS',
              this.id_BL_origine,
              this.date_BL_origine ?? '',
              this.supplier_name ?? ''
            )
            .toPromise();
          // Show success message
          Swal.fire({
            icon: 'success',
            title: 'Le BL a été envoyé avec succès',
            html: 'Vous pouvez retrouver les résultats du BL dans votre espace <b>WinPlusPharma</b> :<br><p><small>Menu → Achats → Réception → Import BL → Importer BLs Scannés</small></p>',
            footer: "<span class='btn_swal_custom'>D'accord</span>",
            showConfirmButton: false,
            showCloseButton: true,
            customClass: {
              closeButton: 'custom-close-button',
              popup: 'custom-popup',
              footer: 'custom-footer',
            },
            didOpen: () => {
              const footerLink = Swal.getFooter()?.querySelector('.btn_swal_custom');
              if (footerLink) {
                footerLink.addEventListener('click', () => {
                  Swal.close();
                  this.navCtrl.navigateRoot('/scan-bl');
                });
              }
            },
          });
          // Navigate to scan-bl page and remove all data
          this.signalService.removeAllData();
          localStorage.removeItem('selectedSupplier');
          this.navCtrl.navigateRoot('/scan-bl');
        } catch (error) {
          console.error('Error sending BL:', error);
          // Show error message
          Swal.fire({
            icon: 'error',
            title: "Erreur lors de l'envoi du BL",
            html: 'Veuillez réessayer plus tard.',
            footer:
              '<a href="/guide">Comment capturer une image de qualité ?</a>',
            showConfirmButton: false, // Remove the confirm button
            showCloseButton: true, // Add a close button
            customClass: {
              closeButton: 'custom-close-button', // Custom class for the close button
              popup: 'custom-popup', // Custom class for the popup for additional styling if needed
              footer: 'custom-footer', // Custom class for the footer
            },
          });
        }
      } else {
        // Show error message of the BL ID Field Required
        Swal.fire({
          icon: 'error',
          title: 'Erreur de validation du BL !',
          html: 'Veuillez renseigner le BL',
          footer: '<span class="btn_swal_custom">Ressayer</span>',
          showConfirmButton: false, // Remove the confirm button
          showCloseButton: true, // Add a close button
          customClass: {
            closeButton: 'custom-close-button', // Custom class for the close button
            popup: 'custom-popup', // Custom class for the popup for additional styling if needed
            footer: 'custom-footer', // Custom class for the footer
          },
          didOpen: () => {
            const footerLink =
              Swal.getFooter()?.querySelector('.btn_swal_custom');
            if (footerLink) {
              footerLink.addEventListener('click', () => {
                Swal.close();
              });
            }
          },
        });
      }
    }
  }

  async NewBL() {
    const alert = await this.alertController.create({
      header: 'Voulez-vous créer un nouveau document ?',
      buttons: [
        {
          text: 'Annuler',
          role: 'cancel',
          cssClass: 'custom-alert-button-rename-doc cancel',
          handler: () => {
            console.log('Confirm Cancel');
          },
        },
        {
          text: 'Nouveau BL',
          cssClass: 'custom-alert-button-rename-doc rename',
          handler: () => {
            this.signalService.removeAllData();
            localStorage.removeItem('selectedSupplier');
            this.navCtrl.navigateForward('/scan-bl');
          },
        },
      ],
    });

    await alert.present();
  }

  async EditCurrentBL() {
    const alert = await this.alertController.create({
      header: 'Voulez-vous vraiment modifier les pages de ce document ?',
      buttons: [
        {
          text: 'Annuler',
          role: 'cancel',
          cssClass: 'custom-alert-button-rename-doc cancel',
          handler: () => {
            console.log('Confirm Cancel');
          },
        },
        {
          text: 'Modifier',
          cssClass: 'custom-alert-button-rename-doc rename',
          handler: () => {
            this.navCtrl.navigateRoot('/process-doc');
          },
        },
      ],
    });

    await alert.present();
  }

  async logout() {
    await this.apiService.logout();  // Wait for the confirmation dialog
    this.navCtrl.navigateRoot('/login');  // Then navigate to login
  }
}
