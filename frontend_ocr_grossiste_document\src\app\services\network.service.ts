import { Injectable } from '@angular/core';
import { Network } from '@capacitor/network';
import { BehaviorSubject } from 'rxjs';
import { merge, fromEvent, map, Observable, Observer } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class NetworkService {
  private networkStatus = new BehaviorSubject<boolean>(true);

  constructor() {
    this.initializeNetworkListener();
  }

  private async initializeNetworkListener() {
    const status = await Network.getStatus();
    this.networkStatus.next(status.connected);
    console.log('Initial Network Status:', status.connected);

    Network.addListener('networkStatusChange', (status) => {
      console.log('Network status changed:', status.connected);
      this.networkStatus.next(status.connected);
    });
  }

  public getNetworkStatus() {
    return this.networkStatus.asObservable();
  }

  public async isConnected(): Promise<boolean> {
    const status = await Network.getStatus();
    return status.connected;
  }
  
  isOnline$ = merge(
    fromEvent(window, 'online').pipe(map(() => true)),
    fromEvent(window, 'offline').pipe(map(() => false)),
    new Observable((sub: Observer<boolean>) => {
      sub.next(navigator.onLine);
      sub.complete();
    }),
  );
}
