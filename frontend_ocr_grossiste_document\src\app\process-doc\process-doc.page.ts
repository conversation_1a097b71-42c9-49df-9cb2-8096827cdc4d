import { Location } from '@angular/common';
import {
  Component,
  ElementRef,
  OnInit,
  ViewChild,
  inject,
} from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { NavController } from '@ionic/angular';
import { SignalService } from '../services/signal.service';
import { ProcessDocData } from 'src/models/ProcessDocData';
import { AlertController } from '@ionic/angular';
import { ChangeDetectorRef } from '@angular/core';
import { environment } from 'src/environments/environment';
import { ApiService } from '../services/api.service';

@Component({
  selector: 'app-process-doc',
  templateUrl: './process-doc.page.html',
  styleUrls: ['./process-doc.page.scss'],
})
export class ProcessDocPage implements OnInit {
  @ViewChild('swiper_docs', { static: true }) swiper_docs:
    | ElementRef
    | undefined;

  data: any;
  isScanning: boolean = false;
  hideAnimation: boolean = false;
  selectedSupplier: string = '';

  croppedImage: string = '';
  filteredImage: string = '';

  forceSupplierGlobal: boolean = false; // force the supplier to be global for apply the Advanced OCR 'Mindee'
  filteredImageLoaded: boolean = false;
  suppliers: any[] = []; // Initialize suppliers as an empty array

  listDocs: ProcessDocData[] = [];

  navCtrl = inject(NavController);
  signalService = inject(SignalService);

  swiperClass: string =
    environment.platform === 'web' ? 'swiper-small' : 'swiper-large';

  constructor(
    private route: ActivatedRoute,
    private location: Location,
    private alertController: AlertController,
    private cd: ChangeDetectorRef,
    private apiService: ApiService
  ) {}

  async ngOnInit() {
    await this.redirectToScanBL();
    
    // if the value is equal 'GLOBAL' then change it to 'AUTRE'
    this.suppliers.forEach((supplier) => {
      if (supplier.value === 'GLOBAL') {
        supplier.value = 'AUTRE';
      }
    });
    const params = this.location.getState() as any;
    this.data = params;
    console.log('Received data:', this.data);

    setTimeout(async () => {
      await this.getAllSuppliers();
    }, 200);

    this.listDocs = this.signalService.getData();
    this.selectedSupplier = localStorage.getItem('selectedSupplier') ?? '';

    this.cd.detectChanges(); // Add this line

    // Check if it's the first entry
    if (this.signalService.firstEntry) {
      if (this.data.supplier_name && this.data.supplier_name != 'Unknown') {
        this.selectedSupplier = this.data.supplier_name.toUpperCase();
        localStorage.setItem('selectedSupplier', this.selectedSupplier);
      } else if (
        this.selectedSupplier == '' ||
        !this.selectedSupplier ||
        this.selectedSupplier == 'UNKNOWN'
      ) {
        // conseil the user to take a other photo if the supplier is unknown
        const alert = await this.alertController.create({
          header: 'Attention !',
          message: `Le fournisseur n'a pas été reconnu !. Veuillez prendre une autre photo.`,
          buttons: [
            {
              text: 'Non',
              role: 'cancel',
              cssClass: 'custom-alert-button cancel',
              handler: () => {
                this.selectedSupplier =
                  localStorage.getItem('selectedSupplier') ?? '';
                console.log('Confirm Cancel');
              },
            },
            {
              text: 'Oui',
              cssClass: 'custom-alert-button danger',
              handler: () => {
                const index_currect =
                  this.swiper_docs?.nativeElement.swiper?.activeIndex;
                this.swiper_docs?.nativeElement.swiper?.removeSlide(
                  index_currect
                );
                this.signalService.removeData(index_currect);
                this.listDocs = this.signalService.getData();
                this.navCtrl.navigateRoot('/scan-bl');
              },
            },
          ],
        });

        // force the supplier to be global for apply the Advanced OCR 'Mindee'
        this.forceSupplierGlobal = true;

        // set it in the local storage
        localStorage.setItem(
          'forceSupplierGlobal',
          this.forceSupplierGlobal.toString()
        );

        await alert.present();
      } else {
        this.selectedSupplier = '';
      }
    } else {
      // Check if the supplier came from the backend not the same that the user selected
      if (
        this.data.supplier_name &&
        this.data.supplier_name != 'Unknown' &&
        this.data.supplier_name.toUpperCase() !=
          this.selectedSupplier.toUpperCase() &&
        this.listDocs.length > 0
      ) {
        // alert prompt for attention the user that if change supplier value for this document then all documents will be updated
        const alert = await this.alertController.create({
          header: 'Attention !',
          message: '',
          buttons: [
            {
              text: 'Annuler',
              role: 'cancel',
              cssClass: 'custom-alert-button cancel',
              handler: () => {
                console.log('Confirm Cancel');
                // set the selected supplier to the previous value
                this.selectedSupplier =
                  localStorage.getItem('selectedSupplier') ?? '';
              },
            },
            {
              text: 'Oui, Mettre à jour !',
              cssClass: 'custom-alert-button warning',
              handler: () => {
                console.log(
                  'Selected Supplier backend:',
                  this.data.supplier_name
                );
                console.log('Selected Supplier select:', this.selectedSupplier);
                this.selectedSupplier = this.data.supplier_name.toUpperCase();
                localStorage.setItem(
                  'selectedSupplier',
                  this.data.supplier_name.toUpperCase()
                );
                console.log('Slide data:', this.data);
              },
            },
          ],
        });
        await alert.present();

        // Use a query selector to set the innerHTML of the message
        const alertMessage = document.querySelector('.alert-message');
        if (alertMessage) {
          alertMessage.innerHTML = `Le nom du fournisseur <b>'${
            this.selectedSupplier
          }'</b> ne correspond pas au modèle attendu <b>'${this.data.supplier_name.toUpperCase()}'</b>. Voulez-vous mettre à jour le fournisseur de ce document ?`;
        }
      }
    }

    setTimeout(() => {
      this.applyFilter();

      if (localStorage.getItem('selectedSupplier') != null) {
        console.log(
          'Selected Supplier:',
          localStorage.getItem('selectedSupplier')
        );
        this.selectedSupplier = localStorage.getItem('selectedSupplier') ?? '';
      }
    }, 1000);
  }

  async getAllSuppliers() {
    // Fetch suppliers from the API
    this.apiService.getAllSuppliers().subscribe(
      (suppliers) => {
        this.suppliers = suppliers.map((supplier: any) => {
          if (supplier.value === 'GLOBAL') {
            return { value: 'AUTRE', label: 'AUTRE' };
          }

          // Replace underscores with spaces in supplier.value
          if (supplier.value.includes('_')) {
            return {
              ...supplier,
              value: supplier.value,
              label: supplier.label.replace(/_/g, ' '),
            };
          }

          return supplier;
        });

        // Move "AUTRE" to the end
        this.suppliers = this.suppliers.sort((a, b) => (a.value === 'AUTRE' ? 1 : b.value === 'AUTRE' ? -1 : 0));
      },
      (error) => {
        console.error('Error fetching suppliers:', error);
      }
    );
}



  async redirectToScanBL() {
    if (
      this.signalService.getData() == null ||
      this.signalService.getData() == undefined ||
      this.signalService.getData().length == 0
    ) {
      this.navCtrl.navigateRoot('/scan-bl');
    }
  }

  applyFilter() {
    this.isScanning = !this.isScanning;
    if (this.isScanning) {
      setTimeout(() => {
        this.hideAnimation = true;
      }, 2000);
      this.hideAnimation = false;
    }
  }

  async isValidBlobUrl(blobUrl: string): Promise<boolean> {
    try {
      const response = await fetch(blobUrl);
      return response.ok; // Returns true if the response is ok (status is in the range 200-299)
    } catch (error) {
      return false; // Returns false if there is an error (e.g., network issue, invalid URL)
    }
  }

  onSlideChange() {
    console.log('changedslidechange');
    this.initSwipers();
  }
  initSwipers() {
    if (this.swiper_docs && this.swiper_docs.nativeElement) {
      const swiper_docsInstance = this.swiper_docs.nativeElement.swiper;
    }
  }

  async removeDoc(index: number) {
    const alert = await this.alertController.create({
      header: 'Supprimer le document',
      message: `Êtes-vous sûr de vouloir supprimer ce document ?`,
      buttons: [
        {
          text: 'Annuler',
          role: 'cancel',
          cssClass: 'custom-alert-button cancel',
          handler: () => {
            console.log('Confirm Cancel');
          },
        },
        {
          text: 'Oui, Supprimer !',
          cssClass: 'custom-alert-button danger',
          handler: () => {
            this.signalService.removeData(index);
            this.listDocs = this.signalService.getData();
            this.swiper_docs?.nativeElement.swiper?.removeSlide(index);
            if (this.signalService.getData().length == 0) {
              localStorage.removeItem('selectedSupplier');
              this.navCtrl.navigateRoot('/scan-bl');
            }
          },
        },
      ],
    });

    await alert.present();
  }

  async removeAllDoc() {
    const alert = await this.alertController.create({
      header: 'Supprimer le document',
      message: `Êtes-vous sûr de vouloir supprimer Tous les documents ?`,
      buttons: [
        {
          text: 'Annuler',
          role: 'cancel',
          cssClass: 'custom-alert-button cancel',
          handler: () => {
            console.log('Confirm Cancel');
          },
        },
        {
          text: 'Oui, Supprimer !',
          cssClass: 'custom-alert-button danger',
          handler: () => {
            this.signalService.removeAllData();
            this.listDocs = [];
            this.swiper_docs?.nativeElement.swiper?.removeAllSlides();
            localStorage.removeItem('selectedSupplier');
            // redirect to scan-bl
            this.navCtrl.navigateRoot('/scan-bl');
          },
        },
      ],
    });

    await alert.present();
  }

  async reTakePhoto() {
    const alert = await this.alertController.create({
      header: 'Supprimer le document',
      message: `Êtes-vous sûr de vouloir prends une nouvelle photo ?`,
      buttons: [
        {
          text: 'Annuler',
          role: 'cancel',
          cssClass: 'custom-alert-button cancel',
          handler: () => {
            console.log('Confirm Cancel');
          },
        },
        {
          text: 'Oui',
          cssClass: 'custom-alert-button',
          handler: () => {
            const index_currect =
              this.swiper_docs?.nativeElement.swiper?.activeIndex;
            // remove currect slide
            this.swiper_docs?.nativeElement.swiper?.removeSlide(index_currect);
            // remove data
            this.signalService.removeData(index_currect);
            this.listDocs = this.signalService.getData();
            // redirect to scan-bl
            this.navCtrl.navigateRoot('/scan-bl');
          },
        },
      ],
    });

    await alert.present();
  }

  async onSupplierChange($event: any) {
    console.log('Selected Supplier:', $event.detail.value);
    if ($event.detail.value != localStorage.getItem('selectedSupplier')) {
      // alert prompt for attention the user that if change supplier value for this document then all documents will be updated
      const alert = await this.alertController.create({
        header: 'Attention !',
        message: `Êtes-vous sûr de vouloir changer le fournisseur ? Tous les documents seront mis à jour avec le nouveau fournisseur.`,
        buttons: [
          {
            text: 'Annuler',
            role: 'cancel',
            cssClass: 'custom-alert-button cancel',
            handler: () => {
              console.log('Confirm Cancel');
              // set the selected supplier to the previous value
              this.selectedSupplier =
                localStorage.getItem('selectedSupplier') ?? '';
            },
          },
          {
            text: 'Oui, Changer !',
            cssClass: 'custom-alert-button warning',
            handler: () => {
              this.selectedSupplier = $event.detail.value;
              this.listDocs.forEach((doc) => {
                doc.supplier_name = $event.detail.value;
              });
              console.log('Slide data:', this.listDocs);
              localStorage.setItem('selectedSupplier', $event.detail.value == 'AUTRE' ? 'GLOBAL' : $event.detail.value);
              console.log('Selected Supplier:', $event.detail.value);
            },
          },
        ],
      });

      await alert.present();
    }
  }

  async valider() {
    // check if selectedSupplier not empty then redirect to  [routerLink]="['/doc-list']" else show alert message
    if (this.selectedSupplier != '') {
      this.navCtrl.navigateRoot('/doc-list');
    } else {
      const alert = await this.alertController.create({
        header: 'Attention !',
        message: `Veuillez sélectionner un fournisseur`,
        buttons: ['OK'],
      });
      await alert.present();
    }
  }

  compressImage(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (event) => {
        const img = new Image();
        img.src = event.target?.result as string;
        img.onload = () => {
          const canvas = document.createElement('canvas');
          const ctx = canvas.getContext('2d');
          const MAX_WIDTH = 1024;
          const MAX_HEIGHT = 1024;
          let width = img.width;
          let height = img.height;

          if (width > height && width > MAX_WIDTH) {
            height = (height * MAX_WIDTH) / width;
            width = MAX_WIDTH;
          } else if (height > width && height > MAX_HEIGHT) {
            width = (width * MAX_HEIGHT) / height;
            height = MAX_HEIGHT;
          }
          canvas.width = width;
          canvas.height = height;
          ctx?.drawImage(img, 0, 0, width, height);
          resolve(canvas.toDataURL('image/jpeg', 0.8));
        };
      };
      reader.onerror = reject;
      reader.readAsDataURL(file);
    });
  }

  preloadImage(src: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.src = src;
      img.onload = () => resolve();
      img.onerror = (err) => reject(err);
    });
  }

  async showFilteredImage(item: ProcessDocData) {
    try {
      if (item.filtered_image) {
        await this.preloadImage(item.filtered_image);
        this.isScanning = true;
      } else {
        console.error("L'image filtrée n'est pas définie");
      }
    } catch (error) {
      console.error("Impossible de charger l'image filtrée", error);
    }
  }

  onFilteredImageLoad() {
    this.filteredImageLoaded = true;
  }

  cacheImage(key: string, imageUrl: string) {
    localStorage.setItem(key, imageUrl);
  }

  getCachedImage(key: string): string | null {
    return localStorage.getItem(key);
  }

  
  scan_bl() {
    this.navCtrl.navigateRoot('/scan-bl');  
  }
}
