import { Component, ViewChild, AfterViewInit, ElementRef, ViewEncapsulation } from '@angular/core';
import { NavController, ModalController } from '@ionic/angular';
import { ImageModalComponent } from '../image-modal/image-modal.component';

@Component({
  selector: 'app-guide',
  templateUrl: './guide.page.html',
  styleUrls: ['./guide.page.scss'],
})
export class GuidePage implements AfterViewInit {

  @ViewChild('swiper', { static: true }) swiper: ElementRef | undefined;
  @ViewChild('popover', { static: true }) popover: any;

  slidesData = [
    {
      image: 'assets/guide/welcome_guide.png',
      title: 'Bienvenue dans le Guide de Numérisation',
      description: 'Suivez ce guide pour capturer vos bons de livraison avec précision. Découvrez les bonnes pratiques et évitez les erreurs courantes.',
      images_details: []
    },
    {
      image: 'assets/guide/good_one.jpg',
      title: 'Assurez-vous de l\'éclairage',
      description: 'Prenez des photos dans un environnement bien éclairé pour une meilleure qualité d\'image et évitez les ombres en tenant l\'appareil à un angle de 90 degrés par rapport au document.',
      images_details: [
        {
          image: 'assets/guide/lighting_error_01.JPG',
          class: 'img-details-2'
        },
        {
          image: 'assets/guide/lighting_error_02.JPG',
          class: 'img-details-2'
        }
      ]
    },
    {
      image: 'assets/guide/good_one.jpg',
      title: 'Redressez les images inclinées',
      description: 'Assurez-vous que le document est bien droit avant de prendre la photo. Une image inclinée peut rendre l\'OCR moins précis.',
      images_details: [
        {
          image: 'assets/guide/rotated_error_01.png',
          class: 'img-details-2'
        },
        {
          image: 'assets/guide/rotated_error_02.jpg',
          class: 'img-details-2'
        }
      ]
    },
    {
      image: 'assets/guide/good_one.jpg',
      title: 'Maintenez le document à plat',
      description: 'Placez le document sur une surface plane et assurez-vous qu\'il est bien aligné pour éviter les distorsions.',
      images_details: [
        {
          image: 'assets/guide/skew_error_01.jpg',
          class: 'img-details-2'
        },
        {
          image: 'assets/guide/skew_error_02.JPG',
          class: 'img-details-2'
        }
      ]
    },
    {
      image: 'assets/guide/good_one.jpg',
      title: 'Lissez le papier froissé',
      description: 'Si le papier est froissé, essayez de le lisser autant que possible avant de prendre la photo pour améliorer la qualité de l\'image.',
      images_details: [
        {
          image: 'assets/guide/wrinkled_error_01.JPG',
          class: 'img-details-2'
        },
        {
          image: 'assets/guide/wrinkled_error_02.JPG',
          class: 'img-details-2'
        }
      ]
    },
    {
      image: 'assets/guide/good_one_quality.jpg',
      title: 'Vérifiez la mise au point',
      description: 'Assurez-vous que le document est bien mis au point avant de prendre la photo pour des résultats clairs et lisibles.',
      images_details: [
        {
          image: 'assets/guide/quality_error_01.jpg',
          class: 'img-details-2'
        },
        {
          image: 'assets/guide/quality_error_02.jpg',
          class: 'img-details-2'
        }
      ]
    },
    {
      image: 'assets/guide/good_one_quality.jpg',
      title: 'Ne rien écrire sur le document',
      description: 'Pour des résultats OCR optimaux, assurez-vous que le document ne contient aucune écriture manuelle avant la numérisation.',
      images_details: [
        {
          image: 'assets/guide/pen_error_01.JPG',
          class: 'img-details-2'
        },
        {
          image: 'assets/guide/pen_error_02.JPG',
          class: 'img-details-2'
        }
      ]
    },
  ];
  

  constructor(private navCtrl: NavController, private modalController: ModalController) {}

  ngAfterViewInit() {
    if (this.swiper && this.swiper.nativeElement) {
      const swiperInstance = this.swiper.nativeElement.swiper;
    }
  }

  async openImageModal(imageSrc: string) {
    const modal = await this.modalController.create({
      component: ImageModalComponent,
      cssClass: 'guide-modal',
      componentProps: { imageSrc: imageSrc }
    });
    return await modal.present();
  }

  skip() {
    this.navCtrl.navigateRoot('/scan-bl');  
  }

  next() {
    if (this.swiper && this.swiper.nativeElement) {
      const swiperInstance = this.swiper.nativeElement.swiper;
      const currentIndex = swiperInstance.activeIndex;
      const totalSlides = swiperInstance.slides.length - 1;

      if (currentIndex === totalSlides) {
        // Navigate to 'scan-bl' page if it's the last slide
        this.skip()
      } else {
        // Otherwise, navigate to the next slide
        swiperInstance.slideNext();
      }
    }
  }
}