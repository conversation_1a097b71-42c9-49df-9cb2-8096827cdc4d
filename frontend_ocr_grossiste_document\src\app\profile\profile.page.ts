import { Component, OnInit,  } from '@angular/core';
import { AlertController, NavController } from '@ionic/angular';
import { ApiService } from '../services/api.service';
import { Location } from '@angular/common';
import { UserService } from '../services/user.service';
import { User } from './user.model';

@Component({
  selector: 'app-profile',
  templateUrl: './profile.page.html',
  styleUrls: ['./profile.page.scss'],
})
export class ProfilePage implements OnInit {

  user : User | null = null;

  constructor( private navCtrl: NavController ,
    private apiService:ApiService,
    private location:Location,
    private userService:UserService,
    private alertController: AlertController,

  ) { }

  ngOnInit() {
    this.forceLightMode();
    this.user = this.userService.user();    
  }

  goBack() {
 // check if there is pages before 
    if (this.location.getState() && (this.location.getState() as unknown as any)?.navigationId > 1) {
      this.navCtrl.pop();
    } else {
      this.navCtrl.navigateBack('/scan-bl');
    }
    this.navCtrl.pop();
  }

  darkMode = false;

 

  navigateTo(page: string) {
    this.navCtrl.navigateForward(`/${page}`);
  }

  navigateToPayments() {
    this.navCtrl.navigateForward('/payments');
  }

  toggleDarkMode() {
    document.body.classList.toggle('dark', this.darkMode);
  }

 

  // onAbout() {
  //    this.alertController.create({
  //     animated : true,
  //     header:'À propos',

  //     message:'Sophatel Tous droits réservés',
  //     buttons:['OK']
  //    }).then(alert=>alert.present())
  // }

  onLogout() {
    this.alertController.create({
      animated:true,
      header : "Confirmation de déconnection",
      message:"Voulez-vous vraiment se déconnecter",
      buttons:[
        {
          text:"Oui",
          handler:()=>{
            localStorage.removeItem('tokenUser');
            localStorage.removeItem('tokenTenant');
            localStorage.removeItem('token');
            localStorage.removeItem('ocrMode');
            localStorage.removeItem('forceSupplierGlobal');
            localStorage.removeItem('selectedSupplier');
            
            this.navCtrl.navigateBack('/login');
          },
          
        },
        {
          text:"Non",
          role:'cancel'
          
        }
      ]
    }).then(alert=>alert.present())
  }




  private forceLightMode() {
    // Remove dark mode from body
    document.body.classList.remove('dark');
    document.documentElement.classList.remove('dark');
    
    // Add light mode
    document.body.classList.add('light');
    document.documentElement.classList.add('light');

    // Set attribute
    document.body.setAttribute('data-theme', 'light');
    
    // Force color scheme
    const meta = document.createElement('meta');
    meta.name = 'color-scheme';
    meta.content = 'light';
    document.head.appendChild(meta);
  }


  async onAbout() {
    const alert = await this.alertController.create({
      header: 'À propos de l\'application',
      cssClass: 'custom-alert',
      message: `
        <div class="about-content">
          <h4>Description</h4>
          <p>Avec <b>WinDoc</b>, scannez vos <b>Bons de Livraison</b> en quelques secondes. Les données extraites seront disponibles dans votre espace personnel sur <b>WinPlusPharma</b>.</p>
          <p><small>Menu → Achats → Réception → Import BL → Importer BLs Scannés</small></p>
          
          <h4>Conditions d'utilisation</h4>
          <ul>
            <li>Cette application est réservée à un usage professionnel</li>
            <li>Les données capturées sont traitées de manière confidentielle</li>
            <li>L'utilisateur s'engage à respecter les bonnes pratiques de numérisation</li>
          </ul>
  
          <h4>Version</h4>
          <p>Version 1.0.0</p>
          
          <small class="privacy-link" id="privacy-link">Politique de confidentialité</small>
        </div>
      `,
      buttons: [
        {
          text: 'Fermer',
          role: 'cancel'
        }
      ]
    });
  
    await alert.present();
  
    // Add click listener for privacy link
    const privacyLink = document.getElementById('privacy-link');
    if (privacyLink) {
      privacyLink.addEventListener('click', () => {
        alert.dismiss();
        this.showPrivacyPolicy();
      });
    }
  }
  
  async showPrivacyPolicy() {
    const alert = await this.alertController.create({
      header: 'Politique de confidentialité',
      cssClass: 'custom-alert',
      message: `
        <div class="privacy-content">
          <p>Nous nous engageons à protéger vos données personnelles :</p>
          <ul>
            <li>Les données capturées sont stockées de manière sécurisée</li>
            <li>Aucune information personnelle n'est partagée avec des tiers</li>
            <li>Les documents sont traités conformément au RGPD</li>
            <li>Vous disposez d'un droit d'accès et de rectification de vos données</li>
          </ul>
        </div>
      `,
      buttons: ['Fermer']
    });
  
    await alert.present();
  }

  async logout() {
    await this.apiService.logout();  // Wait for the confirmation dialog
    this.navCtrl.navigateRoot('/login');  // Then navigate to login
  }

}
