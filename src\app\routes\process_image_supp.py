from fastapi import APIRouter, HTTPException, WebSocketDisconnect, Depends
from src.app.dependencies import get_current_user, check_headers_dynamic
import traceback
import asyncio
import logging
import sys
from ..config import API_URL
from ..services.websocket_manager import manager
from ..utils.helpers import get_temp_path
from ..utils.models import ProcessImageSuppRequest
from ..services import identify_supplier
from src.app.camscanner_lite import smart_crop, magic_pro_filter
from concurrent.futures import ThreadPoolExecutor

router = APIRouter()

logger = logging.getLogger(__name__)

# Define the API URL
apiUrl = API_URL

# Initialize a ThreadPoolExecutor
executor = ThreadPoolExecutor(max_workers=10)

@router.post("/")
async def process_image_supp_endpoint(request: ProcessImageSuppRequest):
    try:

        # Log the entire request in one line
        logging.info(f"Received request from process_image_supp_endpoint: {request.dict()}")

        # Run `process_identify_supplier` in a ThreadPoolExecutor
        loop = asyncio.get_event_loop()
        result = await loop.run_in_executor(
            executor,
            lambda: asyncio.run(process_image_supp_process(request))
        )

        # Return the result
        return result

    except HTTPException as http_exc:
        logging.error(f"Erreur HTTP: {http_exc.detail}")
        raise http_exc
    except Exception as e:
        logging.error(f"Erreur inattendue: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail="Erreur interne du serveur")


async def process_image_supp_process(request: ProcessImageSuppRequest):
    try:
        # logging.info("Received request with random_id: %s and model_name: %s", request.random_id, request.model_name)

        output_dir_crop_img_path = get_temp_path() / "smart_crop_output"
        output_dir_crop_img_path.mkdir(exist_ok=True, parents=True)
        logging.info("Created smart_crop_output directory: %s", output_dir_crop_img_path)

        output_dir_filter_img_path = get_temp_path() / "magic_pro_filter_output"
        output_dir_filter_img_path.mkdir(exist_ok=True)
        logging.info("Created magic_pro_filter_output directory: %s", output_dir_filter_img_path)

        origin_images_path = get_temp_path() / "origin_images_output"
        matching_files = list(origin_images_path.glob(f"*_origin_{request.random_id}.*"))
        if not matching_files:
            message = f"No file matches the pattern '*_origin_{request.random_id}.*' in the directory '{origin_images_path}'"
            logging.error(message)
            raise FileNotFoundError(message)

        image_origin_path = matching_files[0]
        logging.info("image_origin_path ___: %s", image_origin_path)
        logging.info("Found original image: %s", image_origin_path)

        # if matching_files:
        #     image_origin_path = matching_files[0]
        #     logging.info("Found original image: %s", image_origin_path)
        # else:
        #     message = f"No file matches the pattern '*_origin_{request.random_id}.*' in the directory '{origin_images_path}'"
        #     logging.error(message)
        #     raise FileNotFoundError(message)

        # Notify the client about progress (20% complete)
        try:
            await manager.send_progress(request.job_id, 20)
            await asyncio.sleep(0.01)

        except (WebSocketDisconnect, RuntimeError) as e:
            logger.error(f"Error sending progress update: {e}")

        """ --------------********-- Smart Crop Image ----********------------------ """

        # Smart Crop Image
        try:
            scanner = smart_crop.DocScanner(random_id=request.random_id)
            coordinates_list = [[coord.x, coord.y] for coord in request.coordinates]
            cropped_path, cropped_image = scanner.crop_image(coordinates_list, request.random_id, image_origin_path,
                                                             output_dir_crop_img_path, request.rotation)
            logging.info("Cropped image saved at: %s", cropped_path)
        except Exception as e:
            logging.error(f"Exception during smart cropping: {traceback.format_exc()}")
            raise HTTPException(status_code=500, detail="Erreur lors du recadrage de l'image.")

        """ --------------********-- Smart Crop Image ----********------------------ """

        # Notify the client about progress (40% complete)
        try:
            await manager.send_progress(request.job_id, 40)
            await asyncio.sleep(0.01)
        except (WebSocketDisconnect, RuntimeError) as e:
            logger.error(f"Error sending progress update: {e}")

        """ --------------********-- Identify Supplier ----********------------------ """

        # Identify Supplier
        try:
            document_header = identify_supplier.identify_supplier(cropped_path, "GLOBAL",
                                                                  get_temp_path() / "identify_suppliers",
                                                                  request.random_id)
            supplier_name = document_header.header.name_fournisseur
            logging.info("Identified supplier name: %s", supplier_name)
        except Exception as e:
            logging.error(f"Exception during supplier identification: {traceback.format_exc()}")
            raise HTTPException(status_code=500, detail="Erreur lors de l'identification du fournisseur.")

        """ --------------********-- Identify Supplier ----********------------------ """

        # Notify the client about progress (60% complete)
        try:
            await manager.send_progress(request.job_id, 60)
            await asyncio.sleep(0.01)
        except (WebSocketDisconnect, RuntimeError) as e:
            logger.error(f"Error sending progress update: {e}")

        """ --------------********-- Filter Image ----********------------------ """

        # Apply Magic Pro Filter
        try:
            image_name_output = cropped_path.stem + '_Filtered_' + request.random_id + cropped_path.suffix
            output_magic_pro_path = output_dir_filter_img_path / f"{image_name_output}"
            model_name = supplier_name.upper() if supplier_name.upper() != 'UNKNOWN' else 'GLOBAL'
            filtered_img_path, filtered_img = magic_pro_filter.apply_magic_pro_filter(str(cropped_path),
                                                                                      str(output_magic_pro_path),
                                                                                      model_name, 'custom')
            logging.info("Applied magic pro filter, result saved at: %s", filtered_img_path)
        except Exception as e:
            logging.error(f"Exception during magic pro filtering: {traceback.format_exc()}")
            raise HTTPException(status_code=500, detail="Erreur lors de l'application du filtre magique pro.")

        """ --------------********-- Filter Image ----********------------------ """

        # Notify the client about progress (80% complete)
        try:
            await manager.send_progress(request.job_id, 80)
            await asyncio.sleep(0.01)
        except (WebSocketDisconnect, RuntimeError) as e:
            logger.error(f"Error sending progress update: {e}")

        # Check supplier match
        if request.model_name.value.upper() != 'GLOBAL' and request.model_name.value.upper() != 'AUTRE':
            if supplier_name.upper() != request.model_name.value.upper():
                message = f"Le nom du fournisseur '{supplier_name}' ne correspond pas au modèle attendu '{request.model_name.value}'."
                logging.warning(message)
                supplier_match = False
            else:
                message = "Traitement réussi."
                supplier_match = True
        else:
            message = "Traitement réussi."
            supplier_match = True

        # Construct URLs for the images
        cropped_image_url = f"{apiUrl}/static/smart_crop_output/{cropped_path.name}"
        filtered_image_url = f"{apiUrl}/static/magic_pro_filter_output/{output_magic_pro_path.name}"

        # Notify the client about progress (100% complete)
        try:
            await manager.send_progress(request.job_id, 100)
            await asyncio.sleep(0.01)
        except (WebSocketDisconnect, RuntimeError) as e:
            logger.error(f"Error sending progress update: {e}")

        data = {
            "cropped_image": cropped_image_url,
            "filtered_image": filtered_image_url,
            "supplier_name": supplier_name,
            "supplier_match": supplier_match,
            "random_id": request.random_id,
            "message": message
        }
        return data

    except FileNotFoundError as fnf_error:
        logging.error("FileNotFoundError: %s", fnf_error)
        raise HTTPException(status_code=404, detail=str(fnf_error))
    except Exception as e:
        logging.error("Exception: %s", e)
        logging.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail="Erreur de traitement de la requête.")