// scanner-modal.component.ts
import { Component, Input, OnInit } from '@angular/core';
import { ModalController } from '@ionic/angular';

interface ScanImage {
  id: number;
  data: string;
  selected: boolean;
}

@Component({
  selector: 'app-scanner-modal',
  templateUrl: './scanner-modal.component.html',
  styleUrls: ['./scanner-modal.component.scss']
})
export class ScannerModalComponent implements OnInit {
  @Input() scans: string[] = [];
  scanImages: ScanImage[] = [];

  constructor(private modalCtrl: ModalController) {}

  ngOnInit() {
    console.log('Original scans:', this.scans);
    this.scanImages = this.scans.map((scan, index) => ({
      id: index,
      data: scan,
      selected: false
    }));
  }

  toggleSelection(scan: ScanImage, event?: Event) {
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }
    
    // Deselect all other images first
    this.scanImages.forEach(img => {
      img.selected = false;
    });
    
    // Select only the clicked image
    scan.selected = true;
    
    console.log('Selected image:', scan.id);
    console.log('Selection state:', this.scanImages.map(img => ({id: img.id, selected: img.selected})));
  }

  getSelectedImages(): string[] {
    return this.scanImages
      .filter(scan => scan.selected)
      .map(scan => scan.data);
  }

  dismiss() {
    this.modalCtrl.dismiss();
  }

  confirm() {
    const selectedImages = this.getSelectedImages();
    console.log('Confirming selection:', selectedImages.length);
    this.modalCtrl.dismiss({
      selectedImages: selectedImages
    });
  }
}