// src\app\crop-doc\crop-doc.page.ts
import { Location } from '@angular/common';
import { Component, ElementRef, OnInit, ViewChild, inject, Renderer2, ViewContainerRef, TemplateRef, AfterViewInit, HostListener } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { NavController, LoadingController, AlertController  } from '@ionic/angular';
import { ApiService } from '../services/api.service';  // Import the ApiService
import { Coordinates } from '../../models/coordinates';
import { SignalService } from '../services/signal.service';
import { NetworkService } from '../services/network.service';
import { WebSocketService } from '../services/websocket.service';
import { environment } from 'src/environments/environment';
import { Platform } from '@ionic/angular';
import { ImageCropperCustomComponent } from '../components/image-cropper-custom/image-cropper-custom.component';

@Component({
  selector: 'app-crop-doc',
  templateUrl: './crop-doc.page.html',
  styleUrls: ['./crop-doc.page.scss'],
})
export class CropDocPage implements AfterViewInit  {
  imageUrl: string | any = null;
  coordinates: Coordinates[] = [];
  uuid: string | any = null;
  needs_rotation: boolean = false;
  @ViewChild('cropper', { static: false }) cropper: ElementRef | undefined;
  rotationAngle: number = 0;
  @ViewChild('cropperComponent') cropperComponent!: ImageCropperCustomComponent;

  navCtrl = inject(NavController)
  apiService = inject(ApiService);  // Inject the ApiService
  signalService = inject(SignalService);
  
  progress = 0;
  isLoading = false;
  isConnected = true; // Track network status
  jobId: string | undefined; // Add this line to store the job ID

  private currentWidth = 0;
  private currentHeight = 0;

  constructor(
    private route: ActivatedRoute,
    private location: Location,
    private loadingController: LoadingController, 
    private alertController: AlertController, 
    private webSocketService: WebSocketService,
    private networkService: NetworkService,
    private platform: Platform,
    private renderer: Renderer2
  ) {
    // this.jobId = this.apiService.generateJobId(); // Generate job ID once
  }

  ngAfterViewInit() {

    // Suppose your incoming image is W=3060 by H=4080:
    this.currentWidth = 3060;
    this.currentHeight = 4080;
    
    const params = this.location.getState() as { imageUrl: string; coordinates:  Coordinates[]; uuid: string, needs_rotation: boolean};
    if(!params.imageUrl || !params.coordinates || !params.uuid) {
      this.navCtrl.navigateBack('/scan-bl');
      return;
    }
    this.isValidBlobUrl(params.imageUrl).then((isValid) => {
      if (!isValid) {
        this.navCtrl.navigateBack('/scan-bl');
        return;
      }
      this.imageUrl = params?.imageUrl;
      this.coordinates = params?.coordinates;
      this.uuid = params?.uuid;
      this.needs_rotation = params?.needs_rotation;

     setTimeout(() => {
      
       this.openCropper( params.imageUrl, params.coordinates);
     }, 500);
     
      console.log(this.location.getState())
    });
    console.log(this.location.getState())

    if (params?.needs_rotation) {
      this.showRotationAlert();
    }
    

    // Subscribe to the network status
    this.networkService.getNetworkStatus().subscribe((connected: boolean) => {
      this.isConnected = connected;
    });

    if (this.platform.is('android')) {
      this.renderer.addClass(document.body, 'android-specific');
    } else {
      this.renderer.addClass(document.body, 'other-platform');
    }

    // // const jobId = this.apiService.generateJobId();  // Get the job ID
    // const websocketUrl = `${environment.webSocketUrl}/${this.jobId}`;
    // this.webSocketService.connect(websocketUrl, this.jobId);
    // this.webSocketService.onMessage(this.jobId).subscribe((message) => {
    //   // console.log('Received message:', message);
    //   if (message.progress !== undefined) {
    //     this.progress = message.progress;
    //   }

    //   console.log("progress __ :" , this.progress)

    // });
  }

  async showRotationAlert() {
    const alert = await this.alertController.create({
      header: 'Attention',
      message: "L'image est peut-être pivotée, veuillez la corriger si nécessaire",
      buttons: ['OK']
    });
  
    await alert.present();
  }

  // A helper function to rotate a single point by 90° clockwise about (0,0)
  private rotatePoint90Clockwise(pt: Coordinates, oldWidth: number): Coordinates {
    // If pt.x or pt.y can be missing, you can do:
    const x = pt.x ?? 0;  // fallback to 0 if undefined
    const y = pt.y ?? 0;  // fallback to 0 if undefined
  
    console.log({
      x: y,
      y: oldWidth - x
    })
    // Then apply the rotation math
    return {
      x: y,
      y: oldWidth - x
    };
  }

// rotateImage(angle: number) {
//     // Add animation to the button
//     const button = document.querySelector('.rotate-btn ion-icon');
//     button?.classList.add('rotating');
    
//     // Remove animation class after completion
//     setTimeout(() => {
//         button?.classList.remove('rotating');
//     }, 500);
    
//     // Normalize the angle within 0° to 360°
//     this.rotationAngle = (this.rotationAngle + angle) % 360;
//     if (this.rotationAngle < 0) {
//         this.rotationAngle += 360; // Ensure positive angle
//     }

//     if (this.coordinates.length !== 4) {
//         console.error("Invalid number of coordinates for rotation:", this.coordinates);
//         return;
//     }

//     // Validate all points to ensure there are no NaN values
//     this.coordinates.forEach((point, index) => {
//         if (isNaN(point.x) || isNaN(point.y)) {
//             throw new Error(`Point ${index} has invalid coordinates: (${point.x}, ${point.y})`);
//         }
//     });

//     // Rotate the points according to the current angle
//     switch (this.rotationAngle) {
//         case 90:
//             this.coordinates = this.coordinates.map((point) => this.rotatePoint90Clockwise(point, this.currentWidth));
//             break;
//         case 180:
//             this.coordinates = this.coordinates.map((point) => ({
//                 x: this.currentWidth - point.x,
//                 y: this.currentHeight - point.y,
//             }));
//             break;
//         case 270:
//             this.coordinates = this.coordinates.map((point) => this.rotatePoint90Clockwise(point, this.currentWidth));
//             this.coordinates.reverse(); // Reverse to fix the polygon's point order
//             break;
//         default:
//             break; // No rotation
//     }
    
//       console.log("coordinates:", this.coordinates);

//     // Update the cropper display
//     if (this.cropper) {
//         const cropperElement = this.cropper.nativeElement.querySelector('app-image-cropper-custom');
//         if (cropperElement) {
//             cropperElement.style.transform = `rotate(${this.rotationAngle}deg)`;
//         }
//     }
// }


rotateImage(angle: number) {
  // Add animation to the button
  const button = document.querySelector('.rotate-btn ion-icon');
  button?.classList.add('rotating');
  
  // Remove animation class after completion
  setTimeout(() => {
      button?.classList.remove('rotating');
  }, 500);
  
  // Update rotation angle
  this.rotationAngle = (this.rotationAngle + angle) % 360;
  if (this.rotationAngle < 0) {
      this.rotationAngle += 360;
  }

  // Update the cropper component's rotation
  if (this.cropperComponent) {
      this.cropperComponent.rotation = this.rotationAngle;
  }
}




  /**
 * Rotates a single (x,y) corner by a multiple of 90 degrees clockwise.
 * Also handles bounding box swaps if angle = 90 or 270.
 *
 * @param pt    The corner (x,y).
 * @param angle  The rotation in degrees (only 90, 180, or 270).
 * @returns The rotated corner (x,y).
 */
private rotateCorners(pt: Coordinates, angle: number): Coordinates {
  const x = pt.x ?? 0; // fallback if undefined
  const y = pt.y ?? 0;

  // We'll use local copies so we can change them
  let x2 = x;
  let y2 = y;

  // For convenience in formulas
  const w = this.currentWidth;
  const h = this.currentHeight;

  // Choose a formula based on angle
  switch (angle) {
    case 90:
      //  x' = y
      //  y' = (oldWidth) - x
      x2 = y;
      y2 = w - x;
      // bounding box becomes (h, w) after 90° rotation
      break;
    case 180:
      //  x' = (oldWidth) - x
      //  y' = (oldHeight) - y
      x2 = w - x;
      y2 = h - y;
      // bounding box remains (w,h) for 180° around top-left
      break;
    case 270:
      //  x' = (oldHeight) - y
      //  y' = x
      x2 = h - y;
      y2 = x;
      // bounding box becomes (h, w) after 270° rotation
      break;
    default:
      // If angle is something else, do nothing or handle error
      console.warn(`rotateCorners() received an unexpected angle: ${angle}`);
      break;
  }

  return { x: x2, y: y2 };
}



// openCropper(imageUrl: string = '', coordinates: any[] = []) {
//   const cropper = document.querySelector('image-cropper') as any;
//   coordinates = this.convertCoordinates(coordinates);
//   this.coordinates = coordinates;
  
//   if (cropper && this.imageUrl) {
//     const image = new Image();
//     image.src = imageUrl;
    
//     image.onload = () => {
//       // Store dimensions for rotation calculations
//       this.currentWidth = image.width;
//       this.currentHeight = image.height;
      
//       // Update cropper styles based on image dimensions
//       const cropperElement = cropper.parentElement;
//       if (cropperElement) {
//         Object.assign(cropperElement.style, this.getCropperStyles());
//       }
      
//       cropper.img = image;
//       cropper.inactiveSelections = [];
//       cropper.quad = { points: coordinates };
//       cropper.rotation = this.rotationAngle;
//     };
//   }
// }

async openCropper(imageUrl: string = '', coordinates: Coordinates[] = []) {
  if (!imageUrl || !coordinates.length) return;

  console.log('Opening cropper with:', { imageUrl, coordinates });

  // Convert coordinates if needed
  const convertedCoordinates = this.convertCoordinates(coordinates);
  console.log('Converted coordinates:', convertedCoordinates);

  // Wait for component to be available
  await new Promise(resolve => setTimeout(resolve, 100));

  if (this.cropperComponent) {
    // Set image first
    this.cropperComponent.imageUrl = imageUrl;
    
    // Wait for image to load
    await new Promise(resolve => setTimeout(resolve, 200));
    
    // Then set coordinates
    this.cropperComponent.initialCoordinates = convertedCoordinates;
    
    // Update rotation if needed
    this.cropperComponent.rotation = this.rotationAngle;
  } else {
    console.error('Cropper component not found');
  }
}



// async getUpdatedCoordinates() {

//   this.jobId = this.apiService.generateJobId(); // Generate job ID
//   const websocketUrl = `${environment.webSocketUrl}/${this.jobId}`;
//   console.log('WebSocket URL crop-doc:', websocketUrl);
//   this.webSocketService.connect(websocketUrl, this.jobId);

//   this.webSocketService.onMessage(this.jobId).subscribe((message) => {
//     if (message.progress !== undefined) {
//       this.progress = message.progress;
//       console.log("progress __ :", this.progress);
//     }
//   });


//   // const loading = await this.presentLoading(); // Show loading spinner
//   this.isLoading = true;
//   console.log('Request:', this.coordinates);

//   const cropper = document.querySelector('image-cropper') as any;
//   if (cropper) {
//     const updatedQuad = await cropper.getQuad();
//     const coordinates : Coordinates[] = updatedQuad.points;
//     const that = this;

//     // Create the request object
//     const model_name =  (localStorage.getItem('selectedSupplier') && localStorage.getItem('selectedSupplier') != 'undefined' && localStorage.getItem('selectedSupplier') != 'AUTRE') ? localStorage.getItem('selectedSupplier') : 'GLOBAL';
//     const request = {
//       coordinates,
//       random_id: this.uuid,
//       model_name: model_name?.toString(),
//       rotation:this.rotationAngle
//     };

//     console.log('Request:', request);

//     // Call the processImageSupp API
//     this.apiService.processImageSupp(request, this.jobId).subscribe(
//       (response) => {
//         // loading.dismiss();
//         this.isLoading = false;
//         console.log('API response:', response);
//         // Navigate to the process-doc page with the response data
//         this.signalService.setData(response);
//         this.navCtrl.navigateForward('/process-doc', { state: response });

//         // Disconnect WebSocket after completion
//         this.webSocketService.close(this.jobId!);
//       },
//       (error) => {
//         // loading.dismiss();
//         this.isLoading = false;
//         const errorMessage = `
//         <h3>Erreur lors le traitement du document</h3>
//         <ul>
//           <li>Verfier les contours du document</li>
//           <li>Verifier la luminosité de l'image</li>
//           <li>Verifier la qualité de l'image</li>
//           <li>Supprimer les objets inutiles dans l'image </li>
//         </ul>
//         `;
//         this.apiService.showErrorAlert( errorMessage);
//         console.error('API error 22:', error.error.message);
//         console.error(errorMessage);

//         // Disconnect WebSocket after completion
//         this.webSocketService.close(this.jobId!);
//       }
//     );
//   } else {
//     console.log('Cropper not found');
//   }

//   this.webSocketService.onMessage(this.jobId).subscribe((message) => {
//     if (message.progress !== undefined) {
//       this.progress = message.progress;
//     }
//   });
// }

async getUpdatedCoordinates() {
  if (!this.cropperComponent) {
    console.error('Cropper component not found');
    return;
  }

  this.isLoading = true;
  this.jobId = this.apiService.generateJobId();

  try {
    const quad = await this.cropperComponent.getQuad();
    const coordinates = quad.points;

    console.log('Original coordinates:', this.coordinates);
    console.log('Updated coordinates:', coordinates);

    const model_name = localStorage.getItem('selectedSupplier') || 'GLOBAL';
    const request = {
      coordinates,
      random_id: this.uuid,
      model_name: model_name === 'AUTRE' || model_name === 'undefined' ? 'GLOBAL' : model_name,
      rotation: this.rotationAngle
    };

    console.log('Sending request:', request);

    // Setup WebSocket
    const websocketUrl = `${environment.webSocketUrl}/${this.jobId}`;
    this.webSocketService.connect(websocketUrl, this.jobId);
    
    this.webSocketService.onMessage(this.jobId).subscribe(message => {
      if (message.progress !== undefined) {
        this.progress = message.progress;
      }
    });

    // Call API
    this.apiService.processImageSupp(request, this.jobId).subscribe(
      response => {
        this.isLoading = false;
        this.signalService.setData(response);
        this.navCtrl.navigateForward('/process-doc', { state: response });
        this.webSocketService.close(this.jobId!);
      },
      error => {
        this.isLoading = false;
        const errorMessage = `
          <h3>Erreur lors le traitement du document</h3>
          <ul>
            <li>Verfier les contours du document</li>
            <li>Verifier la luminosité de l'image</li>
            <li>Verifier la qualité de l'image</li>
            <li>Supprimer les objets inutiles dans l'image </li>
          </ul>
        `;
        this.apiService.showErrorAlert(errorMessage);
        this.webSocketService.close(this.jobId!);
      }
    );
  } catch (error) {
    this.isLoading = false;
    console.error('Error getting coordinates:', error);
  }
}



  // convertCoordinates(arr : any[]) {
  //   return arr.map(item => {
  //     return {
  //       x: parseFloat(item[0].toFixed(1)),
  //       y: parseFloat(item[1].toFixed(1))
  //     };
  //   });
  // }

  convertCoordinates(arr: any[]): Coordinates[] {
    return arr.map(item => {
      // Handle both array format [x, y] and object format {x, y}
      if (Array.isArray(item)) {
        return {
          x: parseFloat(item[0].toFixed(1)),
          y: parseFloat(item[1].toFixed(1))
        };
      } else {
        return {
          x: parseFloat((item.x || 0).toFixed(1)),
          y: parseFloat((item.y || 0).toFixed(1))
        };
      }
    });
  }

  async isValidBlobUrl(blobUrl: string): Promise<boolean> {
    try {
      const response = await fetch(blobUrl);
      return response.ok; // Returns true if the response is ok (status is in the range 200-299)
    } catch (error) {
      return false; // Returns false if there is an error (e.g., network issue, invalid URL)
    }
  }

  async presentLoading() {
    const loading = await this.loadingController.create({
      message: 'Chargement...',
      spinner: 'circles',
      // duration: 30000 // Optional: specify a timeout for the loading spinner
    });
    await loading.present();
    return loading;
  }
  
  async removeCroppedImage(){
    console.log('Remove cropped image');
    const alert = await this.alertController.create({
      header: 'Supprimer le document',
      message: `Êtes-vous sûr de vouloir supprimer Tous les documents ?`,
      buttons: [
        {
          text: 'Annuler',
          role: 'cancel',
          cssClass: 'custom-alert-button cancel',
          handler: () => {
            console.log('Confirm Cancel');
          },
        },
        {
          text: 'Oui, Supprimer !',
          cssClass: 'custom-alert-button danger',
          handler: () => {
            this.signalService.removeAllData();
            // redirect to scan-bl
            this.navCtrl.navigateRoot('/scan-bl'); 
          },
        },
      ],
    });

    await alert.present();
  }

  async reTakePhoto() {
    const alert = await this.alertController.create({
      header: 'Supprimer le document',
      message: `Êtes-vous sûr de vouloir prends une nouvelle photo ?`,
      buttons: [
        {
          text: 'Annuler',
          role: 'cancel',
          cssClass: 'custom-alert-button cancel',
          handler: () => {
            console.log('Confirm Cancel');
          },
        },
        {
          text: 'Oui',
          cssClass: 'custom-alert-button',
          handler: () => {
            // this.signalService.removeLastIndex();
            // redirect to scan-bl
            this.navCtrl.navigateRoot('/scan-bl');
          },
        },
      ],
    });

    await alert.present();
  }
  clearCroppedImage(){
    console.log('Clear cropped image');
    this.signalService.removeAllData();
    this.navCtrl.navigateRoot('/scan-bl'); 
  }


  getCropperStyles() {
    if (this.imageUrl) {
      const img = new Image();
      img.src = this.imageUrl;
      
      const viewportHeight = window.innerHeight;
      const availableHeight = viewportHeight - 180; // Subtract header and footer height
      
      return {
        // width: this.calculateCropperWidth(img.width, img.height),
        width: '100%',
        height: `${availableHeight}px`,
        maxHeight: '85vh',
        margin: 'auto',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center'
      };
    }
    return {
      width: '90%',
      height: '100%'
    };
  }
  
  calculateCropperWidth(imgWidth: number, imgHeight: number): string {
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;
    const aspectRatio = imgWidth / imgHeight;
    const isVertical = imgHeight > imgWidth;
    
    if (isVertical) {
      // Increase these values for larger vertical images
      const maxWidth = viewportWidth * 0.98; // Changed from 0.85 to 0.98 (98% of viewport width)
      const calculatedHeight = maxWidth / aspectRatio;
      
      if (calculatedHeight > viewportHeight * 0.90) { // Changed from 0.7 to 0.90 (90% of viewport height)
        const maxHeight = viewportHeight * 0.90; // Changed from 0.7 to 0.90
        return `${maxHeight * aspectRatio}px`;
      }
      
      return `${maxWidth}px`;
    } else {
      // Increase this value for larger horizontal images
      const maxWidth = viewportWidth * 0.98; // Changed from 0.9 to 0.98 (98% of viewport width)
      return `${maxWidth}px`;
    }
  }

  @HostListener('window:resize')
  onResize() {
    this.openCropper(this.imageUrl, this.coordinates);
  }

}
