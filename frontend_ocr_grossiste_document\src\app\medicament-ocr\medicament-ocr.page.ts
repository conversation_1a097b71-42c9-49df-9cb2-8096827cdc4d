import {
  <PERSON>mpo<PERSON>,
  OnIni<PERSON>,
  inject,
  Element<PERSON>ef,
  <PERSON><PERSON><PERSON>d,
  <PERSON><PERSON><PERSON><PERSON>,
  HostListener,
} from '@angular/core';
import {
  Nav<PERSON>ontroller,
  LoadingController,
  AlertController,
  ToastController,
} from '@ionic/angular';
import { ApiService } from '../services/api.service';
import { Camera, CameraResultType, CameraSource } from '@capacitor/camera';
import { environment } from 'src/environments/environment';
import { NetworkService } from '../services/network.service';
import { WebSocketService } from '../services/websocket.service';
import { Subject, takeUntil } from 'rxjs';
import { ScannerService } from '../services/scanner.service';

interface Suggestion {
  designation: string;
  code_barre?: string;
  laboratory?: string;
  ppv?: number;
  dosage?: string;
  score?: number;
}

@Component({
  selector: 'app-medicament-ocr',
  templateUrl: './medicament-ocr.page.html',
  styleUrls: ['./medicament-ocr.page.scss'],
})
export class MedicamentOcrPage implements OnInit, OnDestroy {
  isConnected = true;
  progress = 0;
  isLoading = false;
  isFabOpen = false;
  jobId: string | undefined;
  imagePreviewUrl: string | undefined;
  hasScannedImage = false;
  suggestions: Suggestion[] = [];
  isWeb: boolean = false;
  
  readonly TOGGLE_DELAY = 300;
  isAnimating = false;
  
  // Destroy subject for cleanup
  private destroy$ = new Subject<void>();

  @ViewChild('fabButton', { static: false })
  fabButton!: ElementRef<HTMLIonFabButtonElement>;

  constructor(
    private navCtrl: NavController,
    private apiService: ApiService,
    private loadingController: LoadingController,
    private alertController: AlertController,
    private webSocketService: WebSocketService,
    private networkService: NetworkService,
    private scannerService: ScannerService,
    private toastController: ToastController,
  ) {
    this.isWeb = environment.platform === 'web';
  }

  ngOnInit() {
    this.initializeComponent();
  }

  ionViewWillEnter() {
    this.resetStates();
    this.initializeComponent();
  }

  ionViewWillLeave() {
    this.resetStates();
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
    if (this.jobId) {
      this.webSocketService.close(this.jobId);
    }
  }

  private initializeComponent() {
    // Subscribe to the network status
    this.networkService.getNetworkStatus().subscribe((connected: boolean) => {
      this.isConnected = connected;
    });
    this.resetStates();
  }

  private resetStates() {
    this.isFabOpen = false;
    this.isLoading = false;
    this.isAnimating = false;
  }

  resetScan() {
    // When clicking "Scanner à nouveau", directly open the camera
    this.openCamera();
  }

  @HostListener('document:click', ['$event'])
  handleClickOutside(event: Event) {
    const fabElement = this.fabButton?.nativeElement;
    const clickedElement = event.target as HTMLElement;
    
    if (this.isFabOpen && fabElement && !fabElement.contains(clickedElement)) {
      // Check if click was outside the FAB and its children
      if (!clickedElement.closest('ion-fab')) {
        this.onFabToggle();
      }
    }
  }

  async onFabToggle() {
    // Prevent toggle while animating
    if (this.isAnimating) {
      return;
    }
  
    this.isAnimating = true;
    this.isFabOpen = !this.isFabOpen;
  
    // Reset animation lock after transition completes
    setTimeout(() => {
      this.isAnimating = false;
    }, this.TOGGLE_DELAY);
  }

  triggerFabButtonClick() {
    try {
      if (this.fabButton?.nativeElement) {
        this.fabButton.nativeElement.click();
      }
    } catch (err) {
      console.error('Error triggering fab button:', err);
      // Fallback behavior
      this.isFabOpen = !this.isFabOpen;
    }
  }

  async openCamera() {
    // If FAB is open, close it
    if (this.isFabOpen) {
      this.onFabToggle();
    }

    // Reset previous scan data
    this.hasScannedImage = false;
    this.imagePreviewUrl = undefined;
    this.suggestions = [];

    if (this.isWeb) {
      // Launch scanner for web
      await this.startScanning();
    } else {
      try {
        // Logic to open the camera and capture an image
        const image = await Camera.getPhoto({
          quality: 100,
          allowEditing: false,
          resultType: CameraResultType.Uri,
          source: CameraSource.Camera,
        });

        if (image.webPath) {
          const response = await fetch(image.webPath);
          const blob = await response.blob();
          const file = new File([blob], 'captured-medicament.jpg', {
            type: 'image/jpeg',
          });

          this.imagePreviewUrl = image.webPath;
          this.hasScannedImage = true;
          await this.processMedicamentImage(file);
        }
      } catch (error) {
        console.log(error);
        // Handle error appropriately
      }
    }
  }

  async startScanning() {
    const loading = await this.showLoading();
    
    try {
      const canScan = await this.scannerService.launchScan();
      await loading.dismiss();
      
      if (!canScan) {
        await this.showErrorToast();
      }
    } catch (error) {
      await loading.dismiss();
      await this.showErrorToast();
    }
  }

  private async showLoading() {
    const loading = await this.loadingController.create({
      message: 'Tentative de connexion au scanner...',
      spinner: 'circular',
      translucent: true,
      cssClass: 'custom-loading',
      duration: 10000 // Will auto-dismiss after 10 seconds
    });
    await loading.present();
    return loading;
  }

  private async showErrorToast() {
    const toast = await this.toastController.create({
      message: "L'application PostAgent n'est pas ouverte. Merci de l'ouvrir avant de prendre une photo.",
      duration: 3000,
      position: 'top',
      color: 'warning',
      buttons: [
        {
          text: 'OK',
          role: 'cancel'
        }
      ]
    });
    await toast.present();
  }

  async openGallery() {
    // If FAB is open, close it
    if (this.isFabOpen) {
      this.onFabToggle();
    }

    // Reset previous scan data
    this.hasScannedImage = false;
    this.imagePreviewUrl = undefined;
    this.suggestions = [];
    
    // Logic to open the gallery and select an image
    try {
      const image = await Camera.getPhoto({
        quality: 100,
        allowEditing: false,
        resultType: CameraResultType.Uri,
        source: CameraSource.Photos,
      });
  
      if (image.webPath) {
        const response = await fetch(image.webPath);
        const blob = await response.blob();
  
        if(!this.verifySelectedImageBlobType(blob)) {
          return;
        }
  
        const file = new File([blob], `selected-medicament.${image.format}`, {
          type: image.format ? `image/${image.format}` : 'image/jpeg'
        });
  
        this.imagePreviewUrl = image.webPath;
        this.hasScannedImage = true;
        await this.processMedicamentImage(file);
      }
    } catch (error) {
      console.log("getPhoto Error : ", (error as unknown as Error)?.message);
    }
  }

  verifySelectedImageBlobType(blob: Blob) {
    //  should start with image/*
    const type = blob.type.split('/')[0];
    if(type !== 'image') {
      this.alertController.create({
        animated: true,
        header: 'Erreur',
        message: 'Veuillez sélectionner une image valide',
        buttons: ['OK']
      }).then(alert => alert.present());
      return false;
    }
    return true;
  }

  async processMedicamentImage(file: File) {
    this.jobId = this.apiService.generateJobId();
    const websocketUrl = `${environment.webSocketUrl}/${this.jobId}`;
    this.webSocketService.connect(websocketUrl, this.jobId);
    
    // Subscribe to WebSocket messages for progress updates
    this.webSocketService.onMessage(this.jobId).subscribe((message) => {
      if (message.progress !== undefined) {
        this.progress = message.progress;
        console.log('Progress:', this.progress);
      }
    });

    this.isLoading = true;
    this.suggestions = [];

    try {
      this.apiService.getMedicamentInfo(file, this.jobId).subscribe(
        (result: any ) => {
          console.log('API response:', result);
          
          // Process the suggestions
          if (result && result.associations) {
            this.suggestions = result.associations.map((item: any) => {
              return {
                designation: item.nom_base_winplus || 'Médicament inconnu',
                code_barre: item.code_barre_win || '',
                laboratory: '', // Not available in the response
                ppv: item.prix_vente_base_winplus || 0,
                dosage: '', // Not available in the response
                score: 0 // Not available in the response
              };
            });
          }
          
          this.isLoading = false;
          
          // Disconnect WebSocket after completion
          if (this.jobId) {
            this.webSocketService.close(this.jobId);
          }
        },
        (error:any) => {
          console.error('Error processing medicament image:', error);
          this.isLoading = false;
          
          // Show error message
          this.showErrorAlert('Une erreur est survenue lors de l\'analyse de l\'image. Veuillez réessayer.');
          
          // Disconnect WebSocket
          if (this.jobId) {
            this.webSocketService.close(this.jobId);
          }
        }
      );
    } catch (error) {
      console.error('Exception during API call:', error);
      this.isLoading = false;
      this.showErrorAlert('Une erreur inattendue est survenue. Veuillez réessayer.');
      
      if (this.jobId) {
        this.webSocketService.close(this.jobId);
      }
    }
  }

  async showErrorAlert(message: string) {
    const alert = await this.alertController.create({
      header: 'Erreur',
      message: message,
      buttons: ['OK'],
    });
    await alert.present();
  }

  async logout() {
    await this.apiService.logout();
    this.navCtrl.navigateRoot('/login');
  }
}
