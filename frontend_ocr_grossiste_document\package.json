{"name": "windoc", "version": "1.0.4", "author": "Ionic Framework", "homepage": "https://ionicframework.com/", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "build:preprod": "ng build --configuration preprod", "build:webpreprod": "ng build --configuration webpreprod", "serve:preprod": "ng serve --configuration preprod", "serve:webpreprod": "ng serve --configuration webpreprod", "deploy:prod": "nexus-publisher -p prod", "build:dev": "nexus-publisher -p dev --local", "test": "ng test", "lint": "ng lint"}, "private": true, "dependencies": {"@angular/animations": "^17.0.2", "@angular/common": "^17.0.2", "@angular/compiler": "^17.0.2", "@angular/core": "^17.0.2", "@angular/forms": "^17.0.2", "@angular/material": "^18.0.4", "@angular/platform-browser": "^17.0.2", "@angular/platform-browser-dynamic": "^17.0.2", "@angular/router": "^17.0.2", "@capacitor/android": "6.0.0", "@capacitor/app": "6.0.0", "@capacitor/assets": "^3.0.5", "@capacitor/camera": "^6.0.0", "@capacitor/core": "^6.1.0", "@capacitor/filesystem": "^6.0.0", "@capacitor/haptics": "6.0.0", "@capacitor/ios": "^6.1.0", "@capacitor/keyboard": "6.0.0", "@capacitor/network": "^6.0.1", "@capacitor/status-bar": "6.0.0", "@capacitor/storage": "^1.2.5", "@ionic/angular": "^8.2.0", "@ionic/pwa-elements": "^3.3.0", "@ionic/storage-angular": "^4.0.0", "@sophatel/nexus-publisher": "^1.0.2", "@types/swiper": "^6.0.0", "exifreader": "^4.23.3", "image-cropper-component-custom": "^2.3.2", "ionicons": "^7.0.0", "jpeg-js": "^0.4.4", "jszip": "^3.10.1", "jwt-decode": "^4.0.0", "moment": "^2.30.1", "ngx-socket-io": "^4.7.0", "pdf-lib": "^1.17.1", "rxjs": "~7.8.0", "sweetalert2": "^11.12.2", "swiper": "^11.1.4", "tslib": "^2.3.0", "zone.js": "~0.14.2"}, "devDependencies": {"@angular-devkit/build-angular": "^17.0.0", "@angular-eslint/builder": "^17.0.0", "@angular-eslint/eslint-plugin": "^17.0.0", "@angular-eslint/eslint-plugin-template": "^17.0.0", "@angular-eslint/schematics": "^17.0.0", "@angular-eslint/template-parser": "^17.0.0", "@angular/cli": "^17.0.0", "@angular/compiler-cli": "^17.0.2", "@angular/language-service": "^17.0.2", "@capacitor/cli": "6.0.0", "@ionic/angular-toolkit": "^11.0.1", "@types/jasmine": "~5.1.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.57.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsdoc": "^48.2.1", "eslint-plugin-prefer-arrow": "1.2.2", "jasmine-core": "~5.1.0", "jasmine-spec-reporter": "~5.0.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.2.2"}, "description": "An Ionic project"}