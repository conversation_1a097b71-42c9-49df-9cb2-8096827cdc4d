import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { CropDocPageRoutingModule } from './crop-doc-routing.module';
import { SharedModule } from '../shared/shared.module'; // Import SharedModule
import { CropDocPage } from './crop-doc.page';
import { ImageCropperCustomComponent } from '../components/image-cropper-custom/image-cropper-custom.component';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    CropDocPageRoutingModule,
    SharedModule
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  declarations: [CropDocPage, ImageCropperCustomComponent]
})
export class CropDocPageModule {}
