<key>NSCameraUsageDescription</key>
<string>Need access to the camera</string>
<key>NSPhotoLibraryUsageDescription</key>
<string>Need access to the photo library</string>
<key>NSPhotoLibraryAddUsageDescription</key>
<string>Need access to add photos to the library</string>

<key>NSAppTransportSecurity</key>
<dict>
    <key>NSAllowsArbitraryLoads</key>
    <true/>
</dict>


# npm run build --prod
# npx cap sync ios
# npx cap open ios


npx ionic build --prod
npm install @capacitor/core@latest
npm install @capacitor/ios  
npx cap add ios  
npx cap sync ios
npx cap open ios 
# npm run build --prod
# npx cap sync android
# npx cap open android
