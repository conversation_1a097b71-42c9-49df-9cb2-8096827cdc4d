import type { CapacitorConfig } from '@capacitor/cli';

const config: CapacitorConfig = {
  appId: 'com.sophatel.ocr_grossiste_document',
  appName: 'WinDoc',
  webDir: 'www',
  bundledWebRuntime: false,
  plugins: {
    Camera: {
      iosUsageDescription: 'Need access to the camera for taking photos',
      android: {
        allowPinchZoom: true,
        shouldShowPreviewBeforeCapture: true
      },
      Permissions: {
      camera: "prompt"
    }
    },
    
  },
  
};

export default config;
