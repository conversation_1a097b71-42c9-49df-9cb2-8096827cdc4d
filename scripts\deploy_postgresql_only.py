#!/usr/bin/env python3
"""
PostgreSQL-Only Deployment Script

This script helps deploy the simplified PostgreSQL-only architecture.
It performs all necessary checks and setup steps.

Usage:
    python scripts/deploy_postgresql_only.py [--environment=local|prod]
"""

import sys
import os
import argparse
import logging
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def check_environment_file(env_type):
    """Check if environment file exists and is properly configured."""
    env_file = f".env.{env_type}"
    
    if not Path(env_file).exists():
        logger.error(f"❌ Environment file {env_file} not found")
        logger.info(f"💡 Create it by copying: cp .env.template {env_file}")
        return False
    
    # Check for old dual database settings
    with open(env_file, 'r') as f:
        content = f.read()
        
    if 'USE_DUAL_DATABASE' in content or 'PRIMARY_DATABASE' in content:
        logger.warning(f"⚠️  {env_file} contains old dual database settings")
        logger.info("💡 Please remove these lines:")
        logger.info("   USE_DUAL_DATABASE=...")
        logger.info("   PRIMARY_DATABASE=...")
        return False
    
    logger.info(f"✅ Environment file {env_file} looks good")
    return True


def test_database_connection():
    """Test PostgreSQL database connection."""
    try:
        from src.app.utils.db_operations import get_database_health
        
        health = get_database_health()
        if health.get('available'):
            logger.info("✅ PostgreSQL connection successful")
            return True
        else:
            logger.error(f"❌ PostgreSQL connection failed: {health.get('error')}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Database connection test failed: {e}")
        return False


def initialize_database():
    """Initialize PostgreSQL database tables."""
    try:
        from src.app.database.connection import init_db
        
        init_db()
        logger.info("✅ Database tables initialized successfully")
        return True
        
    except Exception as e:
        logger.error(f"❌ Database initialization failed: {e}")
        return False


def verify_simplified_architecture():
    """Verify that the simplified architecture is working."""
    try:
        # Test database operations
        from src.app.database.operations import db_ops
        
        health = db_ops.get_database_health()
        if health.get('available'):
            logger.info("✅ Simplified PostgreSQL-only architecture is working")
            return True
        else:
            logger.error("❌ Simplified architecture verification failed")
            return False
            
    except Exception as e:
        logger.error(f"❌ Architecture verification failed: {e}")
        return False


def show_deployment_commands(env_type):
    """Show the commands to start the application."""
    port = "8088" if env_type == "local" else "8089"
    
    logger.info("🚀 Deployment Commands:")
    logger.info("=" * 50)
    logger.info(f"Start {env_type} application:")
    logger.info(f"uvicorn src.api:app --host 0.0.0.0 --port {port} --log-config=logging.yaml --env-file .env.{env_type}")
    print()
    logger.info("Test health endpoints:")
    logger.info(f"curl http://localhost:{port}/health")
    logger.info(f"curl http://localhost:{port}/health/database")
    logger.info(f"curl http://localhost:{port}/health/detailed")


def main():
    """Main deployment function."""
    parser = argparse.ArgumentParser(description='Deploy PostgreSQL-only architecture')
    parser.add_argument('--environment', choices=['local', 'prod'], default='local',
                       help='Environment to deploy (local or prod)')
    
    args = parser.parse_args()
    env_type = args.environment
    
    logger.info("🚀 PostgreSQL-Only Deployment Script")
    logger.info("=" * 60)
    logger.info(f"Environment: {env_type}")
    print()
    
    # Step 1: Check environment file
    logger.info("📋 Step 1: Checking environment configuration...")
    if not check_environment_file(env_type):
        logger.error("❌ Environment check failed")
        sys.exit(1)
    
    # Step 2: Test database connection
    logger.info("\n🔗 Step 2: Testing database connection...")
    if not test_database_connection():
        logger.error("❌ Database connection failed")
        logger.info("💡 Troubleshooting:")
        logger.info("   - Check if PostgreSQL is running")
        logger.info("   - Verify credentials in .env file")
        logger.info("   - For local dev: docker-compose -f docker-compose.dev.yml up -d")
        sys.exit(1)
    
    # Step 3: Initialize database
    logger.info("\n🏗️  Step 3: Initializing database...")
    if not initialize_database():
        logger.error("❌ Database initialization failed")
        sys.exit(1)
    
    # Step 4: Verify architecture
    logger.info("\n✅ Step 4: Verifying simplified architecture...")
    if not verify_simplified_architecture():
        logger.error("❌ Architecture verification failed")
        sys.exit(1)
    
    # Step 5: Show deployment commands
    print("\n" + "=" * 60)
    logger.info("🎉 PostgreSQL-Only Deployment Ready!")
    print()
    show_deployment_commands(env_type)

    print("\n📊 Architecture Summary:")
    logger.info("✅ Single PostgreSQL database")
    logger.info("✅ Simplified operations")
    logger.info("✅ Better performance")
    logger.info("✅ Easier maintenance")
    logger.info("✅ Cloud ready")


if __name__ == "__main__":
    main()
