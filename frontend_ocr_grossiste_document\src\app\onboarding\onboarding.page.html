<div class="fix-wrapper">
  <ion-header>
  
  </ion-header>
  
  <ion-content fullscreen>
    <div class="onboarding-wrapper">
      <swiper-container #swiper pagination="true">
        <swiper-slide *ngFor="let slide of slidesData" class="test">
          <ion-row>
            <ion-col size="12" class="slide-content">
              <img [src]="slide.image" class="slide-image" />
              <div class="content-slide">
                <h2>{{ slide.title }}</h2>
                <p>{{ slide.description }}</p>
              </div>
            </ion-col>
          </ion-row>
        </swiper-slide>
      </swiper-container>
  
  
    </div>
  </ion-content>
    <ion-footer>
        <div class="buttons_nav">
          <ion-button fill="solid" class="next-button" (click)="next()">
            <span>SUIVANT</span>
            <ion-icon slot="end" name="arrow-forward-outline"></ion-icon>
          </ion-button>
          <ion-button fill="clear" class="skip-button" (click)="skip()">PASSER</ion-button>
        </div>
      </ion-footer>
</div>