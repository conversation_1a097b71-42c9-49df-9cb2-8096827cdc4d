// image-cropper-custom.component.ts
import {
  Component,
  Input,
  Output,
  EventEmitter,
  ElementRef,
  ViewChild,
  OnInit,
  HostListener,
  ChangeDetectorRef,
  ViewEncapsulation,
} from '@angular/core';
import { Coordinates } from '../../../models/coordinates';

interface PointerPosition {
  clientX: number;
  clientY: number;
}

@Component({
  selector: 'app-image-cropper-custom',
  templateUrl: './image-cropper-custom.component.html',
  styleUrls: ['./image-cropper-custom.component.scss'], // Use SCSS if needed
  encapsulation: ViewEncapsulation.None, // Remove style encapsulation
})
export class ImageCropperCustomComponent implements OnInit {
  @Input() width: string = '100%';
  @Input() height: string = '100%';
  // @Input() rotation: number = 0;
  @ViewChild('svgElement') svgElement!: ElementRef;
  @Output() coordinatesChange = new EventEmitter<Coordinates[]>();

  @HostListener('pointerup')
  @HostListener('pointercancel')
  onPointerEnd() {
    this.stopDragging();
  }

  @HostListener('pointermove', ['$event'])
  onPointerMove(event: PointerEvent) {
    if (!this.isDragging) return;

    event.preventDefault();
    event.stopPropagation();

    this.handleMove(event);
  }

  @HostListener('touchstart', ['$event'])
  onTouchStart(event: TouchEvent) {
    if (event.touches.length !== 1) return; // Only handle single touches

    event.preventDefault();
    event.stopPropagation();

    const touch = event.touches[0];
    const element = document.elementFromPoint(touch.clientX, touch.clientY);

    if (element?.classList.contains('handle-touch-area')) {
      const index = parseInt(element.getAttribute('data-index') || '0', 10);
      this.onPointTouchStart(event, index);
    }
  }

  @HostListener('touchmove', ['$event'])
  onTouchMove(event: TouchEvent) {
    if (!this.isDragging || event.touches.length !== 1) return;

    event.preventDefault();
    event.stopPropagation();

    const touch = event.touches[0];
    this.handleMove(touch);
  }

  @HostListener('touchend')
  onTouchEnd() {
    this.stopDragging();
  }

  @HostListener('document:mousemove', ['$event'])
  onDocumentMouseMove(event: MouseEvent) {
    this.handleMove(event);
  }

  // In image-cropper-custom.component.ts
  @Input() set imageUrl(url: string) {
    if (url) {
      const img = new Image();
      img.onload = () => {
        this.img = img;
        this.currentWidth = img.naturalWidth;
        this.currentHeight = img.naturalHeight;

        // Apply pending coordinates if they exist
        if (this._pendingCoordinates) {
          this.initialCoordinates = this._pendingCoordinates;
          this._pendingCoordinates = null;
        } else {
          this.initializeDefaultPoints();
        }

        this.cdRef.detectChanges();
      };
      img.src = url;
    }
  }

  @Input() set initialCoordinates(coords: Coordinates[]) {
    if (!coords || coords.length === 0) {
      this.initializeDefaultPoints();
      return;
    }
  
    const validCoords = coords.slice(0, 4).map((coord) => ({
      x: typeof coord.x === 'number' ? coord.x : 0,
      y: typeof coord.y === 'number' ? coord.y : 0,
    }));
  
    while (validCoords.length < 4) {
      validCoords.push({ x: 0, y: 0 });
    }
  
    if (this.currentWidth > 0) {
      this.quad.points = validCoords.map((point) => {
        const x = this.convertCoordinate(point.x, this.currentWidth);
        const y = this.convertCoordinate(point.y, this.currentHeight);
        return this.clampCoordinates({ x, y });
      });
      this.calculateMidpoints(); // Add this line to calculate midpoints
      this.cdRef.detectChanges();
    } else {
      this._pendingCoordinates = validCoords;
    }
  }

  @Input() set rotation(value: number) {
    if (this._rotation !== value) {
      const deltaAngle = value - (this._rotation || 0);
      this._rotation = value;

      if (this.quad.points && this.validateCoordinates(this.quad.points)) {
        this.rotateCoordinates(deltaAngle);
      }

      this.cdRef.detectChanges();
    }
  }

  private _rotation: number = 0;

  private _pendingCoordinates: Coordinates[] | null = null;

  private convertCoordinate(value: number, max: number): number {
    return value <= 1 ? value * max : value;
  }

  img: HTMLImageElement | null = null;
  quad: { points: Coordinates[] } = { points: [] };
  currentWidth: number = 0;
  currentHeight: number = 0;
  activePoint: number | null = null;
  isDragging = false;
  isMovingCropArea = false;
  lastMousePosition: Coordinates | null = null;

  private touchStartPos: Coordinates | null = null;
  private lastTouchPos: Coordinates | null = null;
  isTouchMoving = false;
  touchActive = false;
  private isTouchHandled = false;

  showMagnifier = false;
  magnifierPosition = { x: 0, y: 0 };
  magnifierRadius = 350; // Radius of the magnifier
  zoomFactor = 4; // How much to zoom in

  midpoints: Coordinates[] = []; // Array to store midpoint coordinates
  activeMidpoint: number | null = null; // Track the active midpoint being dragged
  midpointDragConstraint: 'horizontal' | 'vertical' | null = null; // Constraint for midpoint movement

  constructor(public cdRef: ChangeDetectorRef) {}

  ngOnInit() {
    if (!this.quad.points.length) {
      this.initializeDefaultPoints();
    } else {
      this.validateQuad();
    }
  }

  get rotation(): number {
    return this._rotation || 0;
  }

  private initializeDefaultPoints() {
    if (this.currentWidth > 0 && this.currentHeight > 0) {
      const margin = Math.min(
        50,
        Math.min(this.currentWidth, this.currentHeight) / 10
      );
      this.quad.points = [
        { x: margin, y: margin },
        { x: this.currentWidth - margin, y: margin },
        { x: this.currentWidth - margin, y: this.currentHeight - margin },
        { x: margin, y: this.currentHeight - margin },
      ].map((point) => this.clampCoordinates(point));
      this.calculateMidpoints(); // Add this line
    }
  }

  // handleTouchMove(event: TouchEvent) {
  //   if (!this.isDragging && !this.isMovingCropArea) return;

  //   event.preventDefault();
  //   const touch = event.touches[0];
  //   const pos = this.getTouchPosition(touch);

  //   if (this.isDragging && this.activePoint !== null) {
  //     this.quad.points[this.activePoint] = pos;
  //   } else if (this.isMovingCropArea) {
  //     const delta = {
  //       x: pos.x - (this.lastTouchPos?.x || 0),
  //       y: pos.y - (this.lastTouchPos?.y || 0)
  //     };

  //     this.quad.points = this.quad.points.map(p => ({
  //       x: p.x + delta.x,
  //       y: p.y + delta.y
  //     }));
  //   }

  //   this.lastTouchPos = pos;
  // }

  getTouchPosition(event: Touch | MouseEvent): Coordinates {
    const svg = this.svgElement.nativeElement;
    const CTM = svg.getScreenCTM();
    if (!CTM) return { x: 0, y: 0 };

    const clientX = 'touches' in event ? event.clientX : event.clientX;
    const clientY = 'touches' in event ? event.clientY : event.clientY;

    return {
      x: (clientX - CTM.e) / CTM.a,
      y: (clientY - CTM.f) / CTM.d,
    };
  }

  set inactiveSelections(value: any[]) {
    // Maintain compatibility with original component
  }

  private calculateRotatedDimensions(): { width: number, height: number } {
    const angle = Math.abs(this._rotation % 180); // Normalize angle to 0-180
    const rad = (angle * Math.PI) / 180;
    const cos = Math.cos(rad);
    const sin = Math.sin(rad);
  
    // Calculate rotated dimensions
    let rotatedWidth = Math.abs(this.currentWidth * cos) + Math.abs(this.currentHeight * sin);
    let rotatedHeight = Math.abs(this.currentWidth * sin) + Math.abs(this.currentHeight * cos);
  
    // If image is vertical (height > width)
    if (this.currentHeight > this.currentWidth) {
      if (angle > 45 && angle < 135) {
        // When rotated horizontally (closer to 90 degrees)
        // Adjust the dimensions to maintain aspect ratio
        const scale = this.currentHeight / this.currentWidth;
        rotatedWidth = Math.min(rotatedWidth, this.currentHeight);
        rotatedHeight = rotatedWidth / scale;
      } else {
        // When vertical or close to vertical, use original dimensions
        rotatedWidth = this.currentWidth;
        rotatedHeight = this.currentHeight;
      }
    }
  
    return {
      width: rotatedWidth,
      height: rotatedHeight
    };
  }
  
  getViewBox(): string {
    if (!this.currentWidth || !this.currentHeight) {
      return '0 0 800 600';
    }
  
    const { width, height } = this.calculateRotatedDimensions();
  
    // Calculate the center point
    const centerX = this.currentWidth / 2;
    const centerY = this.currentHeight / 2;
  
    // Calculate the viewBox dimensions
    const viewBoxWidth = width;
    const viewBoxHeight = height;
  
    // Calculate the offset to center the content
    const offsetX = centerX - viewBoxWidth / 2;
    const offsetY = centerY - viewBoxHeight / 2;
  
    return `${offsetX} ${offsetY} ${viewBoxWidth} ${viewBoxHeight}`;
  }
  
  // Update clampCoordinates to use the new dimensions
  private clampCoordinates(coord: Coordinates): Coordinates {
    const { width, height } = this.calculateRotatedDimensions();
    const centerX = this.currentWidth / 2;
    const centerY = this.currentHeight / 2;
  
    // Calculate bounds based on rotated dimensions
    const minX = centerX - width / 2;
    const maxX = centerX + width / 2;
    const minY = centerY - height / 2;
    const maxY = centerY + height / 2;
  
    return {
      x: Math.max(minX, Math.min(maxX, coord.x)),
      y: Math.max(minY, Math.min(maxY, coord.y))
    };
  }

  getRotationTransform(): string {
    const centerX = this.currentWidth / 2;
    const centerY = this.currentHeight / 2;
    return `rotate(${this.rotation} ${centerX} ${centerY})`;
  }

  getPolygonPoints(): string {
    return this.quad.points.map((point) => `${point.x},${point.y}`).join(' ');
  }

  getOverlayPath(): string {
    const points = this.quad.points;
    const width = this.currentWidth;
    const height = this.currentHeight;

    if (!points || points.length < 4) {
      return `M 0,0 H ${width} V ${height} H 0 Z`;
    }

    return `
      M 0,0 H ${width} V ${height} H 0 Z
      M ${points[0].x},${points[0].y}
      L ${points[1].x},${points[1].y}
      L ${points[2].x},${points[2].y}
      L ${points[3].x},${points[3].y} Z
    `;
  }

  getCropTransform(): string {
    return ''; // For additional transformations if needed
  }

  getMousePosition(event: MouseEvent): Coordinates {
    const svg = this.svgElement.nativeElement;
    const CTM = svg.getScreenCTM();
    if (!CTM) return { x: 0, y: 0 };

    return {
      x: (event.clientX - CTM.e) / CTM.a,
      y: (event.clientY - CTM.f) / CTM.d,
    };
  }

  startDragging(event: PointerEvent, index: number) {
    if (this.isTouchHandled) return;

    event.preventDefault();
    event.stopPropagation();

    this.isTouchHandled = true;
    this.isDragging = true;
    this.activePoint = index;
    this.showMagnifier = true;

    const coords = this.getSVGCoordinates(event);
    this.updateMagnifierPosition(coords);

    if (event.pointerType === 'touch') {
      this.lastTouchPos = this.clampCoordinates(coords);
    } else {
      this.lastMousePosition = this.clampCoordinates(coords);
    }

    this.cdRef.detectChanges();
  }

  startMovingCropArea(event: MouseEvent | TouchEvent) {
    event.preventDefault();
    this.isMovingCropArea = true;

    if (event instanceof TouchEvent) {
      this.lastMousePosition = this.getSVGCoordinates(event.touches[0]);
    } else {
      this.lastMousePosition = this.getSVGCoordinates(event);
    }
  }

  onPointTouchStart(event: TouchEvent, index: number) {
    console.log('onPointTouchStart', event, index);

    if (this.isTouchHandled) return; // Prevent multiple triggers

    event.preventDefault();
    event.stopPropagation();

    this.isTouchHandled = true;
    this.touchActive = true;
    this.isDragging = true;
    this.activePoint = index;

    const touch = event.touches[0];
    this.lastTouchPos = this.getSVGCoordinates(touch);
    this.cdRef.detectChanges();
  }

  handleTouchMove(event: TouchEvent) {
    event.preventDefault();
    event.stopPropagation();

    if (!this.isDragging && !this.isMovingCropArea) return;

    const touch = event.touches[0];
    if (this.isDragging && this.activePoint !== null) {
      const currentPos = this.getSVGCoordinates(touch);

      // Direct position update for more responsive feeling
      this.quad.points[this.activePoint] = currentPos;
      this.lastTouchPos = currentPos;

      // Force change detection
      this.cdRef.detectChanges();
    }
  }

  onPolygonTouchStart(event: TouchEvent) {
    event.preventDefault();
    event.stopPropagation();
    // this.startMovingCropArea(event);
  }

  handleMouseMove(event: MouseEvent) {
    this.handleMove(event);
  }

  private handleStart(event: PointerPosition, index: number) {
    this.activePoint = index;
    this.isDragging = true;
  }

  // private handleAreaStart(event: PointerPosition) {
  //   this.isMovingCropArea = true;
  //   this.lastMousePosition = this.getPosition(event);
  // }

  private handleMove(input: MouseEvent | Touch) {
    if (!this.isDragging) return;
  
    const currentPos = this.getSVGCoordinates(input);
    let clampedPos = this.clampCoordinates(currentPos);
  
    if (this.activePoint !== null) {
      const newPoints = [...this.quad.points];
      newPoints[this.activePoint] = clampedPos;
  
      if (this.isValidQuadrilateral(newPoints)) {
        this.quad.points[this.activePoint] = clampedPos;
        this.calculateMidpoints(); // Recalculate midpoints after corner move
        this.updateMagnifierPosition(clampedPos);
      }
    } else if (this.activeMidpoint !== null) {
      const midpoint = this.midpoints[this.activeMidpoint];
      if (this.midpointDragConstraint === 'horizontal') {
        clampedPos.y = midpoint.y; // Lock y-axis
      } else if (this.midpointDragConstraint === 'vertical') {
        clampedPos.x = midpoint.x; // Lock x-axis
      }
      this.updateCornerPointsFromMidpoint(this.activeMidpoint, clampedPos);
      this.calculateMidpoints(); // Recalculate midpoints after move
      this.updateMagnifierPosition(clampedPos);
    }
  
    this.lastMousePosition = clampedPos;
    this.cdRef.detectChanges();
  }

  private isValidQuadrilateral(points: Coordinates[]): boolean {
    if (points.length !== 4) return false;
  
    // Minimum distance between points (in pixels)
    const minDistance = 20;
  
    // Check minimum distance between all points
    for (let i = 0; i < points.length; i++) {
      for (let j = i + 1; j < points.length; j++) {
        const distance = this.getDistance(points[i], points[j]);
        if (distance < minDistance) {
          return false;
        }
      }
    }
  
    // Check if points form a convex quadrilateral
    return this.isConvex(points);
  }
  
  private getDistance(p1: Coordinates, p2: Coordinates): number {
    const dx = p2.x - p1.x;
    const dy = p2.y - p1.y;
    return Math.sqrt(dx * dx + dy * dy);
  }
  
  private isConvex(points: Coordinates[]): boolean {
    if (points.length !== 4) return false;
  
    let sign = 0;
    for (let i = 0; i < points.length; i++) {
      const p1 = points[i];
      const p2 = points[(i + 1) % 4];
      const p3 = points[(i + 2) % 4];
  
      const crossProduct = (p2.x - p1.x) * (p3.y - p1.y) - 
                          (p2.y - p1.y) * (p3.x - p1.x);
  
      if (i === 0) {
        sign = Math.sign(crossProduct);
      } else if (Math.sign(crossProduct) !== sign && crossProduct !== 0) {
        return false;
      }
    }
    return true;
  }
  
  private arePointsWithinBounds(points: Coordinates[]): boolean {
    return points.every(point => 
      point.x >= 0 && 
      point.x <= this.currentWidth && 
      point.y >= 0 && 
      point.y <= this.currentHeight
    );
  }

  // Generic position getter that works with both touch and mouse events
  // private getPosition(event: MouseEvent | TouchEvent): Coordinates {
  //   const svg = this.svgElement.nativeElement;
  //   const CTM = svg.getScreenCTM();
  //   if (!CTM) return { x: 0, y: 0 };

  //   // 2) Extract clientX, clientY via our helper
  //   const { clientX, clientY } = this.getPointerPosition(event);

  //   return {
  //     x: (clientX - CTM.e) / CTM.a,
  //     y: (clientY - CTM.f) / CTM.d,
  //   };
  // }

  private getSVGCoordinates(input: MouseEvent | Touch): Coordinates {
    const svg = this.svgElement.nativeElement;
    const CTM = svg.getScreenCTM();
    if (!CTM) return { x: 0, y: 0 };

    let clientX: number;
    let clientY: number;

    if (input instanceof MouseEvent) {
      clientX = input.clientX;
      clientY = input.clientY;
    } else {
      clientX = input.clientX;
      clientY = input.clientY;
    }

    return {
      x: (clientX - CTM.e) / CTM.a,
      y: (clientY - CTM.f) / CTM.d,
    };
  }

  // The helper function
  private getPointerPosition(input: MouseEvent | Touch): {
    clientX: number;
    clientY: number;
  } {
    if (input instanceof MouseEvent) {
      // It's a MouseEvent
      return { clientX: input.clientX, clientY: input.clientY };
    } else {
      // It's a single Touch object
      return { clientX: input.clientX, clientY: input.clientY };
    }
  }

  stopDragging() {
    this.isDragging = false;
    this.activePoint = null;
    this.activeMidpoint = null; // Reset midpoint
    this.midpointDragConstraint = null; // Reset constraint
    this.lastMousePosition = null;
    this.touchActive = false;
    this.isTouchHandled = false;
    this.showMagnifier = false;
    this.cdRef.detectChanges();
  }

  async getQuad() {
    // If there's a rotation, we need to apply the inverse rotation to get the original coordinates
    if (this._rotation !== 0) {
      const centerX = this.currentWidth / 2;
      const centerY = this.currentHeight / 2;
      const rad = (-this._rotation * Math.PI) / 180; // Note the negative angle for inverse rotation
      const cos = Math.cos(rad);
      const sin = Math.sin(rad);

      const rotatedPoints = this.quad.points.map((point) => {
        // Translate to origin
        const dx = point.x - centerX;
        const dy = point.y - centerY;

        // Rotate
        const rotatedX = centerX + (dx * cos - dy * sin);
        const rotatedY = centerY + (dx * sin + dy * cos);

        return {
          x: Math.round(rotatedX),
          y: Math.round(rotatedY),
        };
      });

      return { points: rotatedPoints };
    }

    // If no rotation, return the points as is
    return {
      points: this.quad.points.map((point) => ({
        x: Math.round(point.x),
        y: Math.round(point.y),
      })),
    };
  }

  

  private validateQuad() {
    if (!this.quad.points) return;

    this.quad.points = this.quad.points.map((point) =>
      this.clampCoordinates(point)
    );
  }

  // In image-cropper-custom.component.ts
  rotateCoordinates(angle: number) {
    if (!this.quad.points || this.quad.points.length !== 4) {
      console.error('Invalid points array for rotation');
      return;
    }
  
    const centerX = this.currentWidth / 2;
    const centerY = this.currentHeight / 2;
    const rad = (angle * Math.PI) / 180;
    const cos = Math.cos(rad);
    const sin = Math.sin(rad);
  
    // Rotate corner points
    this.quad.points = this.quad.points.map((point) => {
      if (typeof point.x === 'undefined' || typeof point.y === 'undefined') {
        console.error('Invalid point coordinates:', point);
        return point;
      }
  
      // Translate point to origin
      const dx = point.x - centerX;
      const dy = point.y - centerY;
  
      // Rotate point
      const newX = centerX + (dx * cos - dy * sin);
      const newY = centerY + (dx * sin + dy * cos);
  
      // Clamp coordinates to image boundaries
      return this.clampCoordinates({
        x: newX,
        y: newY,
      });
    });
  
    // Recalculate midpoints after rotating corner points
    this.calculateMidpoints();
    this.cdRef.detectChanges();
  }

  private validateCoordinates(points: Coordinates[]): boolean {
    if (!points || points.length !== 4) {
      console.error('Invalid number of points:', points);
      return false;
    }

    return points.every((point, index) => {
      if (typeof point.x === 'undefined' || typeof point.y === 'undefined') {
        console.error(`Invalid coordinates at index ${index}:`, point);
        return false;
      }
      return true;
    });
  }

  getMagnifierTransform(): string {
    return ''; // No transform needed for the magnifier group
  }

  updateMagnifierPosition(point: Coordinates) {
    if (this.activePoint !== null) {
      const activePoint = this.quad.points[this.activePoint];

      // Apply rotation to the point coordinates
      const centerX = this.currentWidth / 2;
      const centerY = this.currentHeight / 2;
      const rotatedPoint = this.rotatePoint(
        activePoint,
        { x: centerX, y: centerY },
        this.rotation
      );

      const margin = this.magnifierRadius * 1.2;

      // Calculate position based on rotated coordinates
      let magnifierX = rotatedPoint.x;
      let magnifierY = rotatedPoint.y;

      if (rotatedPoint.y < this.currentHeight / 2) {
        magnifierY = rotatedPoint.y + margin;
      } else {
        magnifierY = rotatedPoint.y - margin;
      }

      if (rotatedPoint.x < this.currentWidth / 2) {
        magnifierX = rotatedPoint.x + margin;
      } else {
        magnifierX = rotatedPoint.x - margin;
      }

      this.magnifierPosition = {
        x: magnifierX,
        y: magnifierY,
      };

      this.cdRef.detectChanges();
    }
  }

  // Helper method to rotate a point
  private rotatePoint(
    point: Coordinates,
    center: Coordinates,
    angleDegrees: number
  ): Coordinates {
    const angleRadians = (angleDegrees * Math.PI) / 180;
    const cos = Math.cos(angleRadians);
    const sin = Math.sin(angleRadians);

    const dx = point.x - center.x;
    const dy = point.y - center.y;

    return {
      x: center.x + (dx * cos - dy * sin),
      y: center.y + (dx * sin + dy * cos),
    };
  }

  // Update the getZoomTransform method to center on the active point
  getZoomTransform(): string {
    if (this.activePoint === null) return '';

    const activePoint = this.quad.points[this.activePoint];
    const scale = this.zoomFactor;

    // Calculate center of rotation
    const centerX = this.currentWidth / 2;
    const centerY = this.currentHeight / 2;

    // Calculate offset to keep the active point centered in the magnifier
    const dx = this.magnifierPosition.x - activePoint.x * scale;
    const dy = this.magnifierPosition.y - activePoint.y * scale;

    // Include rotation in the transformation
    return `translate(${dx}, ${dy}) translate(${centerX * scale}, ${centerY * scale}) rotate(${this.rotation}) translate(${-centerX * scale}, ${-centerY * scale}) scale(${scale})`;
  }

  // getZoomTransform(): string {
  //   if (this.activePoint === null) return '';
  
  //   const activePoint = this.quad.points[this.activePoint];
  //   const scale = this.zoomFactor;
  //   const centerX = this.currentWidth / 2;
  //   const centerY = this.currentHeight / 2;
  //   const dx = this.magnifierPosition.x - activePoint.x * scale;
  //   const dy = this.magnifierPosition.y - activePoint.y * scale;
  
  //   return `translate(${dx}, ${dy}) translate(${centerX * scale}, ${centerY * scale}) rotate(${this.rotation}) translate(${-centerX * scale}, ${-centerY * scale}) scale(${scale})`;
  // }

  getRotatedIntersectionLines(): { vertical: any, horizontal: any } {
    if (this.activePoint === null) return { vertical: null, horizontal: null };
  
    const point = this.quad.points[this.activePoint];
    const lineLength = 50;
  
    // Calculate unrotated lines first
    const vertical = {
      x1: point.x,
      y1: point.y - lineLength,
      x2: point.x,
      y2: point.y + lineLength
    };
  
    const horizontal = {
      x1: point.x - lineLength,
      y1: point.y,
      x2: point.x + lineLength,
      y2: point.y
    };
  
    // No need to rotate the lines as they will be transformed by the parent group's transform
    return {
      vertical,
      horizontal
    };
  }
  
  private calculateMidpoints() {
    this.midpoints = [];
    for (let i = 0; i < 4; i++) {
      const p1 = this.quad.points[i];
      const p2 = this.quad.points[(i + 1) % 4];
      const midpoint = {
        x: (p1.x + p2.x) / 2,
        y: (p1.y + p2.y) / 2,
      };
      this.midpoints.push(midpoint);
    }
  }

  startMidpointDragging(event: PointerEvent, index: number) {
    event.preventDefault();
    event.stopPropagation();
    this.isDragging = true;
    this.activeMidpoint = index;
    this.showMagnifier = false;
  
    // Calculate the current angle of the edge
    const edgeAngle = this.getEdgeAngle(index);
    const normalizedAngle = Math.abs(edgeAngle % 180); // Normalize to 0-180°
  
    // Determine if the edge is more horizontal or vertical
    if (normalizedAngle < 45 || normalizedAngle > 135) {
      // Edge is more horizontal, so drag should be vertical
      this.midpointDragConstraint = 'vertical';
    } else {
      // Edge is more vertical, so drag should be horizontal
      this.midpointDragConstraint = 'horizontal';
    }
  
    const coords = this.getSVGCoordinates(event);
    this.updateMagnifierPosition(coords);
    this.lastMousePosition = this.clampCoordinates(coords);
    this.cdRef.detectChanges();
  }

  private updateCornerPointsFromMidpoint(midpointIndex: number, newPos: Coordinates) {
    const prevIndex = midpointIndex;
    const nextIndex = (midpointIndex + 1) % 4;
    const prevPoint = this.quad.points[prevIndex];
    const nextPoint = this.quad.points[nextIndex];
  
    if (this.midpointDragConstraint === 'horizontal') {
      prevPoint.x = newPos.x;
      nextPoint.x = newPos.x;
    } else if (this.midpointDragConstraint === 'vertical') {
      prevPoint.y = newPos.y;
      nextPoint.y = newPos.y;
    }
  }

  private getEdgeAngle(index: number): number {
    const p1 = this.quad.points[index];
    const p2 = this.quad.points[(index + 1) % 4];
    const dx = p2.x - p1.x;
    const dy = p2.y - p1.y;
    return Math.atan2(dy, dx) * (180 / Math.PI); // Angle in degrees
  }


  getMidpointWidth(index: number): number {
    // Top and bottom midpoints (0 and 2)
    if (index === 0 || index === 2) {
      return 250; // Wider for horizontal handles
    }
    // Left and right midpoints (1 and 3)
    return 70; // Narrower for vertical handles
  }
  
  getMidpointHeight(index: number): number {
    // Top and bottom midpoints (0 and 2)
    if (index === 0 || index === 2) {
      return 70; // Shorter for horizontal handles
    }
    // Left and right midpoints (1 and 3)
    return 250; // Taller for vertical handles
  }
  
  getMidpointX(midpoint: Coordinates, index: number): number {
    const { width } = this.getMidpointDimensions(index);
    return midpoint.x - width / 2;
  }
  
  getMidpointY(midpoint: Coordinates, index: number): number {
    const { height } = this.getMidpointDimensions(index);
    return midpoint.y - height / 2;
  }
  
  getMidpointRadius(index: number): number {
    return 35; // Consistent border radius for all handles
  }

  getMidpointDimensions(index: number): { width: number; height: number } {
    // Use your existing getEdgeAngle(index):
    const angle = Math.abs(this.getEdgeAngle(index) % 180);
    
    // If angle < 45° or > 135° => Edge is more "horizontal"
    // Else => Edge is more "vertical"
    if (angle < 45 || angle > 135) {
      // "Horizontal" handle
      return { width: 250, height: 70 };
    } else {
      // "Vertical" handle
      return { width: 70, height: 250 };
    }
  }
  
}
