-- Manual SQL Migration Script
-- Add src_app column to existing pre_bl_ocr table

-- Step 1: Add the src_app column with default value
ALTER TABLE pre_bl_ocr 
ADD COLUMN src_app VARCHAR(50) NOT NULL DEFAULT 'winpluspharma';

-- Step 2: Update any existing NULL values (if any)
UPDATE pre_bl_ocr 
SET src_app = 'winpluspharma' 
WHERE src_app IS NULL OR src_app = '';

-- Step 3: Create index for better performance
CREATE INDEX IF NOT EXISTS idx_pre_bl_ocr_src_app ON pre_bl_ocr(src_app);

-- Step 4: Verify the changes
SELECT 
    COUNT(*) as total_records,
    COUNT(CASE WHEN src_app = 'winpluspharma' THEN 1 END) as winplus_records,
    COUNT(CASE WHEN src_app = 'pharmalien' THEN 1 END) as pharmalien_records
FROM pre_bl_ocr;

-- Step 5: Check column information
SELECT column_name, data_type, column_default, is_nullable
FROM information_schema.columns 
WHERE table_name = 'pre_bl_ocr' AND column_name = 'src_app';
