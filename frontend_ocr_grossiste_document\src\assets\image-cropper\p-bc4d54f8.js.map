{"version": 3, "names": ["NAMESPACE", "scopeId", "hostTagName", "isSvgMode", "queuePending", "createTime", "fnName", "tagName", "uniqueTime", "key", "measureText", "HYDRATED_CSS", "SLOT_FB_CSS", "EMPTY_OBJ", "SVG_NS", "HTML_NS", "isDef", "v", "isComplexType", "o", "queryNonceMetaTagContent", "doc", "_a", "_b", "_c", "head", "querySelector", "getAttribute", "undefined", "h", "nodeName", "vnodeData", "children", "child", "simple", "lastSimple", "vNodeChildren", "walk", "c", "i", "length", "Array", "isArray", "String", "$text$", "push", "newVNode", "classData", "className", "class", "Object", "keys", "filter", "k", "join", "vdomFnUtils", "vnode", "$attrs$", "$children$", "$key$", "tag", "text", "$flags$", "$tag$", "$elm$", "Host", "isHost", "node", "for<PERSON>ach", "cb", "map", "convertToPublic", "convertToPrivate", "vattrs", "vchildren", "vkey", "vname", "$name$", "vtag", "vtext", "assign", "name", "parsePropertyValue", "propValue", "propType", "parseFloat", "getElement", "ref", "getHostRef", "createEvent", "flags", "elm", "emit", "detail", "emitEvent", "bubbles", "composed", "cancelable", "opts", "ev", "plt", "ce", "dispatchEvent", "rootAppliedStyles", "WeakMap", "registerStyle", "cssText", "allowCS", "style", "styles", "get", "supportsConstructableStylesheets", "CSSStyleSheet", "replaceSync", "set", "addStyle", "styleContainerNode", "cmpMeta", "mode", "getScopeId", "nodeType", "appliedStyles", "styleElm", "Set", "has", "createElement", "innerHTML", "nonce", "$nonce$", "setAttribute", "insertBefore", "add", "adoptedStyleSheets", "includes", "attachStyles", "hostRef", "$cmpMeta$", "$hostElement$", "endAttachStyles", "$tagName$", "shadowRoot", "getRootNode", "classList", "cmp", "setAccessor", "memberName", "oldValue", "newValue", "isSvg", "isProp", "isMemberInElement", "ln", "toLowerCase", "oldClasses", "parseClassList", "newClasses", "remove", "prop", "removeProperty", "setProperty", "slice", "win", "capture", "endsWith", "CAPTURE_EVENT_SUFFIX", "replace", "CAPTURE_EVENT_REGEX", "rel", "ael", "isComplex", "n", "e", "removeAttribute", "parseClassListRegex", "value", "split", "RegExp", "updateElement", "oldVnode", "newVnode", "host", "oldVnodeAttrs", "newVnodeAttrs", "createElm", "oldParentVNode", "newParentVNode", "childIndex", "parentElm", "childNode", "createTextNode", "createElementNS", "append<PERSON><PERSON><PERSON>", "addVnodes", "before", "parentVNode", "vnodes", "startIdx", "endIdx", "containerElm", "removeVnodes", "index", "nullifyVNodeRefs", "update<PERSON><PERSON><PERSON>n", "oldCh", "newCh", "isInitialRender", "oldStartIdx", "newStartIdx", "idxInOld", "oldEndIdx", "oldStartVnode", "oldEndVnode", "newEndIdx", "newStartVnode", "newEndVnode", "elmToMove", "isSameVnode", "patch", "nextS<PERSON>ling", "parentNode", "leftVNode", "rightVNode", "oldVNode", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "textContent", "data", "vNode", "renderVdom", "renderFnResults", "isInitialLoad", "hostElm", "$vnode$", "rootVnode", "hasAttribute", "attachToAncestor", "ancestorComponent", "$onRenderResolve$", "Promise", "r", "scheduleUpdate", "$ancestorComponent$", "dispatch", "dispatchHooks", "writeTask", "endSchedule", "instance", "$lazyInstance$", "<PERSON><PERSON><PERSON><PERSON>", "enqueue", "updateComponent", "fn", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "then", "async", "endUpdate", "rc", "endRender", "callRender", "childrenPromises", "postUpdate", "postUpdateComponent", "all", "render", "consoleError", "endPostUpdate", "addHydratedFlag", "safeCall", "$onReadyResolve$", "appDidLoad", "$onInstanceResolve$", "nextTick", "who", "documentElement", "namespace", "method", "arg", "getValue", "propName", "$instanceValues$", "setValue", "newVal", "oldVal", "$members$", "areBothNaN", "Number", "isNaN", "didValueChange", "$watchers$", "watchMethods", "watchMethodName", "proxyComponent", "Cstr", "prototype", "watchers", "members", "entries", "memberFlags", "defineProperty", "this", "configurable", "enumerable", "args", "$onInstancePromise$", "attrNameToPropName", "Map", "attributeChangedCallback", "attrName", "jmp", "hasOwnProperty", "entry", "callback<PERSON><PERSON>", "call", "observedAttributes", "from", "_", "m", "initializeComponent", "hmrVersionId", "loadModule", "endLoad", "isProxied", "endNewInstance", "endRegisterStyles", "schedule", "fireConnectedCallback", "connectedCallback", "endConnected", "$onReadyPromise$", "disconnectInstance", "disconnectedCallback", "bootstrapLazy", "lazyB<PERSON>les", "options", "endBootstrap", "cmpTags", "exclude", "customElements", "metaCharset", "dataStyles", "deferredConnectedCallbacks", "appLoadFallback", "isBootstrapping", "$resourcesUrl$", "URL", "resourcesUrl", "baseURI", "href", "hasSlotRelocation", "lazyBundle", "compactMeta", "$listeners$", "HostElement", "HTMLElement", "constructor", "self", "super", "registerHost", "attachShadow", "clearTimeout", "componentOnReady", "$lazyBundleId$", "define", "<PERSON><PERSON><PERSON><PERSON>", "setTimeout", "Fragment", "setNonce", "hostRefs", "registerInstance", "lazyInstance", "hostElement", "el", "console", "error", "cmpModules", "exportName", "bundleId", "module", "import", "importedModule", "window", "document", "raf", "requestAnimationFrame", "eventName", "listener", "addEventListener", "removeEventListener", "CustomEvent", "promiseResolve", "resolve", "queueDomReads", "queueDomWrites", "queueTask", "queue", "write", "flush", "consume", "performance", "now"], "sources": ["@stencil/core/internal/app-data", "node_modules/@stencil/core/internal/client/index.js?app-data=conditional"], "sourcesContent": ["export const NAMESPACE = 'image-cropper-component';\nexport const BUILD = /* image-cropper-component */ { allRenderFn: true, appendChildSlotFix: false, asyncLoading: true, asyncQueue: false, attachStyles: true, cloneNodeFix: false, cmpDidLoad: true, cmpDidRender: false, cmpDidUnload: false, cmpDidUpdate: false, cmpShouldUpdate: false, cmpWillLoad: false, cmpWillRender: false, cmpWillUpdate: false, connectedCallback: false, constructableCSS: true, cssAnnotations: true, devTools: false, disconnectedCallback: false, element: false, event: true, experimentalScopedSlotChanges: false, experimentalSlotFixes: false, formAssociated: false, hasRenderFn: true, hostListener: false, hostListenerTarget: false, hostListenerTargetBody: false, hostListenerTargetDocument: false, hostListenerTargetParent: false, hostListenerTargetWindow: false, hotModuleReplacement: false, hydrateClientSide: false, hydrateServerSide: false, hydratedAttribute: false, hydratedClass: true, initializeNextTick: false, invisiblePrehydration: true, isDebug: false, isDev: false, isTesting: false, lazyLoad: true, lifecycle: true, lifecycleDOMEvents: false, member: true, method: true, mode: false, observeAttribute: true, profile: false, prop: true, propBoolean: false, propMutable: false, propNumber: true, propString: true, reflect: false, scoped: false, scopedSlotTextContentFix: false, scriptDataOpts: false, shadowDelegatesFocus: false, shadowDom: true, slot: true, slotChildNodesFix: false, slotRelocation: false, state: true, style: true, svg: true, taskQueue: true, transformTagName: false, updatable: true, vdomAttribute: true, vdomClass: true, vdomFunctional: true, vdomKey: true, vdomListener: true, vdomPropOrAttr: true, vdomRef: true, vdomRender: true, vdomStyle: true, vdomText: true, vdomXlink: false, watchCallback: true };\nexport const Env = /* image-cropper-component */ {};\n", "/**\n * Virtual DOM patching algorithm based on Snabbdom by\n * <PERSON> (@paldepind)\n * Licensed under the MIT License\n * https://github.com/snabbdom/snabbdom/blob/master/LICENSE\n *\n * Modified for Stencil's renderer and slot projection\n */\nlet scopeId;\nlet contentRef;\nlet hostTagName;\nlet customError;\nlet i = 0;\nlet useNativeShadowDom = false;\nlet checkSlotFallbackVisibility = false;\nlet checkSlotRelocate = false;\nlet isSvgMode = false;\nlet renderingRef = null;\nlet queueCongestion = 0;\nlet queuePending = false;\n/*\n Stencil Client Platform v4.12.0 | MIT Licensed | https://stenciljs.com\n */\nimport { BUILD, NAMESPACE } from '@stencil/core/internal/app-data';\nconst Build = {\n    isDev: BUILD.isDev ? true : false,\n    isBrowser: true,\n    isServer: false,\n    isTesting: BUILD.isTesting ? true : false,\n};\nconst getAssetPath = (path) => {\n    const assetUrl = new URL(path, plt.$resourcesUrl$);\n    return assetUrl.origin !== win.location.origin ? assetUrl.href : assetUrl.pathname;\n};\nconst setAssetPath = (path) => (plt.$resourcesUrl$ = path);\nconst createTime = (fnName, tagName = '') => {\n    if (BUILD.profile && performance.mark) {\n        const key = `st:${fnName}:${tagName}:${i++}`;\n        // Start\n        performance.mark(key);\n        // End\n        return () => performance.measure(`[Stencil] ${fnName}() <${tagName}>`, key);\n    }\n    else {\n        return () => {\n            return;\n        };\n    }\n};\nconst uniqueTime = (key, measureText) => {\n    if (BUILD.profile && performance.mark) {\n        if (performance.getEntriesByName(key, 'mark').length === 0) {\n            performance.mark(key);\n        }\n        return () => {\n            if (performance.getEntriesByName(measureText, 'measure').length === 0) {\n                performance.measure(measureText, key);\n            }\n        };\n    }\n    else {\n        return () => {\n            return;\n        };\n    }\n};\nconst inspect = (ref) => {\n    const hostRef = getHostRef(ref);\n    if (!hostRef) {\n        return undefined;\n    }\n    const flags = hostRef.$flags$;\n    const hostElement = hostRef.$hostElement$;\n    return {\n        renderCount: hostRef.$renderCount$,\n        flags: {\n            hasRendered: !!(flags & 2 /* HOST_FLAGS.hasRendered */),\n            hasConnected: !!(flags & 1 /* HOST_FLAGS.hasConnected */),\n            isWaitingForChildren: !!(flags & 4 /* HOST_FLAGS.isWaitingForChildren */),\n            isConstructingInstance: !!(flags & 8 /* HOST_FLAGS.isConstructingInstance */),\n            isQueuedForUpdate: !!(flags & 16 /* HOST_FLAGS.isQueuedForUpdate */),\n            hasInitializedComponent: !!(flags & 32 /* HOST_FLAGS.hasInitializedComponent */),\n            hasLoadedComponent: !!(flags & 64 /* HOST_FLAGS.hasLoadedComponent */),\n            isWatchReady: !!(flags & 128 /* HOST_FLAGS.isWatchReady */),\n            isListenReady: !!(flags & 256 /* HOST_FLAGS.isListenReady */),\n            needsRerender: !!(flags & 512 /* HOST_FLAGS.needsRerender */),\n        },\n        instanceValues: hostRef.$instanceValues$,\n        ancestorComponent: hostRef.$ancestorComponent$,\n        hostElement,\n        lazyInstance: hostRef.$lazyInstance$,\n        vnode: hostRef.$vnode$,\n        modeName: hostRef.$modeName$,\n        onReadyPromise: hostRef.$onReadyPromise$,\n        onReadyResolve: hostRef.$onReadyResolve$,\n        onInstancePromise: hostRef.$onInstancePromise$,\n        onInstanceResolve: hostRef.$onInstanceResolve$,\n        onRenderResolve: hostRef.$onRenderResolve$,\n        queuedListeners: hostRef.$queuedListeners$,\n        rmListeners: hostRef.$rmListeners$,\n        ['s-id']: hostElement['s-id'],\n        ['s-cr']: hostElement['s-cr'],\n        ['s-lr']: hostElement['s-lr'],\n        ['s-p']: hostElement['s-p'],\n        ['s-rc']: hostElement['s-rc'],\n        ['s-sc']: hostElement['s-sc'],\n    };\n};\nconst installDevTools = () => {\n    if (BUILD.devTools) {\n        const stencil = (win.stencil = win.stencil || {});\n        const originalInspect = stencil.inspect;\n        stencil.inspect = (ref) => {\n            let result = inspect(ref);\n            if (!result && typeof originalInspect === 'function') {\n                result = originalInspect(ref);\n            }\n            return result;\n        };\n    }\n};\nconst CONTENT_REF_ID = 'r';\nconst ORG_LOCATION_ID = 'o';\nconst SLOT_NODE_ID = 's';\nconst TEXT_NODE_ID = 't';\nconst HYDRATE_ID = 's-id';\nconst HYDRATED_STYLE_ID = 'sty-id';\nconst HYDRATE_CHILD_ID = 'c-id';\nconst HYDRATED_CSS = '{visibility:hidden}.hydrated{visibility:inherit}';\n/**\n * Constant for styles to be globally applied to `slot-fb` elements for pseudo-slot behavior.\n *\n * Two cascading rules must be used instead of a `:not()` selector due to Stencil browser\n * support as of Stencil v4.\n */\nconst SLOT_FB_CSS = 'slot-fb{display:contents}slot-fb[hidden]{display:none}';\nconst XLINK_NS = 'http://www.w3.org/1999/xlink';\nconst FORM_ASSOCIATED_CUSTOM_ELEMENT_CALLBACKS = [\n    'formAssociatedCallback',\n    'formResetCallback',\n    'formDisabledCallback',\n    'formStateRestoreCallback',\n];\n/**\n * Default style mode id\n */\n/**\n * Reusable empty obj/array\n * Don't add values to these!!\n */\nconst EMPTY_OBJ = {};\n/**\n * Namespaces\n */\nconst SVG_NS = 'http://www.w3.org/2000/svg';\nconst HTML_NS = 'http://www.w3.org/1999/xhtml';\nconst isDef = (v) => v != null;\n/**\n * Check whether a value is a 'complex type', defined here as an object or a\n * function.\n *\n * @param o the value to check\n * @returns whether it's a complex type or not\n */\nconst isComplexType = (o) => {\n    // https://jsperf.com/typeof-fn-object/5\n    o = typeof o;\n    return o === 'object' || o === 'function';\n};\n/**\n * Helper method for querying a `meta` tag that contains a nonce value\n * out of a DOM's head.\n *\n * @param doc The DOM containing the `head` to query against\n * @returns The content of the meta tag representing the nonce value, or `undefined` if no tag\n * exists or the tag has no content.\n */\nfunction queryNonceMetaTagContent(doc) {\n    var _a, _b, _c;\n    return (_c = (_b = (_a = doc.head) === null || _a === void 0 ? void 0 : _a.querySelector('meta[name=\"csp-nonce\"]')) === null || _b === void 0 ? void 0 : _b.getAttribute('content')) !== null && _c !== void 0 ? _c : undefined;\n}\n/**\n * Production h() function based on Preact by\n * Jason Miller (@developit)\n * Licensed under the MIT License\n * https://github.com/developit/preact/blob/master/LICENSE\n *\n * Modified for Stencil's compiler and vdom\n */\n// export function h(nodeName: string | d.FunctionalComponent, vnodeData: d.PropsType, child?: d.ChildType): d.VNode;\n// export function h(nodeName: string | d.FunctionalComponent, vnodeData: d.PropsType, ...children: d.ChildType[]): d.VNode;\nconst h = (nodeName, vnodeData, ...children) => {\n    let child = null;\n    let key = null;\n    let slotName = null;\n    let simple = false;\n    let lastSimple = false;\n    const vNodeChildren = [];\n    const walk = (c) => {\n        for (let i = 0; i < c.length; i++) {\n            child = c[i];\n            if (Array.isArray(child)) {\n                walk(child);\n            }\n            else if (child != null && typeof child !== 'boolean') {\n                if ((simple = typeof nodeName !== 'function' && !isComplexType(child))) {\n                    child = String(child);\n                }\n                else if (BUILD.isDev && typeof nodeName !== 'function' && child.$flags$ === undefined) {\n                    consoleDevError(`vNode passed as children has unexpected type.\nMake sure it's using the correct h() function.\nEmpty objects can also be the cause, look for JSX comments that became objects.`);\n                }\n                if (simple && lastSimple) {\n                    // If the previous child was simple (string), we merge both\n                    vNodeChildren[vNodeChildren.length - 1].$text$ += child;\n                }\n                else {\n                    // Append a new vNode, if it's text, we create a text vNode\n                    vNodeChildren.push(simple ? newVNode(null, child) : child);\n                }\n                lastSimple = simple;\n            }\n        }\n    };\n    walk(children);\n    if (vnodeData) {\n        if (BUILD.isDev && nodeName === 'input') {\n            validateInputProperties(vnodeData);\n        }\n        if (BUILD.vdomKey && vnodeData.key) {\n            key = vnodeData.key;\n        }\n        if (BUILD.slotRelocation && vnodeData.name) {\n            slotName = vnodeData.name;\n        }\n        // normalize class / className attributes\n        if (BUILD.vdomClass) {\n            const classData = vnodeData.className || vnodeData.class;\n            if (classData) {\n                vnodeData.class =\n                    typeof classData !== 'object'\n                        ? classData\n                        : Object.keys(classData)\n                            .filter((k) => classData[k])\n                            .join(' ');\n            }\n        }\n    }\n    if (BUILD.isDev && vNodeChildren.some(isHost)) {\n        consoleDevError(`The <Host> must be the single root component. Make sure:\n- You are NOT using hostData() and <Host> in the same component.\n- <Host> is used once, and it's the single root component of the render() function.`);\n    }\n    if (BUILD.vdomFunctional && typeof nodeName === 'function') {\n        // nodeName is a functional component\n        return nodeName(vnodeData === null ? {} : vnodeData, vNodeChildren, vdomFnUtils);\n    }\n    const vnode = newVNode(nodeName, null);\n    vnode.$attrs$ = vnodeData;\n    if (vNodeChildren.length > 0) {\n        vnode.$children$ = vNodeChildren;\n    }\n    if (BUILD.vdomKey) {\n        vnode.$key$ = key;\n    }\n    if (BUILD.slotRelocation) {\n        vnode.$name$ = slotName;\n    }\n    return vnode;\n};\n/**\n * A utility function for creating a virtual DOM node from a tag and some\n * possible text content.\n *\n * @param tag the tag for this element\n * @param text possible text content for the node\n * @returns a newly-minted virtual DOM node\n */\nconst newVNode = (tag, text) => {\n    const vnode = {\n        $flags$: 0,\n        $tag$: tag,\n        $text$: text,\n        $elm$: null,\n        $children$: null,\n    };\n    if (BUILD.vdomAttribute) {\n        vnode.$attrs$ = null;\n    }\n    if (BUILD.vdomKey) {\n        vnode.$key$ = null;\n    }\n    if (BUILD.slotRelocation) {\n        vnode.$name$ = null;\n    }\n    return vnode;\n};\nconst Host = {};\n/**\n * Check whether a given node is a Host node or not\n *\n * @param node the virtual DOM node to check\n * @returns whether it's a Host node or not\n */\nconst isHost = (node) => node && node.$tag$ === Host;\n/**\n * Implementation of {@link d.FunctionalUtilities} for Stencil's VDom.\n *\n * Note that these functions convert from {@link d.VNode} to\n * {@link d.ChildNode} to give functional component developers a friendly\n * interface.\n */\nconst vdomFnUtils = {\n    forEach: (children, cb) => children.map(convertToPublic).forEach(cb),\n    map: (children, cb) => children.map(convertToPublic).map(cb).map(convertToPrivate),\n};\n/**\n * Convert a {@link d.VNode} to a {@link d.ChildNode} in order to present a\n * friendlier public interface (hence, 'convertToPublic').\n *\n * @param node the virtual DOM node to convert\n * @returns a converted child node\n */\nconst convertToPublic = (node) => ({\n    vattrs: node.$attrs$,\n    vchildren: node.$children$,\n    vkey: node.$key$,\n    vname: node.$name$,\n    vtag: node.$tag$,\n    vtext: node.$text$,\n});\n/**\n * Convert a {@link d.ChildNode} back to an equivalent {@link d.VNode} in\n * order to use the resulting object in the virtual DOM. The initial object was\n * likely created as part of presenting a public API, so converting it back\n * involved making it 'private' again (hence, `convertToPrivate`).\n *\n * @param node the child node to convert\n * @returns a converted virtual DOM node\n */\nconst convertToPrivate = (node) => {\n    if (typeof node.vtag === 'function') {\n        const vnodeData = Object.assign({}, node.vattrs);\n        if (node.vkey) {\n            vnodeData.key = node.vkey;\n        }\n        if (node.vname) {\n            vnodeData.name = node.vname;\n        }\n        return h(node.vtag, vnodeData, ...(node.vchildren || []));\n    }\n    const vnode = newVNode(node.vtag, node.vtext);\n    vnode.$attrs$ = node.vattrs;\n    vnode.$children$ = node.vchildren;\n    vnode.$key$ = node.vkey;\n    vnode.$name$ = node.vname;\n    return vnode;\n};\n/**\n * Validates the ordering of attributes on an input element\n *\n * @param inputElm the element to validate\n */\nconst validateInputProperties = (inputElm) => {\n    const props = Object.keys(inputElm);\n    const value = props.indexOf('value');\n    if (value === -1) {\n        return;\n    }\n    const typeIndex = props.indexOf('type');\n    const minIndex = props.indexOf('min');\n    const maxIndex = props.indexOf('max');\n    const stepIndex = props.indexOf('step');\n    if (value < typeIndex || value < minIndex || value < maxIndex || value < stepIndex) {\n        consoleDevWarn(`The \"value\" prop of <input> should be set after \"min\", \"max\", \"type\" and \"step\"`);\n    }\n};\n/**\n * Entrypoint of the client-side hydration process. Facilitates calls to hydrate the\n * document and all its nodes.\n *\n * This process will also reconstruct the shadow root and slot DOM nodes for components using shadow DOM.\n *\n * @param hostElm The element to hydrate.\n * @param tagName The element's tag name.\n * @param hostId The host ID assigned to the element by the server.\n * @param hostRef The host reference for the element.\n */\nconst initializeClientHydrate = (hostElm, tagName, hostId, hostRef) => {\n    const endHydrate = createTime('hydrateClient', tagName);\n    const shadowRoot = hostElm.shadowRoot;\n    const childRenderNodes = [];\n    const slotNodes = [];\n    const shadowRootNodes = BUILD.shadowDom && shadowRoot ? [] : null;\n    const vnode = (hostRef.$vnode$ = newVNode(tagName, null));\n    if (!plt.$orgLocNodes$) {\n        initializeDocumentHydrate(doc.body, (plt.$orgLocNodes$ = new Map()));\n    }\n    hostElm[HYDRATE_ID] = hostId;\n    hostElm.removeAttribute(HYDRATE_ID);\n    clientHydrate(vnode, childRenderNodes, slotNodes, shadowRootNodes, hostElm, hostElm, hostId);\n    childRenderNodes.map((c) => {\n        const orgLocationId = c.$hostId$ + '.' + c.$nodeId$;\n        const orgLocationNode = plt.$orgLocNodes$.get(orgLocationId);\n        const node = c.$elm$;\n        // Put the node back in its original location since the native Shadow DOM\n        // can handle rendering it its correct location now\n        if (orgLocationNode && supportsShadow && orgLocationNode['s-en'] === '') {\n            orgLocationNode.parentNode.insertBefore(node, orgLocationNode.nextSibling);\n        }\n        if (!shadowRoot) {\n            node['s-hn'] = tagName;\n            if (orgLocationNode) {\n                node['s-ol'] = orgLocationNode;\n                node['s-ol']['s-nr'] = node;\n            }\n        }\n        plt.$orgLocNodes$.delete(orgLocationId);\n    });\n    if (BUILD.shadowDom && shadowRoot) {\n        shadowRootNodes.map((shadowRootNode) => {\n            if (shadowRootNode) {\n                shadowRoot.appendChild(shadowRootNode);\n            }\n        });\n    }\n    endHydrate();\n};\n/**\n * Recursively constructs the virtual node tree for a host element and its children.\n * The tree is constructed by parsing the annotations set on the nodes by the server.\n *\n * In addition to constructing the vNode tree, we also track information about the node's\n * descendants like which are slots, which should exist in the shadow root, and which\n * are nodes that should be rendered as children of the parent node.\n *\n * @param parentVNode The vNode representing the parent node.\n * @param childRenderNodes An array of all child nodes in the parent's node tree.\n * @param slotNodes An array of all slot nodes in the parent's node tree.\n * @param shadowRootNodes An array all nodes that should be rendered in the shadow root in the parent's node tree.\n * @param hostElm The parent element.\n * @param node The node to construct the vNode tree for.\n * @param hostId The host ID assigned to the element by the server.\n */\nconst clientHydrate = (parentVNode, childRenderNodes, slotNodes, shadowRootNodes, hostElm, node, hostId) => {\n    let childNodeType;\n    let childIdSplt;\n    let childVNode;\n    let i;\n    if (node.nodeType === 1 /* NODE_TYPE.ElementNode */) {\n        childNodeType = node.getAttribute(HYDRATE_CHILD_ID);\n        if (childNodeType) {\n            // got the node data from the element's attribute\n            // `${hostId}.${nodeId}.${depth}.${index}`\n            childIdSplt = childNodeType.split('.');\n            if (childIdSplt[0] === hostId || childIdSplt[0] === '0') {\n                childVNode = {\n                    $flags$: 0,\n                    $hostId$: childIdSplt[0],\n                    $nodeId$: childIdSplt[1],\n                    $depth$: childIdSplt[2],\n                    $index$: childIdSplt[3],\n                    $tag$: node.tagName.toLowerCase(),\n                    $elm$: node,\n                    $attrs$: null,\n                    $children$: null,\n                    $key$: null,\n                    $name$: null,\n                    $text$: null,\n                };\n                childRenderNodes.push(childVNode);\n                node.removeAttribute(HYDRATE_CHILD_ID);\n                // this is a new child vnode\n                // so ensure its parent vnode has the vchildren array\n                if (!parentVNode.$children$) {\n                    parentVNode.$children$ = [];\n                }\n                // add our child vnode to a specific index of the vnode's children\n                parentVNode.$children$[childVNode.$index$] = childVNode;\n                // this is now the new parent vnode for all the next child checks\n                parentVNode = childVNode;\n                if (shadowRootNodes && childVNode.$depth$ === '0') {\n                    shadowRootNodes[childVNode.$index$] = childVNode.$elm$;\n                }\n            }\n        }\n        // recursively drill down, end to start so we can remove nodes\n        for (i = node.childNodes.length - 1; i >= 0; i--) {\n            clientHydrate(parentVNode, childRenderNodes, slotNodes, shadowRootNodes, hostElm, node.childNodes[i], hostId);\n        }\n        if (node.shadowRoot) {\n            // keep drilling down through the shadow root nodes\n            for (i = node.shadowRoot.childNodes.length - 1; i >= 0; i--) {\n                clientHydrate(parentVNode, childRenderNodes, slotNodes, shadowRootNodes, hostElm, node.shadowRoot.childNodes[i], hostId);\n            }\n        }\n    }\n    else if (node.nodeType === 8 /* NODE_TYPE.CommentNode */) {\n        // `${COMMENT_TYPE}.${hostId}.${nodeId}.${depth}.${index}`\n        childIdSplt = node.nodeValue.split('.');\n        if (childIdSplt[1] === hostId || childIdSplt[1] === '0') {\n            // comment node for either the host id or a 0 host id\n            childNodeType = childIdSplt[0];\n            childVNode = {\n                $flags$: 0,\n                $hostId$: childIdSplt[1],\n                $nodeId$: childIdSplt[2],\n                $depth$: childIdSplt[3],\n                $index$: childIdSplt[4],\n                $elm$: node,\n                $attrs$: null,\n                $children$: null,\n                $key$: null,\n                $name$: null,\n                $tag$: null,\n                $text$: null,\n            };\n            if (childNodeType === TEXT_NODE_ID) {\n                childVNode.$elm$ = node.nextSibling;\n                if (childVNode.$elm$ && childVNode.$elm$.nodeType === 3 /* NODE_TYPE.TextNode */) {\n                    childVNode.$text$ = childVNode.$elm$.textContent;\n                    childRenderNodes.push(childVNode);\n                    // remove the text comment since it's no longer needed\n                    node.remove();\n                    if (!parentVNode.$children$) {\n                        parentVNode.$children$ = [];\n                    }\n                    parentVNode.$children$[childVNode.$index$] = childVNode;\n                    if (shadowRootNodes && childVNode.$depth$ === '0') {\n                        shadowRootNodes[childVNode.$index$] = childVNode.$elm$;\n                    }\n                }\n            }\n            else if (childVNode.$hostId$ === hostId) {\n                // this comment node is specifically for this host id\n                if (childNodeType === SLOT_NODE_ID) {\n                    // `${SLOT_NODE_ID}.${hostId}.${nodeId}.${depth}.${index}.${slotName}`;\n                    childVNode.$tag$ = 'slot';\n                    if (childIdSplt[5]) {\n                        node['s-sn'] = childVNode.$name$ = childIdSplt[5];\n                    }\n                    else {\n                        node['s-sn'] = '';\n                    }\n                    node['s-sr'] = true;\n                    if (BUILD.shadowDom && shadowRootNodes) {\n                        // browser support shadowRoot and this is a shadow dom component\n                        // create an actual slot element\n                        childVNode.$elm$ = doc.createElement(childVNode.$tag$);\n                        if (childVNode.$name$) {\n                            // add the slot name attribute\n                            childVNode.$elm$.setAttribute('name', childVNode.$name$);\n                        }\n                        // insert the new slot element before the slot comment\n                        node.parentNode.insertBefore(childVNode.$elm$, node);\n                        // remove the slot comment since it's not needed for shadow\n                        node.remove();\n                        if (childVNode.$depth$ === '0') {\n                            shadowRootNodes[childVNode.$index$] = childVNode.$elm$;\n                        }\n                    }\n                    slotNodes.push(childVNode);\n                    if (!parentVNode.$children$) {\n                        parentVNode.$children$ = [];\n                    }\n                    parentVNode.$children$[childVNode.$index$] = childVNode;\n                }\n                else if (childNodeType === CONTENT_REF_ID) {\n                    // `${CONTENT_REF_ID}.${hostId}`;\n                    if (BUILD.shadowDom && shadowRootNodes) {\n                        // remove the content ref comment since it's not needed for shadow\n                        node.remove();\n                    }\n                    else if (BUILD.slotRelocation) {\n                        hostElm['s-cr'] = node;\n                        node['s-cn'] = true;\n                    }\n                }\n            }\n        }\n    }\n    else if (parentVNode && parentVNode.$tag$ === 'style') {\n        const vnode = newVNode(null, node.textContent);\n        vnode.$elm$ = node;\n        vnode.$index$ = '0';\n        parentVNode.$children$ = [vnode];\n    }\n};\n/**\n * Recursively locate any comments representing an original location for a node in a node's\n * children or shadowRoot children.\n *\n * @param node The node to search.\n * @param orgLocNodes A map of the original location annotation and the current node being searched.\n */\nconst initializeDocumentHydrate = (node, orgLocNodes) => {\n    if (node.nodeType === 1 /* NODE_TYPE.ElementNode */) {\n        let i = 0;\n        for (; i < node.childNodes.length; i++) {\n            initializeDocumentHydrate(node.childNodes[i], orgLocNodes);\n        }\n        if (node.shadowRoot) {\n            for (i = 0; i < node.shadowRoot.childNodes.length; i++) {\n                initializeDocumentHydrate(node.shadowRoot.childNodes[i], orgLocNodes);\n            }\n        }\n    }\n    else if (node.nodeType === 8 /* NODE_TYPE.CommentNode */) {\n        const childIdSplt = node.nodeValue.split('.');\n        if (childIdSplt[0] === ORG_LOCATION_ID) {\n            orgLocNodes.set(childIdSplt[1] + '.' + childIdSplt[2], node);\n            node.nodeValue = '';\n            // useful to know if the original location is\n            // the root light-dom of a shadow dom component\n            node['s-en'] = childIdSplt[3];\n        }\n    }\n};\n// Private\nconst computeMode = (elm) => modeResolutionChain.map((h) => h(elm)).find((m) => !!m);\n// Public\nconst setMode = (handler) => modeResolutionChain.push(handler);\nconst getMode = (ref) => getHostRef(ref).$modeName$;\n/**\n * Parse a new property value for a given property type.\n *\n * While the prop value can reasonably be expected to be of `any` type as far as TypeScript's type checker is concerned,\n * it is not safe to assume that the string returned by evaluating `typeof propValue` matches:\n *   1. `any`, the type given to `propValue` in the function signature\n *   2. the type stored from `propType`.\n *\n * This function provides the capability to parse/coerce a property's value to potentially any other JavaScript type.\n *\n * Property values represented in TSX preserve their type information. In the example below, the number 0 is passed to\n * a component. This `propValue` will preserve its type information (`typeof propValue === 'number'`). Note that is\n * based on the type of the value being passed in, not the type declared of the class member decorated with `@Prop`.\n * ```tsx\n * <my-cmp prop-val={0}></my-cmp>\n * ```\n *\n * HTML prop values on the other hand, will always a string\n *\n * @param propValue the new value to coerce to some type\n * @param propType the type of the prop, expressed as a binary number\n * @returns the parsed/coerced value\n */\nconst parsePropertyValue = (propValue, propType) => {\n    // ensure this value is of the correct prop type\n    if (propValue != null && !isComplexType(propValue)) {\n        if (BUILD.propBoolean && propType & 4 /* MEMBER_FLAGS.Boolean */) {\n            // per the HTML spec, any string value means it is a boolean true value\n            // but we'll cheat here and say that the string \"false\" is the boolean false\n            return propValue === 'false' ? false : propValue === '' || !!propValue;\n        }\n        if (BUILD.propNumber && propType & 2 /* MEMBER_FLAGS.Number */) {\n            // force it to be a number\n            return parseFloat(propValue);\n        }\n        if (BUILD.propString && propType & 1 /* MEMBER_FLAGS.String */) {\n            // could have been passed as a number or boolean\n            // but we still want it as a string\n            return String(propValue);\n        }\n        // redundant return here for better minification\n        return propValue;\n    }\n    // not sure exactly what type we want\n    // so no need to change to a different type\n    return propValue;\n};\nconst getElement = (ref) => (BUILD.lazyLoad ? getHostRef(ref).$hostElement$ : ref);\nconst createEvent = (ref, name, flags) => {\n    const elm = getElement(ref);\n    return {\n        emit: (detail) => {\n            if (BUILD.isDev && !elm.isConnected) {\n                consoleDevWarn(`The \"${name}\" event was emitted, but the dispatcher node is no longer connected to the dom.`);\n            }\n            return emitEvent(elm, name, {\n                bubbles: !!(flags & 4 /* EVENT_FLAGS.Bubbles */),\n                composed: !!(flags & 2 /* EVENT_FLAGS.Composed */),\n                cancelable: !!(flags & 1 /* EVENT_FLAGS.Cancellable */),\n                detail,\n            });\n        },\n    };\n};\n/**\n * Helper function to create & dispatch a custom Event on a provided target\n * @param elm the target of the Event\n * @param name the name to give the custom Event\n * @param opts options for configuring a custom Event\n * @returns the custom Event\n */\nconst emitEvent = (elm, name, opts) => {\n    const ev = plt.ce(name, opts);\n    elm.dispatchEvent(ev);\n    return ev;\n};\nconst rootAppliedStyles = /*@__PURE__*/ new WeakMap();\nconst registerStyle = (scopeId, cssText, allowCS) => {\n    let style = styles.get(scopeId);\n    if (supportsConstructableStylesheets && allowCS) {\n        style = (style || new CSSStyleSheet());\n        if (typeof style === 'string') {\n            style = cssText;\n        }\n        else {\n            style.replaceSync(cssText);\n        }\n    }\n    else {\n        style = cssText;\n    }\n    styles.set(scopeId, style);\n};\nconst addStyle = (styleContainerNode, cmpMeta, mode) => {\n    var _a;\n    const scopeId = getScopeId(cmpMeta, mode);\n    const style = styles.get(scopeId);\n    if (!BUILD.attachStyles) {\n        return scopeId;\n    }\n    // if an element is NOT connected then getRootNode() will return the wrong root node\n    // so the fallback is to always use the document for the root node in those cases\n    styleContainerNode = styleContainerNode.nodeType === 11 /* NODE_TYPE.DocumentFragment */ ? styleContainerNode : doc;\n    if (style) {\n        if (typeof style === 'string') {\n            styleContainerNode = styleContainerNode.head || styleContainerNode;\n            let appliedStyles = rootAppliedStyles.get(styleContainerNode);\n            let styleElm;\n            if (!appliedStyles) {\n                rootAppliedStyles.set(styleContainerNode, (appliedStyles = new Set()));\n            }\n            if (!appliedStyles.has(scopeId)) {\n                if (BUILD.hydrateClientSide &&\n                    styleContainerNode.host &&\n                    (styleElm = styleContainerNode.querySelector(`[${HYDRATED_STYLE_ID}=\"${scopeId}\"]`))) {\n                    // This is only happening on native shadow-dom, do not needs CSS var shim\n                    styleElm.innerHTML = style;\n                }\n                else {\n                    styleElm = doc.createElement('style');\n                    styleElm.innerHTML = style;\n                    // Apply CSP nonce to the style tag if it exists\n                    const nonce = (_a = plt.$nonce$) !== null && _a !== void 0 ? _a : queryNonceMetaTagContent(doc);\n                    if (nonce != null) {\n                        styleElm.setAttribute('nonce', nonce);\n                    }\n                    if (BUILD.hydrateServerSide || BUILD.hotModuleReplacement) {\n                        styleElm.setAttribute(HYDRATED_STYLE_ID, scopeId);\n                    }\n                    styleContainerNode.insertBefore(styleElm, styleContainerNode.querySelector('link'));\n                }\n                // Add styles for `slot-fb` elements if we're using slots outside the Shadow DOM\n                if (cmpMeta.$flags$ & 4 /* CMP_FLAGS.hasSlotRelocation */) {\n                    styleElm.innerHTML += SLOT_FB_CSS;\n                }\n                if (appliedStyles) {\n                    appliedStyles.add(scopeId);\n                }\n            }\n        }\n        else if (BUILD.constructableCSS && !styleContainerNode.adoptedStyleSheets.includes(style)) {\n            styleContainerNode.adoptedStyleSheets = [...styleContainerNode.adoptedStyleSheets, style];\n        }\n    }\n    return scopeId;\n};\nconst attachStyles = (hostRef) => {\n    const cmpMeta = hostRef.$cmpMeta$;\n    const elm = hostRef.$hostElement$;\n    const flags = cmpMeta.$flags$;\n    const endAttachStyles = createTime('attachStyles', cmpMeta.$tagName$);\n    const scopeId = addStyle(BUILD.shadowDom && supportsShadow && elm.shadowRoot ? elm.shadowRoot : elm.getRootNode(), cmpMeta, hostRef.$modeName$);\n    if ((BUILD.shadowDom || BUILD.scoped) && BUILD.cssAnnotations && flags & 10 /* CMP_FLAGS.needsScopedEncapsulation */) {\n        // only required when we're NOT using native shadow dom (slot)\n        // or this browser doesn't support native shadow dom\n        // and this host element was NOT created with SSR\n        // let's pick out the inner content for slot projection\n        // create a node to represent where the original\n        // content was first placed, which is useful later on\n        // DOM WRITE!!\n        elm['s-sc'] = scopeId;\n        elm.classList.add(scopeId + '-h');\n        if (BUILD.scoped && flags & 2 /* CMP_FLAGS.scopedCssEncapsulation */) {\n            elm.classList.add(scopeId + '-s');\n        }\n    }\n    endAttachStyles();\n};\nconst getScopeId = (cmp, mode) => 'sc-' + (BUILD.mode && mode && cmp.$flags$ & 32 /* CMP_FLAGS.hasMode */ ? cmp.$tagName$ + '-' + mode : cmp.$tagName$);\nconst convertScopedToShadow = (css) => css.replace(/\\/\\*!@([^\\/]+)\\*\\/[^\\{]+\\{/g, '$1{');\n/**\n * Production setAccessor() function based on Preact by\n * Jason Miller (@developit)\n * Licensed under the MIT License\n * https://github.com/developit/preact/blob/master/LICENSE\n *\n * Modified for Stencil's compiler and vdom\n */\n/**\n * When running a VDom render set properties present on a VDom node onto the\n * corresponding HTML element.\n *\n * Note that this function has special functionality for the `class`,\n * `style`, `key`, and `ref` attributes, as well as event handlers (like\n * `onClick`, etc). All others are just passed through as-is.\n *\n * @param elm the HTMLElement onto which attributes should be set\n * @param memberName the name of the attribute to set\n * @param oldValue the old value for the attribute\n * @param newValue the new value for the attribute\n * @param isSvg whether we're in an svg context or not\n * @param flags bitflags for Vdom variables\n */\nconst setAccessor = (elm, memberName, oldValue, newValue, isSvg, flags) => {\n    if (oldValue !== newValue) {\n        let isProp = isMemberInElement(elm, memberName);\n        let ln = memberName.toLowerCase();\n        if (BUILD.vdomClass && memberName === 'class') {\n            const classList = elm.classList;\n            const oldClasses = parseClassList(oldValue);\n            const newClasses = parseClassList(newValue);\n            classList.remove(...oldClasses.filter((c) => c && !newClasses.includes(c)));\n            classList.add(...newClasses.filter((c) => c && !oldClasses.includes(c)));\n        }\n        else if (BUILD.vdomStyle && memberName === 'style') {\n            // update style attribute, css properties and values\n            if (BUILD.updatable) {\n                for (const prop in oldValue) {\n                    if (!newValue || newValue[prop] == null) {\n                        if (!BUILD.hydrateServerSide && prop.includes('-')) {\n                            elm.style.removeProperty(prop);\n                        }\n                        else {\n                            elm.style[prop] = '';\n                        }\n                    }\n                }\n            }\n            for (const prop in newValue) {\n                if (!oldValue || newValue[prop] !== oldValue[prop]) {\n                    if (!BUILD.hydrateServerSide && prop.includes('-')) {\n                        elm.style.setProperty(prop, newValue[prop]);\n                    }\n                    else {\n                        elm.style[prop] = newValue[prop];\n                    }\n                }\n            }\n        }\n        else if (BUILD.vdomKey && memberName === 'key')\n            ;\n        else if (BUILD.vdomRef && memberName === 'ref') {\n            // minifier will clean this up\n            if (newValue) {\n                newValue(elm);\n            }\n        }\n        else if (BUILD.vdomListener &&\n            (BUILD.lazyLoad ? !isProp : !elm.__lookupSetter__(memberName)) &&\n            memberName[0] === 'o' &&\n            memberName[1] === 'n') {\n            // Event Handlers\n            // so if the member name starts with \"on\" and the 3rd characters is\n            // a capital letter, and it's not already a member on the element,\n            // then we're assuming it's an event listener\n            if (memberName[2] === '-') {\n                // on- prefixed events\n                // allows to be explicit about the dom event to listen without any magic\n                // under the hood:\n                // <my-cmp on-click> // listens for \"click\"\n                // <my-cmp on-Click> // listens for \"Click\"\n                // <my-cmp on-ionChange> // listens for \"ionChange\"\n                // <my-cmp on-EVENTS> // listens for \"EVENTS\"\n                memberName = memberName.slice(3);\n            }\n            else if (isMemberInElement(win, ln)) {\n                // standard event\n                // the JSX attribute could have been \"onMouseOver\" and the\n                // member name \"onmouseover\" is on the window's prototype\n                // so let's add the listener \"mouseover\", which is all lowercased\n                memberName = ln.slice(2);\n            }\n            else {\n                // custom event\n                // the JSX attribute could have been \"onMyCustomEvent\"\n                // so let's trim off the \"on\" prefix and lowercase the first character\n                // and add the listener \"myCustomEvent\"\n                // except for the first character, we keep the event name case\n                memberName = ln[2] + memberName.slice(3);\n            }\n            if (oldValue || newValue) {\n                // Need to account for \"capture\" events.\n                // If the event name ends with \"Capture\", we'll update the name to remove\n                // the \"Capture\" suffix and make sure the event listener is setup to handle the capture event.\n                const capture = memberName.endsWith(CAPTURE_EVENT_SUFFIX);\n                // Make sure we only replace the last instance of \"Capture\"\n                memberName = memberName.replace(CAPTURE_EVENT_REGEX, '');\n                if (oldValue) {\n                    plt.rel(elm, memberName, oldValue, capture);\n                }\n                if (newValue) {\n                    plt.ael(elm, memberName, newValue, capture);\n                }\n            }\n        }\n        else if (BUILD.vdomPropOrAttr) {\n            // Set property if it exists and it's not a SVG\n            const isComplex = isComplexType(newValue);\n            if ((isProp || (isComplex && newValue !== null)) && !isSvg) {\n                try {\n                    if (!elm.tagName.includes('-')) {\n                        const n = newValue == null ? '' : newValue;\n                        // Workaround for Safari, moving the <input> caret when re-assigning the same valued\n                        if (memberName === 'list') {\n                            isProp = false;\n                        }\n                        else if (oldValue == null || elm[memberName] != n) {\n                            elm[memberName] = n;\n                        }\n                    }\n                    else {\n                        elm[memberName] = newValue;\n                    }\n                }\n                catch (e) {\n                    /**\n                     * in case someone tries to set a read-only property, e.g. \"namespaceURI\", we just ignore it\n                     */\n                }\n            }\n            /**\n             * Need to manually update attribute if:\n             * - memberName is not an attribute\n             * - if we are rendering the host element in order to reflect attribute\n             * - if it's a SVG, since properties might not work in <svg>\n             * - if the newValue is null/undefined or 'false'.\n             */\n            let xlink = false;\n            if (BUILD.vdomXlink) {\n                if (ln !== (ln = ln.replace(/^xlink\\:?/, ''))) {\n                    memberName = ln;\n                    xlink = true;\n                }\n            }\n            if (newValue == null || newValue === false) {\n                if (newValue !== false || elm.getAttribute(memberName) === '') {\n                    if (BUILD.vdomXlink && xlink) {\n                        elm.removeAttributeNS(XLINK_NS, memberName);\n                    }\n                    else {\n                        elm.removeAttribute(memberName);\n                    }\n                }\n            }\n            else if ((!isProp || flags & 4 /* VNODE_FLAGS.isHost */ || isSvg) && !isComplex) {\n                newValue = newValue === true ? '' : newValue;\n                if (BUILD.vdomXlink && xlink) {\n                    elm.setAttributeNS(XLINK_NS, memberName, newValue);\n                }\n                else {\n                    elm.setAttribute(memberName, newValue);\n                }\n            }\n        }\n    }\n};\nconst parseClassListRegex = /\\s/;\n/**\n * Parsed a string of classnames into an array\n * @param value className string, e.g. \"foo bar baz\"\n * @returns list of classes, e.g. [\"foo\", \"bar\", \"baz\"]\n */\nconst parseClassList = (value) => (!value ? [] : value.split(parseClassListRegex));\nconst CAPTURE_EVENT_SUFFIX = 'Capture';\nconst CAPTURE_EVENT_REGEX = new RegExp(CAPTURE_EVENT_SUFFIX + '$');\nconst updateElement = (oldVnode, newVnode, isSvgMode, memberName) => {\n    // if the element passed in is a shadow root, which is a document fragment\n    // then we want to be adding attrs/props to the shadow root's \"host\" element\n    // if it's not a shadow root, then we add attrs/props to the same element\n    const elm = newVnode.$elm$.nodeType === 11 /* NODE_TYPE.DocumentFragment */ && newVnode.$elm$.host\n        ? newVnode.$elm$.host\n        : newVnode.$elm$;\n    const oldVnodeAttrs = (oldVnode && oldVnode.$attrs$) || EMPTY_OBJ;\n    const newVnodeAttrs = newVnode.$attrs$ || EMPTY_OBJ;\n    if (BUILD.updatable) {\n        // remove attributes no longer present on the vnode by setting them to undefined\n        for (memberName in oldVnodeAttrs) {\n            if (!(memberName in newVnodeAttrs)) {\n                setAccessor(elm, memberName, oldVnodeAttrs[memberName], undefined, isSvgMode, newVnode.$flags$);\n            }\n        }\n    }\n    // add new & update changed attributes\n    for (memberName in newVnodeAttrs) {\n        setAccessor(elm, memberName, oldVnodeAttrs[memberName], newVnodeAttrs[memberName], isSvgMode, newVnode.$flags$);\n    }\n};\n/**\n * Create a DOM Node corresponding to one of the children of a given VNode.\n *\n * @param oldParentVNode the parent VNode from the previous render\n * @param newParentVNode the parent VNode from the current render\n * @param childIndex the index of the VNode, in the _new_ parent node's\n * children, for which we will create a new DOM node\n * @param parentElm the parent DOM node which our new node will be a child of\n * @returns the newly created node\n */\nconst createElm = (oldParentVNode, newParentVNode, childIndex, parentElm) => {\n    // tslint:disable-next-line: prefer-const\n    const newVNode = newParentVNode.$children$[childIndex];\n    let i = 0;\n    let elm;\n    let childNode;\n    let oldVNode;\n    if (BUILD.slotRelocation && !useNativeShadowDom) {\n        // remember for later we need to check to relocate nodes\n        checkSlotRelocate = true;\n        if (newVNode.$tag$ === 'slot') {\n            if (scopeId) {\n                // scoped css needs to add its scoped id to the parent element\n                parentElm.classList.add(scopeId + '-s');\n            }\n            newVNode.$flags$ |= newVNode.$children$\n                ? // slot element has fallback content\n                    2 /* VNODE_FLAGS.isSlotFallback */\n                : // slot element does not have fallback content\n                    1 /* VNODE_FLAGS.isSlotReference */;\n        }\n    }\n    if (BUILD.isDev && newVNode.$elm$) {\n        consoleDevError(`The JSX ${newVNode.$text$ !== null ? `\"${newVNode.$text$}\" text` : `\"${newVNode.$tag$}\" element`} node should not be shared within the same renderer. The renderer caches element lookups in order to improve performance. However, a side effect from this is that the exact same JSX node should not be reused. For more information please see https://stenciljs.com/docs/templating-jsx#avoid-shared-jsx-nodes`);\n    }\n    if (BUILD.vdomText && newVNode.$text$ !== null) {\n        // create text node\n        elm = newVNode.$elm$ = doc.createTextNode(newVNode.$text$);\n    }\n    else if (BUILD.slotRelocation && newVNode.$flags$ & 1 /* VNODE_FLAGS.isSlotReference */) {\n        // create a slot reference node\n        elm = newVNode.$elm$ =\n            BUILD.isDebug || BUILD.hydrateServerSide ? slotReferenceDebugNode(newVNode) : doc.createTextNode('');\n    }\n    else {\n        if (BUILD.svg && !isSvgMode) {\n            isSvgMode = newVNode.$tag$ === 'svg';\n        }\n        // create element\n        elm = newVNode.$elm$ = (BUILD.svg\n            ? doc.createElementNS(isSvgMode ? SVG_NS : HTML_NS, BUILD.slotRelocation && newVNode.$flags$ & 2 /* VNODE_FLAGS.isSlotFallback */\n                ? 'slot-fb'\n                : newVNode.$tag$)\n            : doc.createElement(BUILD.slotRelocation && newVNode.$flags$ & 2 /* VNODE_FLAGS.isSlotFallback */\n                ? 'slot-fb'\n                : newVNode.$tag$));\n        if (BUILD.svg && isSvgMode && newVNode.$tag$ === 'foreignObject') {\n            isSvgMode = false;\n        }\n        // add css classes, attrs, props, listeners, etc.\n        if (BUILD.vdomAttribute) {\n            updateElement(null, newVNode, isSvgMode);\n        }\n        if ((BUILD.shadowDom || BUILD.scoped) && isDef(scopeId) && elm['s-si'] !== scopeId) {\n            // if there is a scopeId and this is the initial render\n            // then let's add the scopeId as a css class\n            elm.classList.add((elm['s-si'] = scopeId));\n        }\n        if (newVNode.$children$) {\n            for (i = 0; i < newVNode.$children$.length; ++i) {\n                // create the node\n                childNode = createElm(oldParentVNode, newVNode, i, elm);\n                // return node could have been null\n                if (childNode) {\n                    // append our new node\n                    elm.appendChild(childNode);\n                }\n            }\n        }\n        if (BUILD.svg) {\n            if (newVNode.$tag$ === 'svg') {\n                // Only reset the SVG context when we're exiting <svg> element\n                isSvgMode = false;\n            }\n            else if (elm.tagName === 'foreignObject') {\n                // Reenter SVG context when we're exiting <foreignObject> element\n                isSvgMode = true;\n            }\n        }\n    }\n    // This needs to always happen so we can hide nodes that are projected\n    // to another component but don't end up in a slot\n    elm['s-hn'] = hostTagName;\n    if (BUILD.slotRelocation) {\n        if (newVNode.$flags$ & (2 /* VNODE_FLAGS.isSlotFallback */ | 1 /* VNODE_FLAGS.isSlotReference */)) {\n            // remember the content reference comment\n            elm['s-sr'] = true;\n            // remember the content reference comment\n            elm['s-cr'] = contentRef;\n            // remember the slot name, or empty string for default slot\n            elm['s-sn'] = newVNode.$name$ || '';\n            // check if we've got an old vnode for this slot\n            oldVNode = oldParentVNode && oldParentVNode.$children$ && oldParentVNode.$children$[childIndex];\n            if (oldVNode && oldVNode.$tag$ === newVNode.$tag$ && oldParentVNode.$elm$) {\n                if (BUILD.experimentalSlotFixes) {\n                    // we've got an old slot vnode and the wrapper is being replaced\n                    // so let's move the old slot content to the root of the element currently being rendered\n                    relocateToHostRoot(oldParentVNode.$elm$);\n                }\n                else {\n                    // we've got an old slot vnode and the wrapper is being replaced\n                    // so let's move the old slot content back to its original location\n                    putBackInOriginalLocation(oldParentVNode.$elm$, false);\n                }\n            }\n        }\n    }\n    return elm;\n};\n/**\n * Relocates all child nodes of an element that were a part of a previous slot relocation\n * to the root of the Stencil component currently being rendered. This happens when a parent\n * element of a slot reference node dynamically changes and triggers a re-render. We cannot use\n * `putBackInOriginalLocation()` because that may relocate nodes to elements that will not be re-rendered\n * and so they will not be relocated again.\n *\n * @param parentElm The element potentially containing relocated nodes.\n */\nconst relocateToHostRoot = (parentElm) => {\n    plt.$flags$ |= 1 /* PLATFORM_FLAGS.isTmpDisconnected */;\n    const host = parentElm.closest(hostTagName.toLowerCase());\n    if (host != null) {\n        for (const childNode of Array.from(parentElm.childNodes)) {\n            // Only relocate nodes that were slotted in\n            if (childNode['s-sh'] != null) {\n                host.insertBefore(childNode, null);\n                // Reset so we can correctly move the node around again.\n                childNode['s-sh'] = undefined;\n                // Need to tell the render pipeline to check to relocate slot content again\n                checkSlotRelocate = true;\n            }\n        }\n    }\n    plt.$flags$ &= ~1 /* PLATFORM_FLAGS.isTmpDisconnected */;\n};\nconst putBackInOriginalLocation = (parentElm, recursive) => {\n    plt.$flags$ |= 1 /* PLATFORM_FLAGS.isTmpDisconnected */;\n    const oldSlotChildNodes = parentElm.childNodes;\n    for (let i = oldSlotChildNodes.length - 1; i >= 0; i--) {\n        const childNode = oldSlotChildNodes[i];\n        if (childNode['s-hn'] !== hostTagName && childNode['s-ol']) {\n            // and relocate it back to it's original location\n            parentReferenceNode(childNode).insertBefore(childNode, referenceNode(childNode));\n            // remove the old original location comment entirely\n            // later on the patch function will know what to do\n            // and move this to the correct spot if need be\n            childNode['s-ol'].remove();\n            childNode['s-ol'] = undefined;\n            // Reset so we can correctly move the node around again.\n            childNode['s-sh'] = undefined;\n            checkSlotRelocate = true;\n        }\n        if (recursive) {\n            putBackInOriginalLocation(childNode, recursive);\n        }\n    }\n    plt.$flags$ &= ~1 /* PLATFORM_FLAGS.isTmpDisconnected */;\n};\n/**\n * Create DOM nodes corresponding to a list of {@link d.Vnode} objects and\n * add them to the DOM in the appropriate place.\n *\n * @param parentElm the DOM node which should be used as a parent for the new\n * DOM nodes\n * @param before a child of the `parentElm` which the new children should be\n * inserted before (optional)\n * @param parentVNode the parent virtual DOM node\n * @param vnodes the new child virtual DOM nodes to produce DOM nodes for\n * @param startIdx the index in the child virtual DOM nodes at which to start\n * creating DOM nodes (inclusive)\n * @param endIdx the index in the child virtual DOM nodes at which to stop\n * creating DOM nodes (inclusive)\n */\nconst addVnodes = (parentElm, before, parentVNode, vnodes, startIdx, endIdx) => {\n    let containerElm = ((BUILD.slotRelocation && parentElm['s-cr'] && parentElm['s-cr'].parentNode) || parentElm);\n    let childNode;\n    if (BUILD.shadowDom && containerElm.shadowRoot && containerElm.tagName === hostTagName) {\n        containerElm = containerElm.shadowRoot;\n    }\n    for (; startIdx <= endIdx; ++startIdx) {\n        if (vnodes[startIdx]) {\n            childNode = createElm(null, parentVNode, startIdx, parentElm);\n            if (childNode) {\n                vnodes[startIdx].$elm$ = childNode;\n                containerElm.insertBefore(childNode, BUILD.slotRelocation ? referenceNode(before) : before);\n            }\n        }\n    }\n};\n/**\n * Remove the DOM elements corresponding to a list of {@link d.VNode} objects.\n * This can be used to, for instance, clean up after a list of children which\n * should no longer be shown.\n *\n * This function also handles some of Stencil's slot relocation logic.\n *\n * @param vnodes a list of virtual DOM nodes to remove\n * @param startIdx the index at which to start removing nodes (inclusive)\n * @param endIdx the index at which to stop removing nodes (inclusive)\n */\nconst removeVnodes = (vnodes, startIdx, endIdx) => {\n    for (let index = startIdx; index <= endIdx; ++index) {\n        const vnode = vnodes[index];\n        if (vnode) {\n            const elm = vnode.$elm$;\n            nullifyVNodeRefs(vnode);\n            if (elm) {\n                if (BUILD.slotRelocation) {\n                    // we're removing this element\n                    // so it's possible we need to show slot fallback content now\n                    checkSlotFallbackVisibility = true;\n                    if (elm['s-ol']) {\n                        // remove the original location comment\n                        elm['s-ol'].remove();\n                    }\n                    else {\n                        // it's possible that child nodes of the node\n                        // that's being removed are slot nodes\n                        putBackInOriginalLocation(elm, true);\n                    }\n                }\n                // remove the vnode's element from the dom\n                elm.remove();\n            }\n        }\n    }\n};\n/**\n * Reconcile the children of a new VNode with the children of an old VNode by\n * traversing the two collections of children, identifying nodes that are\n * conserved or changed, calling out to `patch` to make any necessary\n * updates to the DOM, and rearranging DOM nodes as needed.\n *\n * The algorithm for reconciling children works by analyzing two 'windows' onto\n * the two arrays of children (`oldCh` and `newCh`). We keep track of the\n * 'windows' by storing start and end indices and references to the\n * corresponding array entries. Initially the two 'windows' are basically equal\n * to the entire array, but we progressively narrow the windows until there are\n * no children left to update by doing the following:\n *\n * 1. Skip any `null` entries at the beginning or end of the two arrays, so\n *    that if we have an initial array like the following we'll end up dealing\n *    only with a window bounded by the highlighted elements:\n *\n *    [null, null, VNode1 , ... , VNode2, null, null]\n *                 ^^^^^^         ^^^^^^\n *\n * 2. Check to see if the elements at the head and tail positions are equal\n *    across the windows. This will basically detect elements which haven't\n *    been added, removed, or changed position, i.e. if you had the following\n *    VNode elements (represented as HTML):\n *\n *    oldVNode: `<div><p><span>HEY</span></p></div>`\n *    newVNode: `<div><p><span>THERE</span></p></div>`\n *\n *    Then when comparing the children of the `<div>` tag we check the equality\n *    of the VNodes corresponding to the `<p>` tags and, since they are the\n *    same tag in the same position, we'd be able to avoid completely\n *    re-rendering the subtree under them with a new DOM element and would just\n *    call out to `patch` to handle reconciling their children and so on.\n *\n * 3. Check, for both windows, to see if the element at the beginning of the\n *    window corresponds to the element at the end of the other window. This is\n *    a heuristic which will let us identify _some_ situations in which\n *    elements have changed position, for instance it _should_ detect that the\n *    children nodes themselves have not changed but merely moved in the\n *    following example:\n *\n *    oldVNode: `<div><element-one /><element-two /></div>`\n *    newVNode: `<div><element-two /><element-one /></div>`\n *\n *    If we find cases like this then we also need to move the concrete DOM\n *    elements corresponding to the moved children to write the re-order to the\n *    DOM.\n *\n * 4. Finally, if VNodes have the `key` attribute set on them we check for any\n *    nodes in the old children which have the same key as the first element in\n *    our window on the new children. If we find such a node we handle calling\n *    out to `patch`, moving relevant DOM nodes, and so on, in accordance with\n *    what we find.\n *\n * Finally, once we've narrowed our 'windows' to the point that either of them\n * collapse (i.e. they have length 0) we then handle any remaining VNode\n * insertion or deletion that needs to happen to get a DOM state that correctly\n * reflects the new child VNodes. If, for instance, after our window on the old\n * children has collapsed we still have more nodes on the new children that\n * we haven't dealt with yet then we need to add them, or if the new children\n * collapse but we still have unhandled _old_ children then we need to make\n * sure the corresponding DOM nodes are removed.\n *\n * @param parentElm the node into which the parent VNode is rendered\n * @param oldCh the old children of the parent node\n * @param newVNode the new VNode which will replace the parent\n * @param newCh the new children of the parent node\n * @param isInitialRender whether or not this is the first render of the vdom\n */\nconst updateChildren = (parentElm, oldCh, newVNode, newCh, isInitialRender = false) => {\n    let oldStartIdx = 0;\n    let newStartIdx = 0;\n    let idxInOld = 0;\n    let i = 0;\n    let oldEndIdx = oldCh.length - 1;\n    let oldStartVnode = oldCh[0];\n    let oldEndVnode = oldCh[oldEndIdx];\n    let newEndIdx = newCh.length - 1;\n    let newStartVnode = newCh[0];\n    let newEndVnode = newCh[newEndIdx];\n    let node;\n    let elmToMove;\n    while (oldStartIdx <= oldEndIdx && newStartIdx <= newEndIdx) {\n        if (oldStartVnode == null) {\n            // VNode might have been moved left\n            oldStartVnode = oldCh[++oldStartIdx];\n        }\n        else if (oldEndVnode == null) {\n            oldEndVnode = oldCh[--oldEndIdx];\n        }\n        else if (newStartVnode == null) {\n            newStartVnode = newCh[++newStartIdx];\n        }\n        else if (newEndVnode == null) {\n            newEndVnode = newCh[--newEndIdx];\n        }\n        else if (isSameVnode(oldStartVnode, newStartVnode, isInitialRender)) {\n            // if the start nodes are the same then we should patch the new VNode\n            // onto the old one, and increment our `newStartIdx` and `oldStartIdx`\n            // indices to reflect that. We don't need to move any DOM Nodes around\n            // since things are matched up in order.\n            patch(oldStartVnode, newStartVnode, isInitialRender);\n            oldStartVnode = oldCh[++oldStartIdx];\n            newStartVnode = newCh[++newStartIdx];\n        }\n        else if (isSameVnode(oldEndVnode, newEndVnode, isInitialRender)) {\n            // likewise, if the end nodes are the same we patch new onto old and\n            // decrement our end indices, and also likewise in this case we don't\n            // need to move any DOM Nodes.\n            patch(oldEndVnode, newEndVnode, isInitialRender);\n            oldEndVnode = oldCh[--oldEndIdx];\n            newEndVnode = newCh[--newEndIdx];\n        }\n        else if (isSameVnode(oldStartVnode, newEndVnode, isInitialRender)) {\n            // case: \"Vnode moved right\"\n            //\n            // We've found that the last node in our window on the new children is\n            // the same VNode as the _first_ node in our window on the old children\n            // we're dealing with now. Visually, this is the layout of these two\n            // nodes:\n            //\n            // newCh: [..., newStartVnode , ... , newEndVnode , ...]\n            //                                    ^^^^^^^^^^^\n            // oldCh: [..., oldStartVnode , ... , oldEndVnode , ...]\n            //              ^^^^^^^^^^^^^\n            //\n            // In this situation we need to patch `newEndVnode` onto `oldStartVnode`\n            // and move the DOM element for `oldStartVnode`.\n            if (BUILD.slotRelocation && (oldStartVnode.$tag$ === 'slot' || newEndVnode.$tag$ === 'slot')) {\n                putBackInOriginalLocation(oldStartVnode.$elm$.parentNode, false);\n            }\n            patch(oldStartVnode, newEndVnode, isInitialRender);\n            // We need to move the element for `oldStartVnode` into a position which\n            // will be appropriate for `newEndVnode`. For this we can use\n            // `.insertBefore` and `oldEndVnode.$elm$.nextSibling`. If there is a\n            // sibling for `oldEndVnode.$elm$` then we want to move the DOM node for\n            // `oldStartVnode` between `oldEndVnode` and it's sibling, like so:\n            //\n            // <old-start-node />\n            // <some-intervening-node />\n            // <old-end-node />\n            // <!-- ->              <-- `oldStartVnode.$elm$` should be inserted here\n            // <next-sibling />\n            //\n            // If instead `oldEndVnode.$elm$` has no sibling then we just want to put\n            // the node for `oldStartVnode` at the end of the children of\n            // `parentElm`. Luckily, `Node.nextSibling` will return `null` if there\n            // aren't any siblings, and passing `null` to `Node.insertBefore` will\n            // append it to the children of the parent element.\n            parentElm.insertBefore(oldStartVnode.$elm$, oldEndVnode.$elm$.nextSibling);\n            oldStartVnode = oldCh[++oldStartIdx];\n            newEndVnode = newCh[--newEndIdx];\n        }\n        else if (isSameVnode(oldEndVnode, newStartVnode, isInitialRender)) {\n            // case: \"Vnode moved left\"\n            //\n            // We've found that the first node in our window on the new children is\n            // the same VNode as the _last_ node in our window on the old children.\n            // Visually, this is the layout of these two nodes:\n            //\n            // newCh: [..., newStartVnode , ... , newEndVnode , ...]\n            //              ^^^^^^^^^^^^^\n            // oldCh: [..., oldStartVnode , ... , oldEndVnode , ...]\n            //                                    ^^^^^^^^^^^\n            //\n            // In this situation we need to patch `newStartVnode` onto `oldEndVnode`\n            // (which will handle updating any changed attributes, reconciling their\n            // children etc) but we also need to move the DOM node to which\n            // `oldEndVnode` corresponds.\n            if (BUILD.slotRelocation && (oldStartVnode.$tag$ === 'slot' || newEndVnode.$tag$ === 'slot')) {\n                putBackInOriginalLocation(oldEndVnode.$elm$.parentNode, false);\n            }\n            patch(oldEndVnode, newStartVnode, isInitialRender);\n            // We've already checked above if `oldStartVnode` and `newStartVnode` are\n            // the same node, so since we're here we know that they are not. Thus we\n            // can move the element for `oldEndVnode` _before_ the element for\n            // `oldStartVnode`, leaving `oldStartVnode` to be reconciled in the\n            // future.\n            parentElm.insertBefore(oldEndVnode.$elm$, oldStartVnode.$elm$);\n            oldEndVnode = oldCh[--oldEndIdx];\n            newStartVnode = newCh[++newStartIdx];\n        }\n        else {\n            // Here we do some checks to match up old and new nodes based on the\n            // `$key$` attribute, which is set by putting a `key=\"my-key\"` attribute\n            // in the JSX for a DOM element in the implementation of a Stencil\n            // component.\n            //\n            // First we check to see if there are any nodes in the array of old\n            // children which have the same key as the first node in the new\n            // children.\n            idxInOld = -1;\n            if (BUILD.vdomKey) {\n                for (i = oldStartIdx; i <= oldEndIdx; ++i) {\n                    if (oldCh[i] && oldCh[i].$key$ !== null && oldCh[i].$key$ === newStartVnode.$key$) {\n                        idxInOld = i;\n                        break;\n                    }\n                }\n            }\n            if (BUILD.vdomKey && idxInOld >= 0) {\n                // We found a node in the old children which matches up with the first\n                // node in the new children! So let's deal with that\n                elmToMove = oldCh[idxInOld];\n                if (elmToMove.$tag$ !== newStartVnode.$tag$) {\n                    // the tag doesn't match so we'll need a new DOM element\n                    node = createElm(oldCh && oldCh[newStartIdx], newVNode, idxInOld, parentElm);\n                }\n                else {\n                    patch(elmToMove, newStartVnode, isInitialRender);\n                    // invalidate the matching old node so that we won't try to update it\n                    // again later on\n                    oldCh[idxInOld] = undefined;\n                    node = elmToMove.$elm$;\n                }\n                newStartVnode = newCh[++newStartIdx];\n            }\n            else {\n                // We either didn't find an element in the old children that matches\n                // the key of the first new child OR the build is not using `key`\n                // attributes at all. In either case we need to create a new element\n                // for the new node.\n                node = createElm(oldCh && oldCh[newStartIdx], newVNode, newStartIdx, parentElm);\n                newStartVnode = newCh[++newStartIdx];\n            }\n            if (node) {\n                // if we created a new node then handle inserting it to the DOM\n                if (BUILD.slotRelocation) {\n                    parentReferenceNode(oldStartVnode.$elm$).insertBefore(node, referenceNode(oldStartVnode.$elm$));\n                }\n                else {\n                    oldStartVnode.$elm$.parentNode.insertBefore(node, oldStartVnode.$elm$);\n                }\n            }\n        }\n    }\n    if (oldStartIdx > oldEndIdx) {\n        // we have some more new nodes to add which don't match up with old nodes\n        addVnodes(parentElm, newCh[newEndIdx + 1] == null ? null : newCh[newEndIdx + 1].$elm$, newVNode, newCh, newStartIdx, newEndIdx);\n    }\n    else if (BUILD.updatable && newStartIdx > newEndIdx) {\n        // there are nodes in the `oldCh` array which no longer correspond to nodes\n        // in the new array, so lets remove them (which entails cleaning up the\n        // relevant DOM nodes)\n        removeVnodes(oldCh, oldStartIdx, oldEndIdx);\n    }\n};\n/**\n * Compare two VNodes to determine if they are the same\n *\n * **NB**: This function is an equality _heuristic_ based on the available\n * information set on the two VNodes and can be misleading under certain\n * circumstances. In particular, if the two nodes do not have `key` attrs\n * (available under `$key$` on VNodes) then the function falls back on merely\n * checking that they have the same tag.\n *\n * So, in other words, if `key` attrs are not set on VNodes which may be\n * changing order within a `children` array or something along those lines then\n * we could obtain a false negative and then have to do needless re-rendering\n * (i.e. we'd say two VNodes aren't equal when in fact they should be).\n *\n * @param leftVNode the first VNode to check\n * @param rightVNode the second VNode to check\n * @param isInitialRender whether or not this is the first render of the vdom\n * @returns whether they're equal or not\n */\nconst isSameVnode = (leftVNode, rightVNode, isInitialRender = false) => {\n    // compare if two vnode to see if they're \"technically\" the same\n    // need to have the same element tag, and same key to be the same\n    if (leftVNode.$tag$ === rightVNode.$tag$) {\n        if (BUILD.slotRelocation && leftVNode.$tag$ === 'slot') {\n            return leftVNode.$name$ === rightVNode.$name$;\n        }\n        // this will be set if JSX tags in the build have `key` attrs set on them\n        // we only want to check this if we're not on the first render since on\n        // first render `leftVNode.$key$` will always be `null`, so we can be led\n        // astray and, for instance, accidentally delete a DOM node that we want to\n        // keep around.\n        if (BUILD.vdomKey && !isInitialRender) {\n            return leftVNode.$key$ === rightVNode.$key$;\n        }\n        return true;\n    }\n    return false;\n};\nconst referenceNode = (node) => {\n    // this node was relocated to a new location in the dom\n    // because of some other component's slot\n    // but we still have an html comment in place of where\n    // it's original location was according to it's original vdom\n    return (node && node['s-ol']) || node;\n};\nconst parentReferenceNode = (node) => (node['s-ol'] ? node['s-ol'] : node).parentNode;\n/**\n * Handle reconciling an outdated VNode with a new one which corresponds to\n * it. This function handles flushing updates to the DOM and reconciling the\n * children of the two nodes (if any).\n *\n * @param oldVNode an old VNode whose DOM element and children we want to update\n * @param newVNode a new VNode representing an updated version of the old one\n * @param isInitialRender whether or not this is the first render of the vdom\n */\nconst patch = (oldVNode, newVNode, isInitialRender = false) => {\n    const elm = (newVNode.$elm$ = oldVNode.$elm$);\n    const oldChildren = oldVNode.$children$;\n    const newChildren = newVNode.$children$;\n    const tag = newVNode.$tag$;\n    const text = newVNode.$text$;\n    let defaultHolder;\n    if (!BUILD.vdomText || text === null) {\n        if (BUILD.svg) {\n            // test if we're rendering an svg element, or still rendering nodes inside of one\n            // only add this to the when the compiler sees we're using an svg somewhere\n            isSvgMode = tag === 'svg' ? true : tag === 'foreignObject' ? false : isSvgMode;\n        }\n        if (BUILD.vdomAttribute || BUILD.reflect) {\n            if (BUILD.slot && tag === 'slot')\n                ;\n            else {\n                // either this is the first render of an element OR it's an update\n                // AND we already know it's possible it could have changed\n                // this updates the element's css classes, attrs, props, listeners, etc.\n                updateElement(oldVNode, newVNode, isSvgMode);\n            }\n        }\n        if (BUILD.updatable && oldChildren !== null && newChildren !== null) {\n            // looks like there's child vnodes for both the old and new vnodes\n            // so we need to call `updateChildren` to reconcile them\n            updateChildren(elm, oldChildren, newVNode, newChildren, isInitialRender);\n        }\n        else if (newChildren !== null) {\n            // no old child vnodes, but there are new child vnodes to add\n            if (BUILD.updatable && BUILD.vdomText && oldVNode.$text$ !== null) {\n                // the old vnode was text, so be sure to clear it out\n                elm.textContent = '';\n            }\n            // add the new vnode children\n            addVnodes(elm, null, newVNode, newChildren, 0, newChildren.length - 1);\n        }\n        else if (BUILD.updatable && oldChildren !== null) {\n            // no new child vnodes, but there are old child vnodes to remove\n            removeVnodes(oldChildren, 0, oldChildren.length - 1);\n        }\n        if (BUILD.svg && isSvgMode && tag === 'svg') {\n            isSvgMode = false;\n        }\n    }\n    else if (BUILD.vdomText && BUILD.slotRelocation && (defaultHolder = elm['s-cr'])) {\n        // this element has slotted content\n        defaultHolder.parentNode.textContent = text;\n    }\n    else if (BUILD.vdomText && oldVNode.$text$ !== text) {\n        // update the text content for the text only vnode\n        // and also only if the text is different than before\n        elm.data = text;\n    }\n};\n/**\n * Adjust the `.hidden` property as-needed on any nodes in a DOM subtree which\n * are slot fallbacks nodes.\n *\n * A slot fallback node should be visible by default. Then, it should be\n * conditionally hidden if:\n *\n * - it has a sibling with a `slot` property set to its slot name or if\n * - it is a default fallback slot node, in which case we hide if it has any\n *   content\n *\n * @param elm the element of interest\n */\nconst updateFallbackSlotVisibility = (elm) => {\n    const childNodes = elm.childNodes;\n    for (const childNode of childNodes) {\n        if (childNode.nodeType === 1 /* NODE_TYPE.ElementNode */) {\n            if (childNode['s-sr']) {\n                // this is a slot fallback node\n                // get the slot name for this slot reference node\n                const slotName = childNode['s-sn'];\n                // by default always show a fallback slot node\n                // then hide it if there are other slots in the light dom\n                childNode.hidden = false;\n                // we need to check all of its sibling nodes in order to see if\n                // `childNode` should be hidden\n                for (const siblingNode of childNodes) {\n                    // Don't check the node against itself\n                    if (siblingNode !== childNode) {\n                        if (siblingNode['s-hn'] !== childNode['s-hn'] || slotName !== '') {\n                            // this sibling node is from a different component OR is a named\n                            // fallback slot node\n                            if (siblingNode.nodeType === 1 /* NODE_TYPE.ElementNode */ &&\n                                (slotName === siblingNode.getAttribute('slot') || slotName === siblingNode['s-sn'])) {\n                                childNode.hidden = true;\n                                break;\n                            }\n                        }\n                        else {\n                            // this is a default fallback slot node\n                            // any element or text node (with content)\n                            // should hide the default fallback slot node\n                            if (siblingNode.nodeType === 1 /* NODE_TYPE.ElementNode */ ||\n                                (siblingNode.nodeType === 3 /* NODE_TYPE.TextNode */ && siblingNode.textContent.trim() !== '')) {\n                                childNode.hidden = true;\n                                break;\n                            }\n                        }\n                    }\n                }\n            }\n            // keep drilling down\n            updateFallbackSlotVisibility(childNode);\n        }\n    }\n};\n/**\n * Component-global information about nodes which are either currently being\n * relocated or will be shortly.\n */\nconst relocateNodes = [];\n/**\n * Mark the contents of a slot for relocation via adding references to them to\n * the {@link relocateNodes} data structure. The actual work of relocating them\n * will then be handled in {@link renderVdom}.\n *\n * @param elm a render node whose child nodes need to be relocated\n */\nconst markSlotContentForRelocation = (elm) => {\n    // tslint:disable-next-line: prefer-const\n    let node;\n    let hostContentNodes;\n    let j;\n    for (const childNode of elm.childNodes) {\n        // we need to find child nodes which are slot references so we can then try\n        // to match them up with nodes that need to be relocated\n        if (childNode['s-sr'] && (node = childNode['s-cr']) && node.parentNode) {\n            // first get the content reference comment node ('s-cr'), then we get\n            // its parent, which is where all the host content is now\n            hostContentNodes = node.parentNode.childNodes;\n            const slotName = childNode['s-sn'];\n            // iterate through all the nodes under the location where the host was\n            // originally rendered\n            for (j = hostContentNodes.length - 1; j >= 0; j--) {\n                node = hostContentNodes[j];\n                // check that the node is not a content reference node or a node\n                // reference and then check that the host name does not match that of\n                // childNode.\n                // In addition, check that the slot either has not already been relocated, or\n                // that its current location's host is not childNode's host. This is essentially\n                // a check so that we don't try to relocate (and then hide) a node that is already\n                // where it should be.\n                if (!node['s-cn'] &&\n                    !node['s-nr'] &&\n                    node['s-hn'] !== childNode['s-hn'] &&\n                    (!BUILD.experimentalSlotFixes || !node['s-sh'] || node['s-sh'] !== childNode['s-hn'])) {\n                    // if `node` is located in the slot that `childNode` refers to (via the\n                    // `'s-sn'` property) then we need to relocate it from it's current spot\n                    // (under the host element parent) to the right slot location\n                    if (isNodeLocatedInSlot(node, slotName)) {\n                        // it's possible we've already decided to relocate this node\n                        let relocateNodeData = relocateNodes.find((r) => r.$nodeToRelocate$ === node);\n                        // made some changes to slots\n                        // let's make sure we also double check\n                        // fallbacks are correctly hidden or shown\n                        checkSlotFallbackVisibility = true;\n                        // ensure that the slot-name attr is correct\n                        node['s-sn'] = node['s-sn'] || slotName;\n                        if (relocateNodeData) {\n                            relocateNodeData.$nodeToRelocate$['s-sh'] = childNode['s-hn'];\n                            // we marked this node for relocation previously but didn't find\n                            // out the slot reference node to which it needs to be relocated\n                            // so write it down now!\n                            relocateNodeData.$slotRefNode$ = childNode;\n                        }\n                        else {\n                            node['s-sh'] = childNode['s-hn'];\n                            // add to our list of nodes to relocate\n                            relocateNodes.push({\n                                $slotRefNode$: childNode,\n                                $nodeToRelocate$: node,\n                            });\n                        }\n                        if (node['s-sr']) {\n                            relocateNodes.map((relocateNode) => {\n                                if (isNodeLocatedInSlot(relocateNode.$nodeToRelocate$, node['s-sn'])) {\n                                    relocateNodeData = relocateNodes.find((r) => r.$nodeToRelocate$ === node);\n                                    if (relocateNodeData && !relocateNode.$slotRefNode$) {\n                                        relocateNode.$slotRefNode$ = relocateNodeData.$slotRefNode$;\n                                    }\n                                }\n                            });\n                        }\n                    }\n                    else if (!relocateNodes.some((r) => r.$nodeToRelocate$ === node)) {\n                        // the node is not found within the slot (`childNode`) that we're\n                        // currently looking at, so we stick it into `relocateNodes` to\n                        // handle later. If we never find a home for this element then\n                        // we'll need to hide it\n                        relocateNodes.push({\n                            $nodeToRelocate$: node,\n                        });\n                    }\n                }\n            }\n        }\n        // if we're dealing with any type of element (capable of itself being a\n        // slot reference or containing one) then we recur\n        if (childNode.nodeType === 1 /* NODE_TYPE.ElementNode */) {\n            markSlotContentForRelocation(childNode);\n        }\n    }\n};\n/**\n * Check whether a node is located in a given named slot.\n *\n * @param nodeToRelocate the node of interest\n * @param slotName the slot name to check\n * @returns whether the node is located in the slot or not\n */\nconst isNodeLocatedInSlot = (nodeToRelocate, slotName) => {\n    if (nodeToRelocate.nodeType === 1 /* NODE_TYPE.ElementNode */) {\n        if (nodeToRelocate.getAttribute('slot') === null && slotName === '') {\n            // if the node doesn't have a slot attribute, and the slot we're checking\n            // is not a named slot, then we assume the node should be within the slot\n            return true;\n        }\n        if (nodeToRelocate.getAttribute('slot') === slotName) {\n            return true;\n        }\n        return false;\n    }\n    if (nodeToRelocate['s-sn'] === slotName) {\n        return true;\n    }\n    return slotName === '';\n};\n/**\n * 'Nullify' any VDom `ref` callbacks on a VDom node or its children by calling\n * them with `null`. This signals that the DOM element corresponding to the VDom\n * node has been removed from the DOM.\n *\n * @param vNode a virtual DOM node\n */\nconst nullifyVNodeRefs = (vNode) => {\n    if (BUILD.vdomRef) {\n        vNode.$attrs$ && vNode.$attrs$.ref && vNode.$attrs$.ref(null);\n        vNode.$children$ && vNode.$children$.map(nullifyVNodeRefs);\n    }\n};\n/**\n * The main entry point for Stencil's virtual DOM-based rendering engine\n *\n * Given a {@link d.HostRef} container and some virtual DOM nodes, this\n * function will handle creating a virtual DOM tree with a single root, patching\n * the current virtual DOM tree onto an old one (if any), dealing with slot\n * relocation, and reflecting attributes.\n *\n * @param hostRef data needed to root and render the virtual DOM tree, such as\n * the DOM node into which it should be rendered.\n * @param renderFnResults the virtual DOM nodes to be rendered\n * @param isInitialLoad whether or not this is the first call after page load\n */\nconst renderVdom = (hostRef, renderFnResults, isInitialLoad = false) => {\n    var _a, _b, _c, _d, _e;\n    const hostElm = hostRef.$hostElement$;\n    const cmpMeta = hostRef.$cmpMeta$;\n    const oldVNode = hostRef.$vnode$ || newVNode(null, null);\n    // if `renderFnResults` is a Host node then we can use it directly. If not,\n    // we need to call `h` again to wrap the children of our component in a\n    // 'dummy' Host node (well, an empty vnode) since `renderVdom` assumes\n    // implicitly that the top-level vdom node is 1) an only child and 2)\n    // contains attrs that need to be set on the host element.\n    const rootVnode = isHost(renderFnResults) ? renderFnResults : h(null, null, renderFnResults);\n    hostTagName = hostElm.tagName;\n    // <Host> runtime check\n    if (BUILD.isDev && Array.isArray(renderFnResults) && renderFnResults.some(isHost)) {\n        throw new Error(`The <Host> must be the single root component.\nLooks like the render() function of \"${hostTagName.toLowerCase()}\" is returning an array that contains the <Host>.\n\nThe render() function should look like this instead:\n\nrender() {\n  // Do not return an array\n  return (\n    <Host>{content}</Host>\n  );\n}\n  `);\n    }\n    if (BUILD.reflect && cmpMeta.$attrsToReflect$) {\n        rootVnode.$attrs$ = rootVnode.$attrs$ || {};\n        cmpMeta.$attrsToReflect$.map(([propName, attribute]) => (rootVnode.$attrs$[attribute] = hostElm[propName]));\n    }\n    // On the first render and *only* on the first render we want to check for\n    // any attributes set on the host element which are also set on the vdom\n    // node. If we find them, we override the value on the VDom node attrs with\n    // the value from the host element, which allows developers building apps\n    // with Stencil components to override e.g. the `role` attribute on a\n    // component even if it's already set on the `Host`.\n    if (isInitialLoad && rootVnode.$attrs$) {\n        for (const key of Object.keys(rootVnode.$attrs$)) {\n            // We have a special implementation in `setAccessor` for `style` and\n            // `class` which reconciles values coming from the VDom with values\n            // already present on the DOM element, so we don't want to override those\n            // attributes on the VDom tree with values from the host element if they\n            // are present.\n            //\n            // Likewise, `ref` and `key` are special internal values for the Stencil\n            // runtime and we don't want to override those either.\n            if (hostElm.hasAttribute(key) && !['key', 'ref', 'style', 'class'].includes(key)) {\n                rootVnode.$attrs$[key] = hostElm[key];\n            }\n        }\n    }\n    rootVnode.$tag$ = null;\n    rootVnode.$flags$ |= 4 /* VNODE_FLAGS.isHost */;\n    hostRef.$vnode$ = rootVnode;\n    rootVnode.$elm$ = oldVNode.$elm$ = (BUILD.shadowDom ? hostElm.shadowRoot || hostElm : hostElm);\n    if (BUILD.scoped || BUILD.shadowDom) {\n        scopeId = hostElm['s-sc'];\n    }\n    if (BUILD.slotRelocation) {\n        contentRef = hostElm['s-cr'];\n        useNativeShadowDom = supportsShadow && (cmpMeta.$flags$ & 1 /* CMP_FLAGS.shadowDomEncapsulation */) !== 0;\n        // always reset\n        checkSlotFallbackVisibility = false;\n    }\n    // synchronous patch\n    patch(oldVNode, rootVnode, isInitialLoad);\n    if (BUILD.slotRelocation) {\n        // while we're moving nodes around existing nodes, temporarily disable\n        // the disconnectCallback from working\n        plt.$flags$ |= 1 /* PLATFORM_FLAGS.isTmpDisconnected */;\n        if (checkSlotRelocate) {\n            markSlotContentForRelocation(rootVnode.$elm$);\n            for (const relocateData of relocateNodes) {\n                const nodeToRelocate = relocateData.$nodeToRelocate$;\n                if (!nodeToRelocate['s-ol']) {\n                    // add a reference node marking this node's original location\n                    // keep a reference to this node for later lookups\n                    const orgLocationNode = BUILD.isDebug || BUILD.hydrateServerSide\n                        ? originalLocationDebugNode(nodeToRelocate)\n                        : doc.createTextNode('');\n                    orgLocationNode['s-nr'] = nodeToRelocate;\n                    nodeToRelocate.parentNode.insertBefore((nodeToRelocate['s-ol'] = orgLocationNode), nodeToRelocate);\n                }\n            }\n            for (const relocateData of relocateNodes) {\n                const nodeToRelocate = relocateData.$nodeToRelocate$;\n                const slotRefNode = relocateData.$slotRefNode$;\n                if (slotRefNode) {\n                    const parentNodeRef = slotRefNode.parentNode;\n                    // When determining where to insert content, the most simple case would be\n                    // to relocate the node immediately following the slot reference node. We do this\n                    // by getting a reference to the node immediately following the slot reference node\n                    // since we will use `insertBefore` to manipulate the DOM.\n                    //\n                    // If there is no node immediately following the slot reference node, then we will just\n                    // end up appending the node as the last child of the parent.\n                    let insertBeforeNode = slotRefNode.nextSibling;\n                    // If the node we're currently planning on inserting the new node before is an element,\n                    // we need to do some additional checks to make sure we're inserting the node in the correct order.\n                    // The use case here would be that we have multiple nodes being relocated to the same slot. So, we want\n                    // to make sure they get inserted into their new how in the same order they were declared in their original location.\n                    //\n                    // TODO(STENCIL-914): Remove `experimentalSlotFixes` check\n                    if (!BUILD.experimentalSlotFixes ||\n                        (insertBeforeNode && insertBeforeNode.nodeType === 1 /* NODE_TYPE.ElementNode */)) {\n                        let orgLocationNode = (_a = nodeToRelocate['s-ol']) === null || _a === void 0 ? void 0 : _a.previousSibling;\n                        while (orgLocationNode) {\n                            let refNode = (_b = orgLocationNode['s-nr']) !== null && _b !== void 0 ? _b : null;\n                            if (refNode && refNode['s-sn'] === nodeToRelocate['s-sn'] && parentNodeRef === refNode.parentNode) {\n                                refNode = refNode.nextSibling;\n                                if (!refNode || !refNode['s-nr']) {\n                                    insertBeforeNode = refNode;\n                                    break;\n                                }\n                            }\n                            orgLocationNode = orgLocationNode.previousSibling;\n                        }\n                    }\n                    if ((!insertBeforeNode && parentNodeRef !== nodeToRelocate.parentNode) ||\n                        nodeToRelocate.nextSibling !== insertBeforeNode) {\n                        // we've checked that it's worth while to relocate\n                        // since that the node to relocate\n                        // has a different next sibling or parent relocated\n                        if (nodeToRelocate !== insertBeforeNode) {\n                            if (!BUILD.experimentalSlotFixes && !nodeToRelocate['s-hn'] && nodeToRelocate['s-ol']) {\n                                // probably a component in the index.html that doesn't have its hostname set\n                                nodeToRelocate['s-hn'] = nodeToRelocate['s-ol'].parentNode.nodeName;\n                            }\n                            // Add it back to the dom but in its new home\n                            // If we get to this point and `insertBeforeNode` is `null`, that means\n                            // we're just going to append the node as the last child of the parent. Passing\n                            // `null` as the second arg here will trigger that behavior.\n                            parentNodeRef.insertBefore(nodeToRelocate, insertBeforeNode);\n                            // Reset the `hidden` value back to what it was defined as originally\n                            // This solves a problem where a `slot` is dynamically rendered and `hidden` may have\n                            // been set on content originally, but now it has a slot to go to so it should have\n                            // the value it was defined as having in the DOM, not what we overrode it to.\n                            if (nodeToRelocate.nodeType === 1 /* NODE_TYPE.ElementNode */) {\n                                nodeToRelocate.hidden = (_c = nodeToRelocate['s-ih']) !== null && _c !== void 0 ? _c : false;\n                            }\n                        }\n                    }\n                }\n                else {\n                    // this node doesn't have a slot home to go to, so let's hide it\n                    if (nodeToRelocate.nodeType === 1 /* NODE_TYPE.ElementNode */) {\n                        // Store the initial value of `hidden` so we can reset it later when\n                        // moving nodes around.\n                        if (isInitialLoad) {\n                            nodeToRelocate['s-ih'] = (_d = nodeToRelocate.hidden) !== null && _d !== void 0 ? _d : false;\n                        }\n                        nodeToRelocate.hidden = true;\n                    }\n                }\n            }\n        }\n        if (checkSlotFallbackVisibility) {\n            updateFallbackSlotVisibility(rootVnode.$elm$);\n        }\n        // done moving nodes around\n        // allow the disconnect callback to work again\n        plt.$flags$ &= ~1 /* PLATFORM_FLAGS.isTmpDisconnected */;\n        // always reset\n        relocateNodes.length = 0;\n    }\n    // Hide any elements that were projected through, but don't have a slot to go to.\n    // Only an issue if there were no \"slots\" rendered. Otherwise, nodes are hidden correctly.\n    // This _only_ happens for `scoped` components!\n    if (BUILD.experimentalScopedSlotChanges && cmpMeta.$flags$ & 2 /* CMP_FLAGS.scopedCssEncapsulation */) {\n        for (const childNode of rootVnode.$elm$.childNodes) {\n            if (childNode['s-hn'] !== hostTagName && !childNode['s-sh']) {\n                // Store the initial value of `hidden` so we can reset it later when\n                // moving nodes around.\n                if (isInitialLoad && childNode['s-ih'] == null) {\n                    childNode['s-ih'] = (_e = childNode.hidden) !== null && _e !== void 0 ? _e : false;\n                }\n                childNode.hidden = true;\n            }\n        }\n    }\n    // Clear the content ref so we don't create a memory leak\n    contentRef = undefined;\n};\n// slot comment debug nodes only created with the `--debug` flag\n// otherwise these nodes are text nodes w/out content\nconst slotReferenceDebugNode = (slotVNode) => doc.createComment(`<slot${slotVNode.$name$ ? ' name=\"' + slotVNode.$name$ + '\"' : ''}> (host=${hostTagName.toLowerCase()})`);\nconst originalLocationDebugNode = (nodeToRelocate) => doc.createComment(`org-location for ` +\n    (nodeToRelocate.localName\n        ? `<${nodeToRelocate.localName}> (host=${nodeToRelocate['s-hn']})`\n        : `[${nodeToRelocate.textContent}]`));\nconst attachToAncestor = (hostRef, ancestorComponent) => {\n    if (BUILD.asyncLoading && ancestorComponent && !hostRef.$onRenderResolve$ && ancestorComponent['s-p']) {\n        ancestorComponent['s-p'].push(new Promise((r) => (hostRef.$onRenderResolve$ = r)));\n    }\n};\nconst scheduleUpdate = (hostRef, isInitialLoad) => {\n    if (BUILD.taskQueue && BUILD.updatable) {\n        hostRef.$flags$ |= 16 /* HOST_FLAGS.isQueuedForUpdate */;\n    }\n    if (BUILD.asyncLoading && hostRef.$flags$ & 4 /* HOST_FLAGS.isWaitingForChildren */) {\n        hostRef.$flags$ |= 512 /* HOST_FLAGS.needsRerender */;\n        return;\n    }\n    attachToAncestor(hostRef, hostRef.$ancestorComponent$);\n    // there is no ancestor component or the ancestor component\n    // has already fired off its lifecycle update then\n    // fire off the initial update\n    const dispatch = () => dispatchHooks(hostRef, isInitialLoad);\n    return BUILD.taskQueue ? writeTask(dispatch) : dispatch();\n};\n/**\n * Dispatch initial-render and update lifecycle hooks, enqueuing calls to\n * component lifecycle methods like `componentWillLoad` as well as\n * {@link updateComponent}, which will kick off the virtual DOM re-render.\n *\n * @param hostRef a reference to a host DOM node\n * @param isInitialLoad whether we're on the initial load or not\n * @returns an empty Promise which is used to enqueue a series of operations for\n * the component\n */\nconst dispatchHooks = (hostRef, isInitialLoad) => {\n    const elm = hostRef.$hostElement$;\n    const endSchedule = createTime('scheduleUpdate', hostRef.$cmpMeta$.$tagName$);\n    const instance = BUILD.lazyLoad ? hostRef.$lazyInstance$ : elm;\n    // We're going to use this variable together with `enqueue` to implement a\n    // little promise-based queue. We start out with it `undefined`. When we add\n    // the first function to the queue we'll set this variable to be that\n    // function's return value. When we attempt to add subsequent values to the\n    // queue we'll check that value and, if it was a `Promise`, we'll then chain\n    // the new function off of that `Promise` using `.then()`. This will give our\n    // queue two nice properties:\n    //\n    // 1. If all functions added to the queue are synchronous they'll be called\n    //    synchronously right away.\n    // 2. If all functions added to the queue are asynchronous they'll all be\n    //    called in order after `dispatchHooks` exits.\n    let maybePromise;\n    if (isInitialLoad) {\n        if (BUILD.lazyLoad && BUILD.hostListener) {\n            hostRef.$flags$ |= 256 /* HOST_FLAGS.isListenReady */;\n            if (hostRef.$queuedListeners$) {\n                hostRef.$queuedListeners$.map(([methodName, event]) => safeCall(instance, methodName, event));\n                hostRef.$queuedListeners$ = undefined;\n            }\n        }\n        emitLifecycleEvent(elm, 'componentWillLoad');\n        if (BUILD.cmpWillLoad) {\n            // If `componentWillLoad` returns a `Promise` then we want to wait on\n            // whatever's going on in that `Promise` before we launch into\n            // rendering the component, doing other lifecycle stuff, etc. So\n            // in that case we assign the returned promise to the variable we\n            // declared above to hold a possible 'queueing' Promise\n            maybePromise = safeCall(instance, 'componentWillLoad');\n        }\n    }\n    else {\n        emitLifecycleEvent(elm, 'componentWillUpdate');\n        if (BUILD.cmpWillUpdate) {\n            // Like `componentWillLoad` above, we allow Stencil component\n            // authors to return a `Promise` from this lifecycle callback, and\n            // we specify that our runtime will wait for that `Promise` to\n            // resolve before the component re-renders. So if the method\n            // returns a `Promise` we need to keep it around!\n            maybePromise = safeCall(instance, 'componentWillUpdate');\n        }\n    }\n    emitLifecycleEvent(elm, 'componentWillRender');\n    if (BUILD.cmpWillRender) {\n        maybePromise = enqueue(maybePromise, () => safeCall(instance, 'componentWillRender'));\n    }\n    endSchedule();\n    return enqueue(maybePromise, () => updateComponent(hostRef, instance, isInitialLoad));\n};\n/**\n * This function uses a Promise to implement a simple first-in, first-out queue\n * of functions to be called.\n *\n * The queue is ordered on the basis of the first argument. If it's\n * `undefined`, then nothing is on the queue yet, so the provided function can\n * be called synchronously (although note that this function may return a\n * `Promise`). The idea is that then the return value of that enqueueing\n * operation is kept around, so that if it was a `Promise` then subsequent\n * functions can be enqueued by calling this function again with that `Promise`\n * as the first argument.\n *\n * @param maybePromise either a `Promise` which should resolve before the next function is called or an 'empty' sentinel\n * @param fn a function to enqueue\n * @returns either a `Promise` or the return value of the provided function\n */\nconst enqueue = (maybePromise, fn) => isPromisey(maybePromise) ? maybePromise.then(fn) : fn();\n/**\n * Check that a value is a `Promise`. To check, we first see if the value is an\n * instance of the `Promise` global. In a few circumstances, in particular if\n * the global has been overwritten, this is could be misleading, so we also do\n * a little 'duck typing' check to see if the `.then` property of the value is\n * defined and a function.\n *\n * @param maybePromise it might be a promise!\n * @returns whether it is or not\n */\nconst isPromisey = (maybePromise) => maybePromise instanceof Promise ||\n    (maybePromise && maybePromise.then && typeof maybePromise.then === 'function');\n/**\n * Update a component given reference to its host elements and so on.\n *\n * @param hostRef an object containing references to the element's host node,\n * VDom nodes, and other metadata\n * @param instance a reference to the underlying host element where it will be\n * rendered\n * @param isInitialLoad whether or not this function is being called as part of\n * the first render cycle\n */\nconst updateComponent = async (hostRef, instance, isInitialLoad) => {\n    var _a;\n    const elm = hostRef.$hostElement$;\n    const endUpdate = createTime('update', hostRef.$cmpMeta$.$tagName$);\n    const rc = elm['s-rc'];\n    if (BUILD.style && isInitialLoad) {\n        // DOM WRITE!\n        attachStyles(hostRef);\n    }\n    const endRender = createTime('render', hostRef.$cmpMeta$.$tagName$);\n    if (BUILD.isDev) {\n        hostRef.$flags$ |= 1024 /* HOST_FLAGS.devOnRender */;\n    }\n    if (BUILD.hydrateServerSide) {\n        await callRender(hostRef, instance, elm, isInitialLoad);\n    }\n    else {\n        callRender(hostRef, instance, elm, isInitialLoad);\n    }\n    if (BUILD.isDev) {\n        hostRef.$renderCount$ = hostRef.$renderCount$ === undefined ? 1 : hostRef.$renderCount$ + 1;\n        hostRef.$flags$ &= ~1024 /* HOST_FLAGS.devOnRender */;\n    }\n    if (BUILD.hydrateServerSide) {\n        try {\n            // manually connected child components during server-side hydrate\n            serverSideConnected(elm);\n            if (isInitialLoad) {\n                // using only during server-side hydrate\n                if (hostRef.$cmpMeta$.$flags$ & 1 /* CMP_FLAGS.shadowDomEncapsulation */) {\n                    elm['s-en'] = '';\n                }\n                else if (hostRef.$cmpMeta$.$flags$ & 2 /* CMP_FLAGS.scopedCssEncapsulation */) {\n                    elm['s-en'] = 'c';\n                }\n            }\n        }\n        catch (e) {\n            consoleError(e, elm);\n        }\n    }\n    if (BUILD.asyncLoading && rc) {\n        // ok, so turns out there are some child host elements\n        // waiting on this parent element to load\n        // let's fire off all update callbacks waiting\n        rc.map((cb) => cb());\n        elm['s-rc'] = undefined;\n    }\n    endRender();\n    endUpdate();\n    if (BUILD.asyncLoading) {\n        const childrenPromises = (_a = elm['s-p']) !== null && _a !== void 0 ? _a : [];\n        const postUpdate = () => postUpdateComponent(hostRef);\n        if (childrenPromises.length === 0) {\n            postUpdate();\n        }\n        else {\n            Promise.all(childrenPromises).then(postUpdate);\n            hostRef.$flags$ |= 4 /* HOST_FLAGS.isWaitingForChildren */;\n            childrenPromises.length = 0;\n        }\n    }\n    else {\n        postUpdateComponent(hostRef);\n    }\n};\n/**\n * Handle making the call to the VDom renderer with the proper context given\n * various build variables\n *\n * @param hostRef an object containing references to the element's host node,\n * VDom nodes, and other metadata\n * @param instance a reference to the underlying host element where it will be\n * rendered\n * @param elm the Host element for the component\n * @param isInitialLoad whether or not this function is being called as part of\n * @returns an empty promise\n */\nconst callRender = (hostRef, instance, elm, isInitialLoad) => {\n    // in order for bundlers to correctly tree-shake the BUILD object\n    // we need to ensure BUILD is not deoptimized within a try/catch\n    // https://rollupjs.org/guide/en/#treeshake tryCatchDeoptimization\n    const allRenderFn = BUILD.allRenderFn ? true : false;\n    const lazyLoad = BUILD.lazyLoad ? true : false;\n    const taskQueue = BUILD.taskQueue ? true : false;\n    const updatable = BUILD.updatable ? true : false;\n    try {\n        renderingRef = instance;\n        /**\n         * minification optimization: `allRenderFn` is `true` if all components have a `render`\n         * method, so we can call the method immediately. If not, check before calling it.\n         */\n        instance = allRenderFn ? instance.render() : instance.render && instance.render();\n        if (updatable && taskQueue) {\n            hostRef.$flags$ &= ~16 /* HOST_FLAGS.isQueuedForUpdate */;\n        }\n        if (updatable || lazyLoad) {\n            hostRef.$flags$ |= 2 /* HOST_FLAGS.hasRendered */;\n        }\n        if (BUILD.hasRenderFn || BUILD.reflect) {\n            if (BUILD.vdomRender || BUILD.reflect) {\n                // looks like we've got child nodes to render into this host element\n                // or we need to update the css class/attrs on the host element\n                // DOM WRITE!\n                if (BUILD.hydrateServerSide) {\n                    return Promise.resolve(instance).then((value) => renderVdom(hostRef, value, isInitialLoad));\n                }\n                else {\n                    renderVdom(hostRef, instance, isInitialLoad);\n                }\n            }\n            else {\n                const shadowRoot = elm.shadowRoot;\n                if (hostRef.$cmpMeta$.$flags$ & 1 /* CMP_FLAGS.shadowDomEncapsulation */) {\n                    shadowRoot.textContent = instance;\n                }\n                else {\n                    elm.textContent = instance;\n                }\n            }\n        }\n    }\n    catch (e) {\n        consoleError(e, hostRef.$hostElement$);\n    }\n    renderingRef = null;\n    return null;\n};\nconst getRenderingRef = () => renderingRef;\nconst postUpdateComponent = (hostRef) => {\n    const tagName = hostRef.$cmpMeta$.$tagName$;\n    const elm = hostRef.$hostElement$;\n    const endPostUpdate = createTime('postUpdate', tagName);\n    const instance = BUILD.lazyLoad ? hostRef.$lazyInstance$ : elm;\n    const ancestorComponent = hostRef.$ancestorComponent$;\n    if (BUILD.cmpDidRender) {\n        if (BUILD.isDev) {\n            hostRef.$flags$ |= 1024 /* HOST_FLAGS.devOnRender */;\n        }\n        safeCall(instance, 'componentDidRender');\n        if (BUILD.isDev) {\n            hostRef.$flags$ &= ~1024 /* HOST_FLAGS.devOnRender */;\n        }\n    }\n    emitLifecycleEvent(elm, 'componentDidRender');\n    if (!(hostRef.$flags$ & 64 /* HOST_FLAGS.hasLoadedComponent */)) {\n        hostRef.$flags$ |= 64 /* HOST_FLAGS.hasLoadedComponent */;\n        if (BUILD.asyncLoading && BUILD.cssAnnotations) {\n            // DOM WRITE!\n            addHydratedFlag(elm);\n        }\n        if (BUILD.cmpDidLoad) {\n            if (BUILD.isDev) {\n                hostRef.$flags$ |= 2048 /* HOST_FLAGS.devOnDidLoad */;\n            }\n            safeCall(instance, 'componentDidLoad');\n            if (BUILD.isDev) {\n                hostRef.$flags$ &= ~2048 /* HOST_FLAGS.devOnDidLoad */;\n            }\n        }\n        emitLifecycleEvent(elm, 'componentDidLoad');\n        endPostUpdate();\n        if (BUILD.asyncLoading) {\n            hostRef.$onReadyResolve$(elm);\n            if (!ancestorComponent) {\n                appDidLoad(tagName);\n            }\n        }\n    }\n    else {\n        if (BUILD.cmpDidUpdate) {\n            // we've already loaded this component\n            // fire off the user's componentDidUpdate method (if one was provided)\n            // componentDidUpdate runs AFTER render() has been called\n            // and all child components have finished updating\n            if (BUILD.isDev) {\n                hostRef.$flags$ |= 1024 /* HOST_FLAGS.devOnRender */;\n            }\n            safeCall(instance, 'componentDidUpdate');\n            if (BUILD.isDev) {\n                hostRef.$flags$ &= ~1024 /* HOST_FLAGS.devOnRender */;\n            }\n        }\n        emitLifecycleEvent(elm, 'componentDidUpdate');\n        endPostUpdate();\n    }\n    if (BUILD.method && BUILD.lazyLoad) {\n        hostRef.$onInstanceResolve$(elm);\n    }\n    // load events fire from bottom to top\n    // the deepest elements load first then bubbles up\n    if (BUILD.asyncLoading) {\n        if (hostRef.$onRenderResolve$) {\n            hostRef.$onRenderResolve$();\n            hostRef.$onRenderResolve$ = undefined;\n        }\n        if (hostRef.$flags$ & 512 /* HOST_FLAGS.needsRerender */) {\n            nextTick(() => scheduleUpdate(hostRef, false));\n        }\n        hostRef.$flags$ &= ~(4 /* HOST_FLAGS.isWaitingForChildren */ | 512 /* HOST_FLAGS.needsRerender */);\n    }\n    // ( •_•)\n    // ( •_•)>⌐■-■\n    // (⌐■_■)\n};\nconst forceUpdate = (ref) => {\n    if (BUILD.updatable && (Build.isBrowser || Build.isTesting)) {\n        const hostRef = getHostRef(ref);\n        const isConnected = hostRef.$hostElement$.isConnected;\n        if (isConnected &&\n            (hostRef.$flags$ & (2 /* HOST_FLAGS.hasRendered */ | 16 /* HOST_FLAGS.isQueuedForUpdate */)) === 2 /* HOST_FLAGS.hasRendered */) {\n            scheduleUpdate(hostRef, false);\n        }\n        // Returns \"true\" when the forced update was successfully scheduled\n        return isConnected;\n    }\n    return false;\n};\nconst appDidLoad = (who) => {\n    // on appload\n    // we have finish the first big initial render\n    if (BUILD.cssAnnotations) {\n        addHydratedFlag(doc.documentElement);\n    }\n    if (BUILD.asyncQueue) {\n        plt.$flags$ |= 2 /* PLATFORM_FLAGS.appLoaded */;\n    }\n    nextTick(() => emitEvent(win, 'appload', { detail: { namespace: NAMESPACE } }));\n    if (BUILD.profile && performance.measure) {\n        performance.measure(`[Stencil] ${NAMESPACE} initial load (by ${who})`, 'st:app:start');\n    }\n};\n/**\n * Allows to safely call a method, e.g. `componentDidLoad`, on an instance,\n * e.g. custom element node. If a build figures out that e.g. no component\n * has a `componentDidLoad` method, the instance method gets removed from the\n * output bundle and this function returns `undefined`.\n * @param instance any object that may or may not contain methods\n * @param method method name\n * @param arg single arbitrary argument\n * @returns result of method call if it exists, otherwise `undefined`\n */\nconst safeCall = (instance, method, arg) => {\n    if (instance && instance[method]) {\n        try {\n            return instance[method](arg);\n        }\n        catch (e) {\n            consoleError(e);\n        }\n    }\n    return undefined;\n};\n/**\n * For debugging purposes as `BUILD.lifecycleDOMEvents` is `false` by default and will\n * get removed by the compiler. Used for timing events to see how long they take.\n * @param elm the target of the Event\n * @param lifecycleName name of the event\n */\nconst emitLifecycleEvent = (elm, lifecycleName) => {\n    if (BUILD.lifecycleDOMEvents) {\n        emitEvent(elm, 'stencil_' + lifecycleName, {\n            bubbles: true,\n            composed: true,\n            detail: {\n                namespace: NAMESPACE,\n            },\n        });\n    }\n};\nconst addHydratedFlag = (elm) => BUILD.hydratedClass\n    ? elm.classList.add('hydrated')\n    : BUILD.hydratedAttribute\n        ? elm.setAttribute('hydrated', '')\n        : undefined;\nconst serverSideConnected = (elm) => {\n    const children = elm.children;\n    if (children != null) {\n        for (let i = 0, ii = children.length; i < ii; i++) {\n            const childElm = children[i];\n            if (typeof childElm.connectedCallback === 'function') {\n                childElm.connectedCallback();\n            }\n            serverSideConnected(childElm);\n        }\n    }\n};\nconst getValue = (ref, propName) => getHostRef(ref).$instanceValues$.get(propName);\nconst setValue = (ref, propName, newVal, cmpMeta) => {\n    // check our new property value against our internal value\n    const hostRef = getHostRef(ref);\n    const elm = BUILD.lazyLoad ? hostRef.$hostElement$ : ref;\n    const oldVal = hostRef.$instanceValues$.get(propName);\n    const flags = hostRef.$flags$;\n    const instance = BUILD.lazyLoad ? hostRef.$lazyInstance$ : elm;\n    newVal = parsePropertyValue(newVal, cmpMeta.$members$[propName][0]);\n    // explicitly check for NaN on both sides, as `NaN === NaN` is always false\n    const areBothNaN = Number.isNaN(oldVal) && Number.isNaN(newVal);\n    const didValueChange = newVal !== oldVal && !areBothNaN;\n    if ((!BUILD.lazyLoad || !(flags & 8 /* HOST_FLAGS.isConstructingInstance */) || oldVal === undefined) && didValueChange) {\n        // gadzooks! the property's value has changed!!\n        // set our new value!\n        hostRef.$instanceValues$.set(propName, newVal);\n        if (BUILD.isDev) {\n            if (hostRef.$flags$ & 1024 /* HOST_FLAGS.devOnRender */) {\n                consoleDevWarn(`The state/prop \"${propName}\" changed during rendering. This can potentially lead to infinite-loops and other bugs.`, '\\nElement', elm, '\\nNew value', newVal, '\\nOld value', oldVal);\n            }\n            else if (hostRef.$flags$ & 2048 /* HOST_FLAGS.devOnDidLoad */) {\n                consoleDevWarn(`The state/prop \"${propName}\" changed during \"componentDidLoad()\", this triggers extra re-renders, try to setup on \"componentWillLoad()\"`, '\\nElement', elm, '\\nNew value', newVal, '\\nOld value', oldVal);\n            }\n        }\n        if (!BUILD.lazyLoad || instance) {\n            // get an array of method names of watch functions to call\n            if (BUILD.watchCallback && cmpMeta.$watchers$ && flags & 128 /* HOST_FLAGS.isWatchReady */) {\n                const watchMethods = cmpMeta.$watchers$[propName];\n                if (watchMethods) {\n                    // this instance is watching for when this property changed\n                    watchMethods.map((watchMethodName) => {\n                        try {\n                            // fire off each of the watch methods that are watching this property\n                            instance[watchMethodName](newVal, oldVal, propName);\n                        }\n                        catch (e) {\n                            consoleError(e, elm);\n                        }\n                    });\n                }\n            }\n            if (BUILD.updatable &&\n                (flags & (2 /* HOST_FLAGS.hasRendered */ | 16 /* HOST_FLAGS.isQueuedForUpdate */)) === 2 /* HOST_FLAGS.hasRendered */) {\n                if (BUILD.cmpShouldUpdate && instance.componentShouldUpdate) {\n                    if (instance.componentShouldUpdate(newVal, oldVal, propName) === false) {\n                        return;\n                    }\n                }\n                // looks like this value actually changed, so we've got work to do!\n                // but only if we've already rendered, otherwise just chill out\n                // queue that we need to do an update, but don't worry about queuing\n                // up millions cuz this function ensures it only runs once\n                scheduleUpdate(hostRef, false);\n            }\n        }\n    }\n};\n/**\n * Attach a series of runtime constructs to a compiled Stencil component\n * constructor, including getters and setters for the `@Prop` and `@State`\n * decorators, callbacks for when attributes change, and so on.\n *\n * @param Cstr the constructor for a component that we need to process\n * @param cmpMeta metadata collected previously about the component\n * @param flags a number used to store a series of bit flags\n * @returns a reference to the same constructor passed in (but now mutated)\n */\nconst proxyComponent = (Cstr, cmpMeta, flags) => {\n    var _a;\n    const prototype = Cstr.prototype;\n    /**\n     * proxy form associated custom element lifecycle callbacks\n     * @ref https://web.dev/articles/more-capable-form-controls#lifecycle_callbacks\n     */\n    if (BUILD.formAssociated && cmpMeta.$flags$ & 64 /* CMP_FLAGS.formAssociated */ && flags & 1 /* PROXY_FLAGS.isElementConstructor */) {\n        FORM_ASSOCIATED_CUSTOM_ELEMENT_CALLBACKS.forEach((cbName) => Object.defineProperty(prototype, cbName, {\n            value(...args) {\n                const hostRef = getHostRef(this);\n                const elm = BUILD.lazyLoad ? hostRef.$hostElement$ : this;\n                const instance = BUILD.lazyLoad ? hostRef.$lazyInstance$ : elm;\n                if (!instance) {\n                    hostRef.$onReadyPromise$.then((instance) => {\n                        const cb = instance[cbName];\n                        typeof cb === 'function' && cb.call(instance, ...args);\n                    });\n                }\n                else {\n                    const cb = instance[cbName];\n                    typeof cb === 'function' && cb.call(instance, ...args);\n                }\n            },\n        }));\n    }\n    if (BUILD.member && cmpMeta.$members$) {\n        if (BUILD.watchCallback && Cstr.watchers) {\n            cmpMeta.$watchers$ = Cstr.watchers;\n        }\n        // It's better to have a const than two Object.entries()\n        const members = Object.entries(cmpMeta.$members$);\n        members.map(([memberName, [memberFlags]]) => {\n            if ((BUILD.prop || BUILD.state) &&\n                (memberFlags & 31 /* MEMBER_FLAGS.Prop */ ||\n                    ((!BUILD.lazyLoad || flags & 2 /* PROXY_FLAGS.proxyState */) && memberFlags & 32 /* MEMBER_FLAGS.State */))) {\n                // proxyComponent - prop\n                Object.defineProperty(prototype, memberName, {\n                    get() {\n                        // proxyComponent, get value\n                        return getValue(this, memberName);\n                    },\n                    set(newValue) {\n                        // only during dev time\n                        if (BUILD.isDev) {\n                            const ref = getHostRef(this);\n                            if (\n                            // we are proxying the instance (not element)\n                            (flags & 1 /* PROXY_FLAGS.isElementConstructor */) === 0 &&\n                                // the element is not constructing\n                                (ref && ref.$flags$ & 8 /* HOST_FLAGS.isConstructingInstance */) === 0 &&\n                                // the member is a prop\n                                (memberFlags & 31 /* MEMBER_FLAGS.Prop */) !== 0 &&\n                                // the member is not mutable\n                                (memberFlags & 1024 /* MEMBER_FLAGS.Mutable */) === 0) {\n                                consoleDevWarn(`@Prop() \"${memberName}\" on <${cmpMeta.$tagName$}> is immutable but was modified from within the component.\\nMore information: https://stenciljs.com/docs/properties#prop-mutability`);\n                            }\n                        }\n                        // proxyComponent, set value\n                        setValue(this, memberName, newValue, cmpMeta);\n                    },\n                    configurable: true,\n                    enumerable: true,\n                });\n            }\n            else if (BUILD.lazyLoad &&\n                BUILD.method &&\n                flags & 1 /* PROXY_FLAGS.isElementConstructor */ &&\n                memberFlags & 64 /* MEMBER_FLAGS.Method */) {\n                // proxyComponent - method\n                Object.defineProperty(prototype, memberName, {\n                    value(...args) {\n                        var _a;\n                        const ref = getHostRef(this);\n                        return (_a = ref === null || ref === void 0 ? void 0 : ref.$onInstancePromise$) === null || _a === void 0 ? void 0 : _a.then(() => { var _a; return (_a = ref.$lazyInstance$) === null || _a === void 0 ? void 0 : _a[memberName](...args); });\n                    },\n                });\n            }\n        });\n        if (BUILD.observeAttribute && (!BUILD.lazyLoad || flags & 1 /* PROXY_FLAGS.isElementConstructor */)) {\n            const attrNameToPropName = new Map();\n            prototype.attributeChangedCallback = function (attrName, oldValue, newValue) {\n                plt.jmp(() => {\n                    var _a;\n                    const propName = attrNameToPropName.get(attrName);\n                    //  In a web component lifecycle the attributeChangedCallback runs prior to connectedCallback\n                    //  in the case where an attribute was set inline.\n                    //  ```html\n                    //    <my-component some-attribute=\"some-value\"></my-component>\n                    //  ```\n                    //\n                    //  There is an edge case where a developer sets the attribute inline on a custom element and then\n                    //  programmatically changes it before it has been upgraded as shown below:\n                    //\n                    //  ```html\n                    //    <!-- this component has _not_ been upgraded yet -->\n                    //    <my-component id=\"test\" some-attribute=\"some-value\"></my-component>\n                    //    <script>\n                    //      // grab non-upgraded component\n                    //      el = document.querySelector(\"#test\");\n                    //      el.someAttribute = \"another-value\";\n                    //      // upgrade component\n                    //      customElements.define('my-component', MyComponent);\n                    //    </script>\n                    //  ```\n                    //  In this case if we do not un-shadow here and use the value of the shadowing property, attributeChangedCallback\n                    //  will be called with `newValue = \"some-value\"` and will set the shadowed property (this.someAttribute = \"another-value\")\n                    //  to the value that was set inline i.e. \"some-value\" from above example. When\n                    //  the connectedCallback attempts to un-shadow it will use \"some-value\" as the initial value rather than \"another-value\"\n                    //\n                    //  The case where the attribute was NOT set inline but was not set programmatically shall be handled/un-shadowed\n                    //  by connectedCallback as this attributeChangedCallback will not fire.\n                    //\n                    //  https://developers.google.com/web/fundamentals/web-components/best-practices#lazy-properties\n                    //\n                    //  TODO(STENCIL-16) we should think about whether or not we actually want to be reflecting the attributes to\n                    //  properties here given that this goes against best practices outlined here\n                    //  https://developers.google.com/web/fundamentals/web-components/best-practices#avoid-reentrancy\n                    if (this.hasOwnProperty(propName)) {\n                        newValue = this[propName];\n                        delete this[propName];\n                    }\n                    else if (prototype.hasOwnProperty(propName) &&\n                        typeof this[propName] === 'number' &&\n                        this[propName] == newValue) {\n                        // if the propName exists on the prototype of `Cstr`, this update may be a result of Stencil using native\n                        // APIs to reflect props as attributes. Calls to `setAttribute(someElement, propName)` will result in\n                        // `propName` to be converted to a `DOMString`, which may not be what we want for other primitive props.\n                        return;\n                    }\n                    else if (propName == null) {\n                        // At this point we should know this is not a \"member\", so we can treat it like watching an attribute\n                        // on a vanilla web component\n                        const hostRef = getHostRef(this);\n                        const flags = hostRef === null || hostRef === void 0 ? void 0 : hostRef.$flags$;\n                        // We only want to trigger the callback(s) if:\n                        // 1. The instance is ready\n                        // 2. The watchers are ready\n                        // 3. The value has changed\n                        if (flags &&\n                            !(flags & 8 /* HOST_FLAGS.isConstructingInstance */) &&\n                            flags & 128 /* HOST_FLAGS.isWatchReady */ &&\n                            newValue !== oldValue) {\n                            const elm = BUILD.lazyLoad ? hostRef.$hostElement$ : this;\n                            const instance = BUILD.lazyLoad ? hostRef.$lazyInstance$ : elm;\n                            const entry = (_a = cmpMeta.$watchers$) === null || _a === void 0 ? void 0 : _a[attrName];\n                            entry === null || entry === void 0 ? void 0 : entry.forEach((callbackName) => {\n                                if (instance[callbackName] != null) {\n                                    instance[callbackName].call(instance, newValue, oldValue, attrName);\n                                }\n                            });\n                        }\n                        return;\n                    }\n                    this[propName] = newValue === null && typeof this[propName] === 'boolean' ? false : newValue;\n                });\n            };\n            // Create an array of attributes to observe\n            // This list in comprised of all strings used within a `@Watch()` decorator\n            // on a component as well as any Stencil-specific \"members\" (`@Prop()`s and `@State()`s).\n            // As such, there is no way to guarantee type-safety here that a user hasn't entered\n            // an invalid attribute.\n            Cstr.observedAttributes = Array.from(new Set([\n                ...Object.keys((_a = cmpMeta.$watchers$) !== null && _a !== void 0 ? _a : {}),\n                ...members\n                    .filter(([_, m]) => m[0] & 15 /* MEMBER_FLAGS.HasAttribute */)\n                    .map(([propName, m]) => {\n                    var _a;\n                    const attrName = m[1] || propName;\n                    attrNameToPropName.set(attrName, propName);\n                    if (BUILD.reflect && m[0] & 512 /* MEMBER_FLAGS.ReflectAttr */) {\n                        (_a = cmpMeta.$attrsToReflect$) === null || _a === void 0 ? void 0 : _a.push([propName, attrName]);\n                    }\n                    return attrName;\n                }),\n            ]));\n        }\n    }\n    return Cstr;\n};\n/**\n * Initialize a Stencil component given a reference to its host element, its\n * runtime bookkeeping data structure, runtime metadata about the component,\n * and (optionally) an HMR version ID.\n *\n * @param elm a host element\n * @param hostRef the element's runtime bookkeeping object\n * @param cmpMeta runtime metadata for the Stencil component\n * @param hmrVersionId an (optional) HMR version ID\n */\nconst initializeComponent = async (elm, hostRef, cmpMeta, hmrVersionId) => {\n    let Cstr;\n    // initializeComponent\n    if ((hostRef.$flags$ & 32 /* HOST_FLAGS.hasInitializedComponent */) === 0) {\n        // Let the runtime know that the component has been initialized\n        hostRef.$flags$ |= 32 /* HOST_FLAGS.hasInitializedComponent */;\n        if (BUILD.lazyLoad || BUILD.hydrateClientSide) {\n            // lazy loaded components\n            // request the component's implementation to be\n            // wired up with the host element\n            Cstr = loadModule(cmpMeta, hostRef, hmrVersionId);\n            if (Cstr.then) {\n                // Await creates a micro-task avoid if possible\n                const endLoad = uniqueTime(`st:load:${cmpMeta.$tagName$}:${hostRef.$modeName$}`, `[Stencil] Load module for <${cmpMeta.$tagName$}>`);\n                Cstr = await Cstr;\n                endLoad();\n            }\n            if ((BUILD.isDev || BUILD.isDebug) && !Cstr) {\n                throw new Error(`Constructor for \"${cmpMeta.$tagName$}#${hostRef.$modeName$}\" was not found`);\n            }\n            if (BUILD.member && !Cstr.isProxied) {\n                // we've never proxied this Constructor before\n                // let's add the getters/setters to its prototype before\n                // the first time we create an instance of the implementation\n                if (BUILD.watchCallback) {\n                    cmpMeta.$watchers$ = Cstr.watchers;\n                }\n                proxyComponent(Cstr, cmpMeta, 2 /* PROXY_FLAGS.proxyState */);\n                Cstr.isProxied = true;\n            }\n            const endNewInstance = createTime('createInstance', cmpMeta.$tagName$);\n            // ok, time to construct the instance\n            // but let's keep track of when we start and stop\n            // so that the getters/setters don't incorrectly step on data\n            if (BUILD.member) {\n                hostRef.$flags$ |= 8 /* HOST_FLAGS.isConstructingInstance */;\n            }\n            // construct the lazy-loaded component implementation\n            // passing the hostRef is very important during\n            // construction in order to directly wire together the\n            // host element and the lazy-loaded instance\n            try {\n                new Cstr(hostRef);\n            }\n            catch (e) {\n                consoleError(e);\n            }\n            if (BUILD.member) {\n                hostRef.$flags$ &= ~8 /* HOST_FLAGS.isConstructingInstance */;\n            }\n            if (BUILD.watchCallback) {\n                hostRef.$flags$ |= 128 /* HOST_FLAGS.isWatchReady */;\n            }\n            endNewInstance();\n            fireConnectedCallback(hostRef.$lazyInstance$);\n        }\n        else {\n            // sync constructor component\n            Cstr = elm.constructor;\n            // wait for the CustomElementRegistry to mark the component as ready before setting `isWatchReady`. Otherwise,\n            // watchers may fire prematurely if `customElements.get()`/`customElements.whenDefined()` resolves _before_\n            // Stencil has completed instantiating the component.\n            customElements.whenDefined(cmpMeta.$tagName$).then(() => (hostRef.$flags$ |= 128 /* HOST_FLAGS.isWatchReady */));\n        }\n        if (BUILD.style && Cstr.style) {\n            // this component has styles but we haven't registered them yet\n            let style = Cstr.style;\n            if (BUILD.mode && typeof style !== 'string') {\n                style = style[(hostRef.$modeName$ = computeMode(elm))];\n                if (BUILD.hydrateServerSide && hostRef.$modeName$) {\n                    elm.setAttribute('s-mode', hostRef.$modeName$);\n                }\n            }\n            const scopeId = getScopeId(cmpMeta, hostRef.$modeName$);\n            if (!styles.has(scopeId)) {\n                const endRegisterStyles = createTime('registerStyles', cmpMeta.$tagName$);\n                if (!BUILD.hydrateServerSide &&\n                    BUILD.shadowDom &&\n                    // TODO(STENCIL-854): Remove code related to legacy shadowDomShim field\n                    BUILD.shadowDomShim &&\n                    cmpMeta.$flags$ & 8 /* CMP_FLAGS.needsShadowDomShim */) {\n                    style = await import('./shadow-css.js').then((m) => m.scopeCss(style, scopeId, false));\n                }\n                registerStyle(scopeId, style, !!(cmpMeta.$flags$ & 1 /* CMP_FLAGS.shadowDomEncapsulation */));\n                endRegisterStyles();\n            }\n        }\n    }\n    // we've successfully created a lazy instance\n    const ancestorComponent = hostRef.$ancestorComponent$;\n    const schedule = () => scheduleUpdate(hostRef, true);\n    if (BUILD.asyncLoading && ancestorComponent && ancestorComponent['s-rc']) {\n        // this is the initial load and this component it has an ancestor component\n        // but the ancestor component has NOT fired its will update lifecycle yet\n        // so let's just cool our jets and wait for the ancestor to continue first\n        // this will get fired off when the ancestor component\n        // finally gets around to rendering its lazy self\n        // fire off the initial update\n        ancestorComponent['s-rc'].push(schedule);\n    }\n    else {\n        schedule();\n    }\n};\nconst fireConnectedCallback = (instance) => {\n    if (BUILD.lazyLoad && BUILD.connectedCallback) {\n        safeCall(instance, 'connectedCallback');\n    }\n};\nconst connectedCallback = (elm) => {\n    if ((plt.$flags$ & 1 /* PLATFORM_FLAGS.isTmpDisconnected */) === 0) {\n        const hostRef = getHostRef(elm);\n        const cmpMeta = hostRef.$cmpMeta$;\n        const endConnected = createTime('connectedCallback', cmpMeta.$tagName$);\n        if (BUILD.hostListenerTargetParent) {\n            // only run if we have listeners being attached to a parent\n            addHostEventListeners(elm, hostRef, cmpMeta.$listeners$, true);\n        }\n        if (!(hostRef.$flags$ & 1 /* HOST_FLAGS.hasConnected */)) {\n            // first time this component has connected\n            hostRef.$flags$ |= 1 /* HOST_FLAGS.hasConnected */;\n            let hostId;\n            if (BUILD.hydrateClientSide) {\n                hostId = elm.getAttribute(HYDRATE_ID);\n                if (hostId) {\n                    if (BUILD.shadowDom && supportsShadow && cmpMeta.$flags$ & 1 /* CMP_FLAGS.shadowDomEncapsulation */) {\n                        const scopeId = BUILD.mode\n                            ? addStyle(elm.shadowRoot, cmpMeta, elm.getAttribute('s-mode'))\n                            : addStyle(elm.shadowRoot, cmpMeta);\n                        elm.classList.remove(scopeId + '-h', scopeId + '-s');\n                    }\n                    initializeClientHydrate(elm, cmpMeta.$tagName$, hostId, hostRef);\n                }\n            }\n            if (BUILD.slotRelocation && !hostId) {\n                // initUpdate\n                // if the slot polyfill is required we'll need to put some nodes\n                // in here to act as original content anchors as we move nodes around\n                // host element has been connected to the DOM\n                if (BUILD.hydrateServerSide ||\n                    ((BUILD.slot || BUILD.shadowDom) &&\n                        // TODO(STENCIL-854): Remove code related to legacy shadowDomShim field\n                        cmpMeta.$flags$ & (4 /* CMP_FLAGS.hasSlotRelocation */ | 8 /* CMP_FLAGS.needsShadowDomShim */))) {\n                    setContentReference(elm);\n                }\n            }\n            if (BUILD.asyncLoading) {\n                // find the first ancestor component (if there is one) and register\n                // this component as one of the actively loading child components for its ancestor\n                let ancestorComponent = elm;\n                while ((ancestorComponent = ancestorComponent.parentNode || ancestorComponent.host)) {\n                    // climb up the ancestors looking for the first\n                    // component that hasn't finished its lifecycle update yet\n                    if ((BUILD.hydrateClientSide &&\n                        ancestorComponent.nodeType === 1 /* NODE_TYPE.ElementNode */ &&\n                        ancestorComponent.hasAttribute('s-id') &&\n                        ancestorComponent['s-p']) ||\n                        ancestorComponent['s-p']) {\n                        // we found this components first ancestor component\n                        // keep a reference to this component's ancestor component\n                        attachToAncestor(hostRef, (hostRef.$ancestorComponent$ = ancestorComponent));\n                        break;\n                    }\n                }\n            }\n            // Lazy properties\n            // https://developers.google.com/web/fundamentals/web-components/best-practices#lazy-properties\n            if (BUILD.prop && !BUILD.hydrateServerSide && cmpMeta.$members$) {\n                Object.entries(cmpMeta.$members$).map(([memberName, [memberFlags]]) => {\n                    if (memberFlags & 31 /* MEMBER_FLAGS.Prop */ && elm.hasOwnProperty(memberName)) {\n                        const value = elm[memberName];\n                        delete elm[memberName];\n                        elm[memberName] = value;\n                    }\n                });\n            }\n            if (BUILD.initializeNextTick) {\n                // connectedCallback, taskQueue, initialLoad\n                // angular sets attribute AFTER connectCallback\n                // https://github.com/angular/angular/issues/18909\n                // https://github.com/angular/angular/issues/19940\n                nextTick(() => initializeComponent(elm, hostRef, cmpMeta));\n            }\n            else {\n                initializeComponent(elm, hostRef, cmpMeta);\n            }\n        }\n        else {\n            // not the first time this has connected\n            // reattach any event listeners to the host\n            // since they would have been removed when disconnected\n            addHostEventListeners(elm, hostRef, cmpMeta.$listeners$, false);\n            // fire off connectedCallback() on component instance\n            if (hostRef === null || hostRef === void 0 ? void 0 : hostRef.$lazyInstance$) {\n                fireConnectedCallback(hostRef.$lazyInstance$);\n            }\n            else if (hostRef === null || hostRef === void 0 ? void 0 : hostRef.$onReadyPromise$) {\n                hostRef.$onReadyPromise$.then(() => fireConnectedCallback(hostRef.$lazyInstance$));\n            }\n        }\n        endConnected();\n    }\n};\nconst setContentReference = (elm) => {\n    // only required when we're NOT using native shadow dom (slot)\n    // or this browser doesn't support native shadow dom\n    // and this host element was NOT created with SSR\n    // let's pick out the inner content for slot projection\n    // create a node to represent where the original\n    // content was first placed, which is useful later on\n    const contentRefElm = (elm['s-cr'] = doc.createComment(BUILD.isDebug ? `content-ref (host=${elm.localName})` : ''));\n    contentRefElm['s-cn'] = true;\n    elm.insertBefore(contentRefElm, elm.firstChild);\n};\nconst disconnectInstance = (instance) => {\n    if (BUILD.lazyLoad && BUILD.disconnectedCallback) {\n        safeCall(instance, 'disconnectedCallback');\n    }\n    if (BUILD.cmpDidUnload) {\n        safeCall(instance, 'componentDidUnload');\n    }\n};\nconst disconnectedCallback = async (elm) => {\n    if ((plt.$flags$ & 1 /* PLATFORM_FLAGS.isTmpDisconnected */) === 0) {\n        const hostRef = getHostRef(elm);\n        if (BUILD.hostListener) {\n            if (hostRef.$rmListeners$) {\n                hostRef.$rmListeners$.map((rmListener) => rmListener());\n                hostRef.$rmListeners$ = undefined;\n            }\n        }\n        if (!BUILD.lazyLoad) {\n            disconnectInstance(elm);\n        }\n        else if (hostRef === null || hostRef === void 0 ? void 0 : hostRef.$lazyInstance$) {\n            disconnectInstance(hostRef.$lazyInstance$);\n        }\n        else if (hostRef === null || hostRef === void 0 ? void 0 : hostRef.$onReadyPromise$) {\n            hostRef.$onReadyPromise$.then(() => disconnectInstance(hostRef.$lazyInstance$));\n        }\n    }\n};\nconst patchPseudoShadowDom = (hostElementPrototype, descriptorPrototype) => {\n    patchCloneNode(hostElementPrototype);\n    patchSlotAppendChild(hostElementPrototype);\n    patchSlotAppend(hostElementPrototype);\n    patchSlotPrepend(hostElementPrototype);\n    patchSlotInsertAdjacentElement(hostElementPrototype);\n    patchSlotInsertAdjacentHTML(hostElementPrototype);\n    patchSlotInsertAdjacentText(hostElementPrototype);\n    patchTextContent(hostElementPrototype);\n    patchChildSlotNodes(hostElementPrototype, descriptorPrototype);\n    patchSlotRemoveChild(hostElementPrototype);\n};\nconst patchCloneNode = (HostElementPrototype) => {\n    const orgCloneNode = HostElementPrototype.cloneNode;\n    HostElementPrototype.cloneNode = function (deep) {\n        const srcNode = this;\n        const isShadowDom = BUILD.shadowDom ? srcNode.shadowRoot && supportsShadow : false;\n        const clonedNode = orgCloneNode.call(srcNode, isShadowDom ? deep : false);\n        if (BUILD.slot && !isShadowDom && deep) {\n            let i = 0;\n            let slotted, nonStencilNode;\n            const stencilPrivates = [\n                's-id',\n                's-cr',\n                's-lr',\n                's-rc',\n                's-sc',\n                's-p',\n                's-cn',\n                's-sr',\n                's-sn',\n                's-hn',\n                's-ol',\n                's-nr',\n                's-si',\n            ];\n            for (; i < srcNode.childNodes.length; i++) {\n                slotted = srcNode.childNodes[i]['s-nr'];\n                nonStencilNode = stencilPrivates.every((privateField) => !srcNode.childNodes[i][privateField]);\n                if (slotted) {\n                    if (BUILD.appendChildSlotFix && clonedNode.__appendChild) {\n                        clonedNode.__appendChild(slotted.cloneNode(true));\n                    }\n                    else {\n                        clonedNode.appendChild(slotted.cloneNode(true));\n                    }\n                }\n                if (nonStencilNode) {\n                    clonedNode.appendChild(srcNode.childNodes[i].cloneNode(true));\n                }\n            }\n        }\n        return clonedNode;\n    };\n};\n/**\n * Patches the `appendChild` method on a `scoped` Stencil component.\n * The patch will attempt to find a slot with the same name as the node being appended\n * and insert it into the slot reference if found. Otherwise, it falls-back to the original\n * `appendChild` method.\n *\n * @param HostElementPrototype The Stencil component to be patched\n */\nconst patchSlotAppendChild = (HostElementPrototype) => {\n    HostElementPrototype.__appendChild = HostElementPrototype.appendChild;\n    HostElementPrototype.appendChild = function (newChild) {\n        const slotName = (newChild['s-sn'] = getSlotName(newChild));\n        const slotNode = getHostSlotNode(this.childNodes, slotName);\n        if (slotNode) {\n            const slotChildNodes = getHostSlotChildNodes(slotNode, slotName);\n            const appendAfter = slotChildNodes[slotChildNodes.length - 1];\n            const insertedNode = appendAfter.parentNode.insertBefore(newChild, appendAfter.nextSibling);\n            // Check if there is fallback content that should be hidden\n            updateFallbackSlotVisibility(this);\n            // Force a re-render of the host element\n            forceUpdate(this);\n            return insertedNode;\n        }\n        return this.__appendChild(newChild);\n    };\n};\n/**\n * Patches the `removeChild` method on a `scoped` Stencil component.\n * This patch attempts to remove the specified node from a slot reference\n * if the slot exists. Otherwise, it falls-back to the original `removeChild` method.\n *\n * @param ElementPrototype The Stencil component to be patched\n */\nconst patchSlotRemoveChild = (ElementPrototype) => {\n    ElementPrototype.__removeChild = ElementPrototype.removeChild;\n    ElementPrototype.removeChild = function (toRemove) {\n        if (toRemove && typeof toRemove['s-sn'] !== 'undefined') {\n            const slotNode = getHostSlotNode(this.childNodes, toRemove['s-sn']);\n            if (slotNode) {\n                // Get all slot content\n                const slotChildNodes = getHostSlotChildNodes(slotNode, toRemove['s-sn']);\n                // See if any of the slotted content matches the node to remove\n                const existingNode = slotChildNodes.find((n) => n === toRemove);\n                if (existingNode) {\n                    existingNode.remove();\n                    // Check if there is fallback content that should be displayed if that\n                    // was the last node in the slot\n                    updateFallbackSlotVisibility(this);\n                    return;\n                }\n            }\n        }\n        return this.__removeChild(toRemove);\n    };\n};\n/**\n * Patches the `prepend` method for a slotted node inside a scoped component.\n *\n * @param HostElementPrototype the `Element` to be patched\n */\nconst patchSlotPrepend = (HostElementPrototype) => {\n    const originalPrepend = HostElementPrototype.prepend;\n    HostElementPrototype.prepend = function (...newChildren) {\n        newChildren.forEach((newChild) => {\n            if (typeof newChild === 'string') {\n                newChild = this.ownerDocument.createTextNode(newChild);\n            }\n            const slotName = (newChild['s-sn'] = getSlotName(newChild));\n            const slotNode = getHostSlotNode(this.childNodes, slotName);\n            if (slotNode) {\n                const slotPlaceholder = document.createTextNode('');\n                slotPlaceholder['s-nr'] = newChild;\n                slotNode['s-cr'].parentNode.__appendChild(slotPlaceholder);\n                newChild['s-ol'] = slotPlaceholder;\n                const slotChildNodes = getHostSlotChildNodes(slotNode, slotName);\n                const appendAfter = slotChildNodes[0];\n                return appendAfter.parentNode.insertBefore(newChild, appendAfter.nextSibling);\n            }\n            if (newChild.nodeType === 1 && !!newChild.getAttribute('slot')) {\n                newChild.hidden = true;\n            }\n            return originalPrepend.call(this, newChild);\n        });\n    };\n};\n/**\n * Patches the `append` method for a slotted node inside a scoped component. The patched method uses\n * `appendChild` under-the-hood while creating text nodes for any new children that passed as bare strings.\n *\n * @param HostElementPrototype the `Element` to be patched\n */\nconst patchSlotAppend = (HostElementPrototype) => {\n    HostElementPrototype.append = function (...newChildren) {\n        newChildren.forEach((newChild) => {\n            if (typeof newChild === 'string') {\n                newChild = this.ownerDocument.createTextNode(newChild);\n            }\n            this.appendChild(newChild);\n        });\n    };\n};\n/**\n * Patches the `insertAdjacentHTML` method for a slotted node inside a scoped component. Specifically,\n * we only need to patch the behavior for the specific `beforeend` and `afterbegin` positions so the element\n * gets inserted into the DOM in the correct location.\n *\n * @param HostElementPrototype the `Element` to be patched\n */\nconst patchSlotInsertAdjacentHTML = (HostElementPrototype) => {\n    const originalInsertAdjacentHtml = HostElementPrototype.insertAdjacentHTML;\n    HostElementPrototype.insertAdjacentHTML = function (position, text) {\n        if (position !== 'afterbegin' && position !== 'beforeend') {\n            return originalInsertAdjacentHtml.call(this, position, text);\n        }\n        const container = this.ownerDocument.createElement('_');\n        let node;\n        container.innerHTML = text;\n        if (position === 'afterbegin') {\n            while ((node = container.firstChild)) {\n                this.prepend(node);\n            }\n        }\n        else if (position === 'beforeend') {\n            while ((node = container.firstChild)) {\n                this.append(node);\n            }\n        }\n    };\n};\n/**\n * Patches the `insertAdjacentText` method for a slotted node inside a scoped component. Specifically,\n * we only need to patch the behavior for the specific `beforeend` and `afterbegin` positions so the text node\n * gets inserted into the DOM in the correct location.\n *\n * @param HostElementPrototype the `Element` to be patched\n */\nconst patchSlotInsertAdjacentText = (HostElementPrototype) => {\n    HostElementPrototype.insertAdjacentText = function (position, text) {\n        this.insertAdjacentHTML(position, text);\n    };\n};\n/**\n * Patches the `insertAdjacentElement` method for a slotted node inside a scoped component. Specifically,\n * we only need to patch the behavior for the specific `beforeend` and `afterbegin` positions so the element\n * gets inserted into the DOM in the correct location.\n *\n * @param HostElementPrototype the `Element` to be patched\n */\nconst patchSlotInsertAdjacentElement = (HostElementPrototype) => {\n    const originalInsertAdjacentElement = HostElementPrototype.insertAdjacentElement;\n    HostElementPrototype.insertAdjacentElement = function (position, element) {\n        if (position !== 'afterbegin' && position !== 'beforeend') {\n            return originalInsertAdjacentElement.call(this, position, element);\n        }\n        if (position === 'afterbegin') {\n            this.prepend(element);\n            return element;\n        }\n        else if (position === 'beforeend') {\n            this.append(element);\n            return element;\n        }\n        return element;\n    };\n};\n/**\n * Patches the text content of an unnamed slotted node inside a scoped component\n * @param hostElementPrototype the `Element` to be patched\n */\nconst patchTextContent = (hostElementPrototype) => {\n    const descriptor = Object.getOwnPropertyDescriptor(Node.prototype, 'textContent');\n    Object.defineProperty(hostElementPrototype, '__textContent', descriptor);\n    if (BUILD.experimentalScopedSlotChanges) {\n        // Patch `textContent` to mimic shadow root behavior\n        Object.defineProperty(hostElementPrototype, 'textContent', {\n            // To mimic shadow root behavior, we need to return the text content of all\n            // nodes in a slot reference node\n            get() {\n                const slotRefNodes = getAllChildSlotNodes(this.childNodes);\n                const textContent = slotRefNodes\n                    .map((node) => {\n                    var _a, _b;\n                    const text = [];\n                    // Need to get the text content of all nodes in the slot reference node\n                    let slotContent = node.nextSibling;\n                    while (slotContent && slotContent['s-sn'] === node['s-sn']) {\n                        if (slotContent.nodeType === 3 /* NODE_TYPES.TEXT_NODE */ || slotContent.nodeType === 1 /* NODE_TYPES.ELEMENT_NODE */) {\n                            text.push((_b = (_a = slotContent.textContent) === null || _a === void 0 ? void 0 : _a.trim()) !== null && _b !== void 0 ? _b : '');\n                        }\n                        slotContent = slotContent.nextSibling;\n                    }\n                    return text.filter((ref) => ref !== '').join(' ');\n                })\n                    .filter((text) => text !== '')\n                    .join(' ');\n                // Pad the string to return\n                return ' ' + textContent + ' ';\n            },\n            // To mimic shadow root behavior, we need to overwrite all nodes in a slot\n            // reference node. If a default slot reference node exists, the text content will be\n            // placed there. Otherwise, the new text node will be hidden\n            set(value) {\n                const slotRefNodes = getAllChildSlotNodes(this.childNodes);\n                slotRefNodes.forEach((node) => {\n                    // Remove the existing content of the slot\n                    let slotContent = node.nextSibling;\n                    while (slotContent && slotContent['s-sn'] === node['s-sn']) {\n                        const tmp = slotContent;\n                        slotContent = slotContent.nextSibling;\n                        tmp.remove();\n                    }\n                    // If this is a default slot, add the text node in the slot location.\n                    // Otherwise, destroy the slot reference node\n                    if (node['s-sn'] === '') {\n                        const textNode = this.ownerDocument.createTextNode(value);\n                        textNode['s-sn'] = '';\n                        node.parentElement.insertBefore(textNode, node.nextSibling);\n                    }\n                    else {\n                        node.remove();\n                    }\n                });\n            },\n        });\n    }\n    else {\n        Object.defineProperty(hostElementPrototype, 'textContent', {\n            get() {\n                var _a;\n                // get the 'default slot', which would be the first slot in a shadow tree (if we were using one), whose name is\n                // the empty string\n                const slotNode = getHostSlotNode(this.childNodes, '');\n                // when a slot node is found, the textContent _may_ be found in the next sibling (text) node, depending on how\n                // nodes were reordered during the vdom render. first try to get the text content from the sibling.\n                if (((_a = slotNode === null || slotNode === void 0 ? void 0 : slotNode.nextSibling) === null || _a === void 0 ? void 0 : _a.nodeType) === 3 /* NODE_TYPES.TEXT_NODE */) {\n                    return slotNode.nextSibling.textContent;\n                }\n                else if (slotNode) {\n                    return slotNode.textContent;\n                }\n                else {\n                    // fallback to the original implementation\n                    return this.__textContent;\n                }\n            },\n            set(value) {\n                var _a;\n                // get the 'default slot', which would be the first slot in a shadow tree (if we were using one), whose name is\n                // the empty string\n                const slotNode = getHostSlotNode(this.childNodes, '');\n                // when a slot node is found, the textContent _may_ need to be placed in the next sibling (text) node,\n                // depending on how nodes were reordered during the vdom render. first try to set the text content on the\n                // sibling.\n                if (((_a = slotNode === null || slotNode === void 0 ? void 0 : slotNode.nextSibling) === null || _a === void 0 ? void 0 : _a.nodeType) === 3 /* NODE_TYPES.TEXT_NODE */) {\n                    slotNode.nextSibling.textContent = value;\n                }\n                else if (slotNode) {\n                    slotNode.textContent = value;\n                }\n                else {\n                    // we couldn't find a slot, but that doesn't mean that there isn't one. if this check ran before the DOM\n                    // loaded, we could have missed it. check for a content reference element on the scoped component and insert\n                    // it there\n                    this.__textContent = value;\n                    const contentRefElm = this['s-cr'];\n                    if (contentRefElm) {\n                        this.insertBefore(contentRefElm, this.firstChild);\n                    }\n                }\n            },\n        });\n    }\n};\nconst patchChildSlotNodes = (elm, cmpMeta) => {\n    class FakeNodeList extends Array {\n        item(n) {\n            return this[n];\n        }\n    }\n    // TODO(STENCIL-854): Remove code related to legacy shadowDomShim field\n    if (cmpMeta.$flags$ & 8 /* CMP_FLAGS.needsShadowDomShim */) {\n        const childNodesFn = elm.__lookupGetter__('childNodes');\n        Object.defineProperty(elm, 'children', {\n            get() {\n                return this.childNodes.map((n) => n.nodeType === 1);\n            },\n        });\n        Object.defineProperty(elm, 'childElementCount', {\n            get() {\n                return elm.children.length;\n            },\n        });\n        Object.defineProperty(elm, 'childNodes', {\n            get() {\n                const childNodes = childNodesFn.call(this);\n                if ((plt.$flags$ & 1 /* PLATFORM_FLAGS.isTmpDisconnected */) === 0 &&\n                    getHostRef(this).$flags$ & 2 /* HOST_FLAGS.hasRendered */) {\n                    const result = new FakeNodeList();\n                    for (let i = 0; i < childNodes.length; i++) {\n                        const slot = childNodes[i]['s-nr'];\n                        if (slot) {\n                            result.push(slot);\n                        }\n                    }\n                    return result;\n                }\n                return FakeNodeList.from(childNodes);\n            },\n        });\n    }\n};\n/**\n * Recursively finds all slot reference nodes ('s-sr') in a series of child nodes.\n *\n * @param childNodes The set of child nodes to search for slot reference nodes.\n * @returns An array of slot reference nodes.\n */\nconst getAllChildSlotNodes = (childNodes) => {\n    const slotRefNodes = [];\n    for (const childNode of Array.from(childNodes)) {\n        if (childNode['s-sr']) {\n            slotRefNodes.push(childNode);\n        }\n        slotRefNodes.push(...getAllChildSlotNodes(childNode.childNodes));\n    }\n    return slotRefNodes;\n};\nconst getSlotName = (node) => node['s-sn'] || (node.nodeType === 1 && node.getAttribute('slot')) || '';\n/**\n * Recursively searches a series of child nodes for a slot with the provided name.\n * @param childNodes the nodes to search for a slot with a specific name.\n * @param slotName the name of the slot to match on.\n * @returns a reference to the slot node that matches the provided name, `null` otherwise\n */\nconst getHostSlotNode = (childNodes, slotName) => {\n    let i = 0;\n    let childNode;\n    for (; i < childNodes.length; i++) {\n        childNode = childNodes[i];\n        if (childNode['s-sr'] && childNode['s-sn'] === slotName) {\n            return childNode;\n        }\n        childNode = getHostSlotNode(childNode.childNodes, slotName);\n        if (childNode) {\n            return childNode;\n        }\n    }\n    return null;\n};\nconst getHostSlotChildNodes = (n, slotName) => {\n    const childNodes = [n];\n    while ((n = n.nextSibling) && n['s-sn'] === slotName) {\n        childNodes.push(n);\n    }\n    return childNodes;\n};\nconst defineCustomElement = (Cstr, compactMeta) => {\n    customElements.define(compactMeta[1], proxyCustomElement(Cstr, compactMeta));\n};\nconst proxyCustomElement = (Cstr, compactMeta) => {\n    const cmpMeta = {\n        $flags$: compactMeta[0],\n        $tagName$: compactMeta[1],\n    };\n    if (BUILD.member) {\n        cmpMeta.$members$ = compactMeta[2];\n    }\n    if (BUILD.hostListener) {\n        cmpMeta.$listeners$ = compactMeta[3];\n    }\n    if (BUILD.watchCallback) {\n        cmpMeta.$watchers$ = Cstr.$watchers$;\n    }\n    if (BUILD.reflect) {\n        cmpMeta.$attrsToReflect$ = [];\n    }\n    if (BUILD.shadowDom && !supportsShadow && cmpMeta.$flags$ & 1 /* CMP_FLAGS.shadowDomEncapsulation */) {\n        // TODO(STENCIL-854): Remove code related to legacy shadowDomShim field\n        cmpMeta.$flags$ |= 8 /* CMP_FLAGS.needsShadowDomShim */;\n    }\n    // TODO(STENCIL-914): this check and `else` block can go away and be replaced by just the `scoped` check\n    if (BUILD.experimentalSlotFixes) {\n        if (BUILD.scoped && cmpMeta.$flags$ & 2 /* CMP_FLAGS.scopedCssEncapsulation */) {\n            // This check is intentionally not combined with the surrounding `experimentalSlotFixes` check\n            // since, moving forward, we only want to patch the pseudo shadow DOM when the component is scoped\n            patchPseudoShadowDom(Cstr.prototype, cmpMeta);\n        }\n    }\n    else {\n        if (BUILD.slotChildNodesFix) {\n            patchChildSlotNodes(Cstr.prototype, cmpMeta);\n        }\n        if (BUILD.cloneNodeFix) {\n            patchCloneNode(Cstr.prototype);\n        }\n        if (BUILD.appendChildSlotFix) {\n            patchSlotAppendChild(Cstr.prototype);\n        }\n        if (BUILD.scopedSlotTextContentFix && cmpMeta.$flags$ & 2 /* CMP_FLAGS.scopedCssEncapsulation */) {\n            patchTextContent(Cstr.prototype);\n        }\n    }\n    const originalConnectedCallback = Cstr.prototype.connectedCallback;\n    const originalDisconnectedCallback = Cstr.prototype.disconnectedCallback;\n    Object.assign(Cstr.prototype, {\n        __registerHost() {\n            registerHost(this, cmpMeta);\n        },\n        connectedCallback() {\n            connectedCallback(this);\n            if (BUILD.connectedCallback && originalConnectedCallback) {\n                originalConnectedCallback.call(this);\n            }\n        },\n        disconnectedCallback() {\n            disconnectedCallback(this);\n            if (BUILD.disconnectedCallback && originalDisconnectedCallback) {\n                originalDisconnectedCallback.call(this);\n            }\n        },\n        __attachShadow() {\n            if (supportsShadow) {\n                if (BUILD.shadowDelegatesFocus) {\n                    this.attachShadow({\n                        mode: 'open',\n                        delegatesFocus: !!(cmpMeta.$flags$ & 16 /* CMP_FLAGS.shadowDelegatesFocus */),\n                    });\n                }\n                else {\n                    this.attachShadow({ mode: 'open' });\n                }\n            }\n            else {\n                this.shadowRoot = this;\n            }\n        },\n    });\n    Cstr.is = cmpMeta.$tagName$;\n    return proxyComponent(Cstr, cmpMeta, 1 /* PROXY_FLAGS.isElementConstructor */ | 2 /* PROXY_FLAGS.proxyState */);\n};\nconst forceModeUpdate = (elm) => {\n    if (BUILD.style && BUILD.mode && !BUILD.lazyLoad) {\n        const mode = computeMode(elm);\n        const hostRef = getHostRef(elm);\n        if (hostRef.$modeName$ !== mode) {\n            const cmpMeta = hostRef.$cmpMeta$;\n            const oldScopeId = elm['s-sc'];\n            const scopeId = getScopeId(cmpMeta, mode);\n            const style = elm.constructor.style[mode];\n            const flags = cmpMeta.$flags$;\n            if (style) {\n                if (!styles.has(scopeId)) {\n                    registerStyle(scopeId, style, !!(flags & 1 /* CMP_FLAGS.shadowDomEncapsulation */));\n                }\n                hostRef.$modeName$ = mode;\n                elm.classList.remove(oldScopeId + '-h', oldScopeId + '-s');\n                attachStyles(hostRef);\n                forceUpdate(elm);\n            }\n        }\n    }\n};\n/**\n * Kick off hot-module-replacement for a component. In order to replace the\n * component in-place we:\n *\n * 1. get a reference to the {@link d.HostRef} for the element\n * 2. reset the element's runtime flags\n * 3. re-run the initialization logic for the element (via\n *    {@link initializeComponent})\n *\n * @param hostElement the host element for the component which we want to start\n * doing HMR\n * @param cmpMeta runtime metadata for the component\n * @param hmrVersionId the current HMR version ID\n */\nconst hmrStart = (hostElement, cmpMeta, hmrVersionId) => {\n    // ¯\\_(ツ)_/¯\n    const hostRef = getHostRef(hostElement);\n    // reset state flags to only have been connected\n    hostRef.$flags$ = 1 /* HOST_FLAGS.hasConnected */;\n    // TODO\n    // detach any event listeners that may have been added\n    // because we're not passing an exact event name it'll\n    // remove all of this element's event, which is good\n    // re-initialize the component\n    initializeComponent(hostElement, hostRef, cmpMeta, hmrVersionId);\n};\nconst bootstrapLazy = (lazyBundles, options = {}) => {\n    var _a;\n    if (BUILD.profile && performance.mark) {\n        performance.mark('st:app:start');\n    }\n    installDevTools();\n    const endBootstrap = createTime('bootstrapLazy');\n    const cmpTags = [];\n    const exclude = options.exclude || [];\n    const customElements = win.customElements;\n    const head = doc.head;\n    const metaCharset = /*@__PURE__*/ head.querySelector('meta[charset]');\n    const dataStyles = /*@__PURE__*/ doc.createElement('style');\n    const deferredConnectedCallbacks = [];\n    const styles = /*@__PURE__*/ doc.querySelectorAll(`[${HYDRATED_STYLE_ID}]`);\n    let appLoadFallback;\n    let isBootstrapping = true;\n    let i = 0;\n    Object.assign(plt, options);\n    plt.$resourcesUrl$ = new URL(options.resourcesUrl || './', doc.baseURI).href;\n    if (BUILD.asyncQueue) {\n        if (options.syncQueue) {\n            plt.$flags$ |= 4 /* PLATFORM_FLAGS.queueSync */;\n        }\n    }\n    if (BUILD.hydrateClientSide) {\n        // If the app is already hydrated there is not point to disable the\n        // async queue. This will improve the first input delay\n        plt.$flags$ |= 2 /* PLATFORM_FLAGS.appLoaded */;\n    }\n    if (BUILD.hydrateClientSide && BUILD.shadowDom) {\n        for (; i < styles.length; i++) {\n            registerStyle(styles[i].getAttribute(HYDRATED_STYLE_ID), convertScopedToShadow(styles[i].innerHTML), true);\n        }\n    }\n    let hasSlotRelocation = false;\n    lazyBundles.map((lazyBundle) => {\n        lazyBundle[1].map((compactMeta) => {\n            var _a;\n            const cmpMeta = {\n                $flags$: compactMeta[0],\n                $tagName$: compactMeta[1],\n                $members$: compactMeta[2],\n                $listeners$: compactMeta[3],\n            };\n            // Check if we are using slots outside the shadow DOM in this component.\n            // We'll use this information later to add styles for `slot-fb` elements\n            if (cmpMeta.$flags$ & 4 /* CMP_FLAGS.hasSlotRelocation */) {\n                hasSlotRelocation = true;\n            }\n            if (BUILD.member) {\n                cmpMeta.$members$ = compactMeta[2];\n            }\n            if (BUILD.hostListener) {\n                cmpMeta.$listeners$ = compactMeta[3];\n            }\n            if (BUILD.reflect) {\n                cmpMeta.$attrsToReflect$ = [];\n            }\n            if (BUILD.watchCallback) {\n                cmpMeta.$watchers$ = (_a = compactMeta[4]) !== null && _a !== void 0 ? _a : {};\n            }\n            if (BUILD.shadowDom && !supportsShadow && cmpMeta.$flags$ & 1 /* CMP_FLAGS.shadowDomEncapsulation */) {\n                // TODO(STENCIL-854): Remove code related to legacy shadowDomShim field\n                cmpMeta.$flags$ |= 8 /* CMP_FLAGS.needsShadowDomShim */;\n            }\n            const tagName = BUILD.transformTagName && options.transformTagName\n                ? options.transformTagName(cmpMeta.$tagName$)\n                : cmpMeta.$tagName$;\n            const HostElement = class extends HTMLElement {\n                // StencilLazyHost\n                constructor(self) {\n                    // @ts-ignore\n                    super(self);\n                    self = this;\n                    registerHost(self, cmpMeta);\n                    if (BUILD.shadowDom && cmpMeta.$flags$ & 1 /* CMP_FLAGS.shadowDomEncapsulation */) {\n                        // this component is using shadow dom\n                        // and this browser supports shadow dom\n                        // add the read-only property \"shadowRoot\" to the host element\n                        // adding the shadow root build conditionals to minimize runtime\n                        if (supportsShadow) {\n                            if (BUILD.shadowDelegatesFocus) {\n                                self.attachShadow({\n                                    mode: 'open',\n                                    delegatesFocus: !!(cmpMeta.$flags$ & 16 /* CMP_FLAGS.shadowDelegatesFocus */),\n                                });\n                            }\n                            else {\n                                self.attachShadow({ mode: 'open' });\n                            }\n                        }\n                        else if (!BUILD.hydrateServerSide && !('shadowRoot' in self)) {\n                            self.shadowRoot = self;\n                        }\n                    }\n                }\n                connectedCallback() {\n                    if (appLoadFallback) {\n                        clearTimeout(appLoadFallback);\n                        appLoadFallback = null;\n                    }\n                    if (isBootstrapping) {\n                        // connectedCallback will be processed once all components have been registered\n                        deferredConnectedCallbacks.push(this);\n                    }\n                    else {\n                        plt.jmp(() => connectedCallback(this));\n                    }\n                }\n                disconnectedCallback() {\n                    plt.jmp(() => disconnectedCallback(this));\n                }\n                componentOnReady() {\n                    return getHostRef(this).$onReadyPromise$;\n                }\n            };\n            // TODO(STENCIL-914): this check and `else` block can go away and be replaced by just the `scoped` check\n            if (BUILD.experimentalSlotFixes) {\n                // This check is intentionally not combined with the surrounding `experimentalSlotFixes` check\n                // since, moving forward, we only want to patch the pseudo shadow DOM when the component is scoped\n                if (BUILD.scoped && cmpMeta.$flags$ & 2 /* CMP_FLAGS.scopedCssEncapsulation */) {\n                    patchPseudoShadowDom(HostElement.prototype, cmpMeta);\n                }\n            }\n            else {\n                if (BUILD.slotChildNodesFix) {\n                    patchChildSlotNodes(HostElement.prototype, cmpMeta);\n                }\n                if (BUILD.cloneNodeFix) {\n                    patchCloneNode(HostElement.prototype);\n                }\n                if (BUILD.appendChildSlotFix) {\n                    patchSlotAppendChild(HostElement.prototype);\n                }\n                if (BUILD.scopedSlotTextContentFix && cmpMeta.$flags$ & 2 /* CMP_FLAGS.scopedCssEncapsulation */) {\n                    patchTextContent(HostElement.prototype);\n                }\n            }\n            // if the component is formAssociated we need to set that on the host\n            // element so that it will be ready for `attachInternals` to be called on\n            // it later on\n            if (BUILD.formAssociated && cmpMeta.$flags$ & 64 /* CMP_FLAGS.formAssociated */) {\n                HostElement.formAssociated = true;\n            }\n            if (BUILD.hotModuleReplacement) {\n                // if we're in an HMR dev build then we need to set up the callback\n                // which will carry out the work of actually replacing the module for\n                // this particular component\n                HostElement.prototype['s-hmr'] = function (hmrVersionId) {\n                    hmrStart(this, cmpMeta, hmrVersionId);\n                };\n            }\n            cmpMeta.$lazyBundleId$ = lazyBundle[0];\n            if (!exclude.includes(tagName) && !customElements.get(tagName)) {\n                cmpTags.push(tagName);\n                customElements.define(tagName, proxyComponent(HostElement, cmpMeta, 1 /* PROXY_FLAGS.isElementConstructor */));\n            }\n        });\n    });\n    // Add styles for `slot-fb` elements if any of our components are using slots outside the Shadow DOM\n    if (hasSlotRelocation) {\n        dataStyles.innerHTML += SLOT_FB_CSS;\n    }\n    // Add hydration styles\n    if (BUILD.invisiblePrehydration && (BUILD.hydratedClass || BUILD.hydratedAttribute)) {\n        dataStyles.innerHTML += cmpTags + HYDRATED_CSS;\n    }\n    // If we have styles, add them to the DOM\n    if (dataStyles.innerHTML.length) {\n        dataStyles.setAttribute('data-styles', '');\n        // Apply CSP nonce to the style tag if it exists\n        const nonce = (_a = plt.$nonce$) !== null && _a !== void 0 ? _a : queryNonceMetaTagContent(doc);\n        if (nonce != null) {\n            dataStyles.setAttribute('nonce', nonce);\n        }\n        // Insert the styles into the document head\n        // NOTE: this _needs_ to happen last so we can ensure the nonce (and other attributes) are applied\n        head.insertBefore(dataStyles, metaCharset ? metaCharset.nextSibling : head.firstChild);\n    }\n    // Process deferred connectedCallbacks now all components have been registered\n    isBootstrapping = false;\n    if (deferredConnectedCallbacks.length) {\n        deferredConnectedCallbacks.map((host) => host.connectedCallback());\n    }\n    else {\n        if (BUILD.profile) {\n            plt.jmp(() => (appLoadFallback = setTimeout(appDidLoad, 30, 'timeout')));\n        }\n        else {\n            plt.jmp(() => (appLoadFallback = setTimeout(appDidLoad, 30)));\n        }\n    }\n    // Fallback appLoad event\n    endBootstrap();\n};\nconst Fragment = (_, children) => children;\nconst addHostEventListeners = (elm, hostRef, listeners, attachParentListeners) => {\n    if (BUILD.hostListener && listeners) {\n        // this is called immediately within the element's constructor\n        // initialize our event listeners on the host element\n        // we do this now so that we can listen to events that may\n        // have fired even before the instance is ready\n        if (BUILD.hostListenerTargetParent) {\n            // this component may have event listeners that should be attached to the parent\n            if (attachParentListeners) {\n                // this is being ran from within the connectedCallback\n                // which is important so that we know the host element actually has a parent element\n                // filter out the listeners to only have the ones that ARE being attached to the parent\n                listeners = listeners.filter(([flags]) => flags & 32 /* LISTENER_FLAGS.TargetParent */);\n            }\n            else {\n                // this is being ran from within the component constructor\n                // everything BUT the parent element listeners should be attached at this time\n                // filter out the listeners that are NOT being attached to the parent\n                listeners = listeners.filter(([flags]) => !(flags & 32 /* LISTENER_FLAGS.TargetParent */));\n            }\n        }\n        listeners.map(([flags, name, method]) => {\n            const target = BUILD.hostListenerTarget ? getHostListenerTarget(elm, flags) : elm;\n            const handler = hostListenerProxy(hostRef, method);\n            const opts = hostListenerOpts(flags);\n            plt.ael(target, name, handler, opts);\n            (hostRef.$rmListeners$ = hostRef.$rmListeners$ || []).push(() => plt.rel(target, name, handler, opts));\n        });\n    }\n};\nconst hostListenerProxy = (hostRef, methodName) => (ev) => {\n    try {\n        if (BUILD.lazyLoad) {\n            if (hostRef.$flags$ & 256 /* HOST_FLAGS.isListenReady */) {\n                // instance is ready, let's call it's member method for this event\n                hostRef.$lazyInstance$[methodName](ev);\n            }\n            else {\n                (hostRef.$queuedListeners$ = hostRef.$queuedListeners$ || []).push([methodName, ev]);\n            }\n        }\n        else {\n            hostRef.$hostElement$[methodName](ev);\n        }\n    }\n    catch (e) {\n        consoleError(e);\n    }\n};\nconst getHostListenerTarget = (elm, flags) => {\n    if (BUILD.hostListenerTargetDocument && flags & 4 /* LISTENER_FLAGS.TargetDocument */)\n        return doc;\n    if (BUILD.hostListenerTargetWindow && flags & 8 /* LISTENER_FLAGS.TargetWindow */)\n        return win;\n    if (BUILD.hostListenerTargetBody && flags & 16 /* LISTENER_FLAGS.TargetBody */)\n        return doc.body;\n    if (BUILD.hostListenerTargetParent && flags & 32 /* LISTENER_FLAGS.TargetParent */)\n        return elm.parentElement;\n    return elm;\n};\n// prettier-ignore\nconst hostListenerOpts = (flags) => supportsListenerOptions\n    ? ({\n        passive: (flags & 1 /* LISTENER_FLAGS.Passive */) !== 0,\n        capture: (flags & 2 /* LISTENER_FLAGS.Capture */) !== 0,\n    })\n    : (flags & 2 /* LISTENER_FLAGS.Capture */) !== 0;\n/**\n * Assigns the given value to the nonce property on the runtime platform object.\n * During runtime, this value is used to set the nonce attribute on all dynamically created script and style tags.\n * @param nonce The value to be assigned to the platform nonce property.\n * @returns void\n */\nconst setNonce = (nonce) => (plt.$nonce$ = nonce);\nconst setPlatformOptions = (opts) => Object.assign(plt, opts);\n/**\n * Updates the DOM generated on the server with annotations such as node attributes and\n * comment nodes to facilitate future client-side hydration. These annotations are used for things\n * like moving elements back to their original hosts if using Shadow DOM on the client, and for quickly\n * reconstructing the vNode representations of the DOM.\n *\n * @param doc The DOM generated by the server.\n * @param staticComponents Any components that should be considered static and do not need client-side hydration.\n */\nconst insertVdomAnnotations = (doc, staticComponents) => {\n    if (doc != null) {\n        const docData = {\n            hostIds: 0,\n            rootLevelIds: 0,\n            staticComponents: new Set(staticComponents),\n        };\n        const orgLocationNodes = [];\n        parseVNodeAnnotations(doc, doc.body, docData, orgLocationNodes);\n        orgLocationNodes.forEach((orgLocationNode) => {\n            var _a, _b;\n            if (orgLocationNode != null && orgLocationNode['s-nr']) {\n                const nodeRef = orgLocationNode['s-nr'];\n                let hostId = nodeRef['s-host-id'];\n                let nodeId = nodeRef['s-node-id'];\n                let childId = `${hostId}.${nodeId}`;\n                if (hostId == null) {\n                    hostId = 0;\n                    docData.rootLevelIds++;\n                    nodeId = docData.rootLevelIds;\n                    childId = `${hostId}.${nodeId}`;\n                    if (nodeRef.nodeType === 1 /* NODE_TYPE.ElementNode */) {\n                        nodeRef.setAttribute(HYDRATE_CHILD_ID, childId);\n                    }\n                    else if (nodeRef.nodeType === 3 /* NODE_TYPE.TextNode */) {\n                        if (hostId === 0) {\n                            const textContent = (_a = nodeRef.nodeValue) === null || _a === void 0 ? void 0 : _a.trim();\n                            if (textContent === '') {\n                                // useless whitespace node at the document root\n                                orgLocationNode.remove();\n                                return;\n                            }\n                        }\n                        const commentBeforeTextNode = doc.createComment(childId);\n                        commentBeforeTextNode.nodeValue = `${TEXT_NODE_ID}.${childId}`;\n                        (_b = nodeRef.parentNode) === null || _b === void 0 ? void 0 : _b.insertBefore(commentBeforeTextNode, nodeRef);\n                    }\n                }\n                let orgLocationNodeId = `${ORG_LOCATION_ID}.${childId}`;\n                const orgLocationParentNode = orgLocationNode.parentElement;\n                if (orgLocationParentNode) {\n                    if (orgLocationParentNode['s-en'] === '') {\n                        // ending with a \".\" means that the parent element\n                        // of this node's original location is a SHADOW dom element\n                        // and this node is apart of the root level light dom\n                        orgLocationNodeId += `.`;\n                    }\n                    else if (orgLocationParentNode['s-en'] === 'c') {\n                        // ending with a \".c\" means that the parent element\n                        // of this node's original location is a SCOPED element\n                        // and this node is apart of the root level light dom\n                        orgLocationNodeId += `.c`;\n                    }\n                }\n                orgLocationNode.nodeValue = orgLocationNodeId;\n            }\n        });\n    }\n};\n/**\n * Recursively parses a node generated by the server and its children to set host and child id\n * attributes read during client-side hydration. This function also tracks whether each node is\n * an original location reference node meaning that a node has been moved via slot relocation.\n *\n * @param doc The DOM generated by the server.\n * @param node The node to parse.\n * @param docData An object containing metadata about the document.\n * @param orgLocationNodes An array of nodes that have been moved via slot relocation.\n */\nconst parseVNodeAnnotations = (doc, node, docData, orgLocationNodes) => {\n    if (node == null) {\n        return;\n    }\n    if (node['s-nr'] != null) {\n        orgLocationNodes.push(node);\n    }\n    if (node.nodeType === 1 /* NODE_TYPE.ElementNode */) {\n        node.childNodes.forEach((childNode) => {\n            const hostRef = getHostRef(childNode);\n            if (hostRef != null && !docData.staticComponents.has(childNode.nodeName.toLowerCase())) {\n                const cmpData = {\n                    nodeIds: 0,\n                };\n                insertVNodeAnnotations(doc, childNode, hostRef.$vnode$, docData, cmpData);\n            }\n            parseVNodeAnnotations(doc, childNode, docData, orgLocationNodes);\n        });\n    }\n};\n/**\n * Insert attribute annotations on an element for its host ID and, potentially, its child ID.\n * Also makes calls to insert annotations on the element's children, keeping track of the depth of\n * the component tree.\n *\n * @param doc The DOM generated by the server.\n * @param hostElm The element to insert annotations for.\n * @param vnode The vNode representation of the element.\n * @param docData An object containing metadata about the document.\n * @param cmpData An object containing metadata about the component.\n */\nconst insertVNodeAnnotations = (doc, hostElm, vnode, docData, cmpData) => {\n    if (vnode != null) {\n        const hostId = ++docData.hostIds;\n        hostElm.setAttribute(HYDRATE_ID, hostId);\n        if (hostElm['s-cr'] != null) {\n            hostElm['s-cr'].nodeValue = `${CONTENT_REF_ID}.${hostId}`;\n        }\n        if (vnode.$children$ != null) {\n            const depth = 0;\n            vnode.$children$.forEach((vnodeChild, index) => {\n                insertChildVNodeAnnotations(doc, vnodeChild, cmpData, hostId, depth, index);\n            });\n        }\n        // If this element does not already have a child ID and has a sibling comment node\n        // representing a slot, we use the content of the comment to set the child ID attribute\n        // on the host element.\n        if (hostElm && vnode && vnode.$elm$ && !hostElm.hasAttribute(HYDRATE_CHILD_ID)) {\n            const parent = hostElm.parentElement;\n            if (parent && parent.childNodes) {\n                const parentChildNodes = Array.from(parent.childNodes);\n                const comment = parentChildNodes.find((node) => node.nodeType === 8 /* NODE_TYPE.CommentNode */ && node['s-sr']);\n                if (comment) {\n                    const index = parentChildNodes.indexOf(hostElm) - 1;\n                    vnode.$elm$.setAttribute(HYDRATE_CHILD_ID, `${comment['s-host-id']}.${comment['s-node-id']}.0.${index}`);\n                }\n            }\n        }\n    }\n};\n/**\n * Recursively analyzes the type of a child vNode and inserts annotations on the vNodes's element based on its type.\n * Element nodes receive a child ID attribute, text nodes have a comment with the child ID inserted before them,\n * and comment nodes representing a slot have their node value set to a slot node ID containing the child ID.\n *\n * @param doc The DOM generated by the server.\n * @param vnodeChild The vNode to insert annotations for.\n * @param cmpData An object containing metadata about the component.\n * @param hostId The host ID of this element's parent.\n * @param depth How deep this element sits in the component tree relative to its parent.\n * @param index The index of this element in its parent's children array.\n */\nconst insertChildVNodeAnnotations = (doc, vnodeChild, cmpData, hostId, depth, index) => {\n    const childElm = vnodeChild.$elm$;\n    if (childElm == null) {\n        return;\n    }\n    const nodeId = cmpData.nodeIds++;\n    const childId = `${hostId}.${nodeId}.${depth}.${index}`;\n    childElm['s-host-id'] = hostId;\n    childElm['s-node-id'] = nodeId;\n    if (childElm.nodeType === 1 /* NODE_TYPE.ElementNode */) {\n        childElm.setAttribute(HYDRATE_CHILD_ID, childId);\n    }\n    else if (childElm.nodeType === 3 /* NODE_TYPE.TextNode */) {\n        const parentNode = childElm.parentNode;\n        const nodeName = parentNode === null || parentNode === void 0 ? void 0 : parentNode.nodeName;\n        if (nodeName !== 'STYLE' && nodeName !== 'SCRIPT') {\n            const textNodeId = `${TEXT_NODE_ID}.${childId}`;\n            const commentBeforeTextNode = doc.createComment(textNodeId);\n            parentNode === null || parentNode === void 0 ? void 0 : parentNode.insertBefore(commentBeforeTextNode, childElm);\n        }\n    }\n    else if (childElm.nodeType === 8 /* NODE_TYPE.CommentNode */) {\n        if (childElm['s-sr']) {\n            const slotName = childElm['s-sn'] || '';\n            const slotNodeId = `${SLOT_NODE_ID}.${childId}.${slotName}`;\n            childElm.nodeValue = slotNodeId;\n        }\n    }\n    if (vnodeChild.$children$ != null) {\n        // Increment depth each time we recur deeper into the tree\n        const childDepth = depth + 1;\n        vnodeChild.$children$.forEach((vnode, index) => {\n            insertChildVNodeAnnotations(doc, vnode, cmpData, hostId, childDepth, index);\n        });\n    }\n};\nvar _a;\n/**\n * A WeakMap mapping runtime component references to their corresponding host reference\n * instances.\n *\n * **Note**: If we're in an HMR context we need to store a reference to this\n * value on `window` in order to maintain the mapping of {@link d.RuntimeRef}\n * to {@link d.HostRef} across HMR updates.\n *\n * This is necessary because when HMR updates for a component are processed by\n * the browser-side dev server client the JS bundle for that component is\n * re-fetched. Since the module containing {@link hostRefs} is included in\n * that bundle, if we do not store a reference to it the new iteration of the\n * component will not have access to the previous hostRef map, leading to a\n * bug where the new version of the component cannot properly initialize.\n */\nconst hostRefs = BUILD.hotModuleReplacement\n    ? ((_a = window).__STENCIL_HOSTREFS__ || (_a.__STENCIL_HOSTREFS__ = new WeakMap()))\n    : new WeakMap();\n/**\n * Given a {@link d.RuntimeRef} retrieve the corresponding {@link d.HostRef}\n *\n * @param ref the runtime ref of interest\n * @returns the Host reference (if found) or undefined\n */\nconst getHostRef = (ref) => hostRefs.get(ref);\n/**\n * Register a lazy instance with the {@link hostRefs} object so it's\n * corresponding {@link d.HostRef} can be retrieved later.\n *\n * @param lazyInstance the lazy instance of interest\n * @param hostRef that instances `HostRef` object\n * @returns a reference to the host ref WeakMap\n */\nconst registerInstance = (lazyInstance, hostRef) => hostRefs.set((hostRef.$lazyInstance$ = lazyInstance), hostRef);\n/**\n * Register a host element for a Stencil component, setting up various metadata\n * and callbacks based on {@link BUILD} flags as well as the component's runtime\n * metadata.\n *\n * @param hostElement the host element to register\n * @param cmpMeta runtime metadata for that component\n * @returns a reference to the host ref WeakMap\n */\nconst registerHost = (hostElement, cmpMeta) => {\n    const hostRef = {\n        $flags$: 0,\n        $hostElement$: hostElement,\n        $cmpMeta$: cmpMeta,\n        $instanceValues$: new Map(),\n    };\n    if (BUILD.isDev) {\n        hostRef.$renderCount$ = 0;\n    }\n    if (BUILD.method && BUILD.lazyLoad) {\n        hostRef.$onInstancePromise$ = new Promise((r) => (hostRef.$onInstanceResolve$ = r));\n    }\n    if (BUILD.asyncLoading) {\n        hostRef.$onReadyPromise$ = new Promise((r) => (hostRef.$onReadyResolve$ = r));\n        hostElement['s-p'] = [];\n        hostElement['s-rc'] = [];\n    }\n    addHostEventListeners(hostElement, hostRef, cmpMeta.$listeners$, false);\n    return hostRefs.set(hostElement, hostRef);\n};\nconst isMemberInElement = (elm, memberName) => memberName in elm;\nconst consoleError = (e, el) => (customError || console.error)(e, el);\nconst STENCIL_DEV_MODE = BUILD.isTesting\n    ? ['STENCIL:'] // E2E testing\n    : [\n        '%cstencil',\n        'color: white;background:#4c47ff;font-weight: bold; font-size:10px; padding:2px 6px; border-radius: 5px',\n    ];\nconst consoleDevError = (...m) => console.error(...STENCIL_DEV_MODE, ...m);\nconst consoleDevWarn = (...m) => console.warn(...STENCIL_DEV_MODE, ...m);\nconst consoleDevInfo = (...m) => console.info(...STENCIL_DEV_MODE, ...m);\nconst setErrorHandler = (handler) => (customError = handler);\nconst cmpModules = /*@__PURE__*/ new Map();\nconst loadModule = (cmpMeta, hostRef, hmrVersionId) => {\n    // loadModuleImport\n    const exportName = cmpMeta.$tagName$.replace(/-/g, '_');\n    const bundleId = cmpMeta.$lazyBundleId$;\n    if (BUILD.isDev && typeof bundleId !== 'string') {\n        consoleDevError(`Trying to lazily load component <${cmpMeta.$tagName$}> with style mode \"${hostRef.$modeName$}\", but it does not exist.`);\n        return undefined;\n    }\n    const module = !BUILD.hotModuleReplacement ? cmpModules.get(bundleId) : false;\n    if (module) {\n        return module[exportName];\n    }\n    /*!__STENCIL_STATIC_IMPORT_SWITCH__*/\n    return import(\n    /* @vite-ignore */\n    /* webpackInclude: /\\.entry\\.js$/ */\n    /* webpackExclude: /\\.system\\.entry\\.js$/ */\n    /* webpackMode: \"lazy\" */\n    `./${bundleId}.entry.js${BUILD.hotModuleReplacement && hmrVersionId ? '?s-hmr=' + hmrVersionId : ''}`).then((importedModule) => {\n        if (!BUILD.hotModuleReplacement) {\n            cmpModules.set(bundleId, importedModule);\n        }\n        return importedModule[exportName];\n    }, consoleError);\n};\nconst styles = /*@__PURE__*/ new Map();\nconst modeResolutionChain = [];\nconst win = typeof window !== 'undefined' ? window : {};\nconst doc = win.document || { head: {} };\nconst H = (win.HTMLElement || class {\n});\nconst plt = {\n    $flags$: 0,\n    $resourcesUrl$: '',\n    jmp: (h) => h(),\n    raf: (h) => requestAnimationFrame(h),\n    ael: (el, eventName, listener, opts) => el.addEventListener(eventName, listener, opts),\n    rel: (el, eventName, listener, opts) => el.removeEventListener(eventName, listener, opts),\n    ce: (eventName, opts) => new CustomEvent(eventName, opts),\n};\nconst setPlatformHelpers = (helpers) => {\n    Object.assign(plt, helpers);\n};\nconst supportsShadow = \n// TODO(STENCIL-854): Remove code related to legacy shadowDomShim field\nBUILD.shadowDomShim && BUILD.shadowDom\n    ? /*@__PURE__*/ (() => (doc.head.attachShadow + '').indexOf('[native') > -1)()\n    : true;\nconst supportsListenerOptions = /*@__PURE__*/ (() => {\n    let supportsListenerOptions = false;\n    try {\n        doc.addEventListener('e', null, Object.defineProperty({}, 'passive', {\n            get() {\n                supportsListenerOptions = true;\n            },\n        }));\n    }\n    catch (e) { }\n    return supportsListenerOptions;\n})();\nconst promiseResolve = (v) => Promise.resolve(v);\nconst supportsConstructableStylesheets = BUILD.constructableCSS\n    ? /*@__PURE__*/ (() => {\n        try {\n            new CSSStyleSheet();\n            return typeof new CSSStyleSheet().replaceSync === 'function';\n        }\n        catch (e) { }\n        return false;\n    })()\n    : false;\nconst queueDomReads = [];\nconst queueDomWrites = [];\nconst queueDomWritesLow = [];\nconst queueTask = (queue, write) => (cb) => {\n    queue.push(cb);\n    if (!queuePending) {\n        queuePending = true;\n        if (write && plt.$flags$ & 4 /* PLATFORM_FLAGS.queueSync */) {\n            nextTick(flush);\n        }\n        else {\n            plt.raf(flush);\n        }\n    }\n};\nconst consume = (queue) => {\n    for (let i = 0; i < queue.length; i++) {\n        try {\n            queue[i](performance.now());\n        }\n        catch (e) {\n            consoleError(e);\n        }\n    }\n    queue.length = 0;\n};\nconst consumeTimeout = (queue, timeout) => {\n    let i = 0;\n    let ts = 0;\n    while (i < queue.length && (ts = performance.now()) < timeout) {\n        try {\n            queue[i++](ts);\n        }\n        catch (e) {\n            consoleError(e);\n        }\n    }\n    if (i === queue.length) {\n        queue.length = 0;\n    }\n    else if (i !== 0) {\n        queue.splice(0, i);\n    }\n};\nconst flush = () => {\n    if (BUILD.asyncQueue) {\n        queueCongestion++;\n    }\n    // always force a bunch of medium callbacks to run, but still have\n    // a throttle on how many can run in a certain time\n    // DOM READS!!!\n    consume(queueDomReads);\n    // DOM WRITES!!!\n    if (BUILD.asyncQueue) {\n        const timeout = (plt.$flags$ & 6 /* PLATFORM_FLAGS.queueMask */) === 2 /* PLATFORM_FLAGS.appLoaded */\n            ? performance.now() + 14 * Math.ceil(queueCongestion * (1.0 / 10.0))\n            : Infinity;\n        consumeTimeout(queueDomWrites, timeout);\n        consumeTimeout(queueDomWritesLow, timeout);\n        if (queueDomWrites.length > 0) {\n            queueDomWritesLow.push(...queueDomWrites);\n            queueDomWrites.length = 0;\n        }\n        if ((queuePending = queueDomReads.length + queueDomWrites.length + queueDomWritesLow.length > 0)) {\n            // still more to do yet, but we've run out of time\n            // let's let this thing cool off and try again in the next tick\n            plt.raf(flush);\n        }\n        else {\n            queueCongestion = 0;\n        }\n    }\n    else {\n        consume(queueDomWrites);\n        if ((queuePending = queueDomReads.length > 0)) {\n            // still more to do yet, but we've run out of time\n            // let's let this thing cool off and try again in the next tick\n            plt.raf(flush);\n        }\n    }\n};\nconst nextTick = (cb) => promiseResolve().then(cb);\nconst readTask = /*@__PURE__*/ queueTask(queueDomReads, false);\nconst writeTask = /*@__PURE__*/ queueTask(queueDomWrites, true);\nexport { BUILD, Env, NAMESPACE } from '@stencil/core/internal/app-data';\nexport { Build, Fragment, H, H as HTMLElement, Host, STENCIL_DEV_MODE, addHostEventListeners, bootstrapLazy, cmpModules, connectedCallback, consoleDevError, consoleDevInfo, consoleDevWarn, consoleError, createEvent, defineCustomElement, disconnectedCallback, doc, forceModeUpdate, forceUpdate, getAssetPath, getElement, getHostRef, getMode, getRenderingRef, getValue, h, insertVdomAnnotations, isMemberInElement, loadModule, modeResolutionChain, nextTick, parsePropertyValue, plt, postUpdateComponent, promiseResolve, proxyComponent, proxyCustomElement, readTask, registerHost, registerInstance, renderVdom, setAssetPath, setErrorHandler, setMode, setNonce, setPlatformHelpers, setPlatformOptions, setValue, styles, supportsConstructableStylesheets, supportsListenerOptions, supportsShadow, win, writeTask };\n"], "mappings": "AAAO,MAAMA,EAAY,0BCQzB,IAAIC,EAEJ,IAAIC,EAMJ,IAAIC,EAAY,MAGhB,IAAIC,EAAe,MAgBnB,MAAMC,EAAa,CAACC,EAAQC,EAAU,MAQ7B,CACD,MAAO,MAGf,GAEA,MAAMC,EAAa,CAACC,EAAKC,KAWhB,CACD,MAAO,MAGf,GAgEA,MAAMC,EAAe,mDAOrB,MAAMC,EAAc,yDAepB,MAAMC,EAAY,GAIlB,MAAMC,EAAS,6BACf,MAAMC,EAAU,+BAChB,MAAMC,EAASC,GAAMA,GAAK,KAQ1B,MAAMC,EAAiBC,IAEnBA,SAAWA,EACX,OAAOA,IAAM,UAAYA,IAAM,UAAU,EAU7C,SAASC,EAAyBC,GAC9B,IAAIC,EAAIC,EAAIC,EACZ,OAAQA,GAAMD,GAAMD,EAAKD,EAAII,QAAU,MAAQH,SAAY,OAAS,EAAIA,EAAGI,cAAc,6BAA+B,MAAQH,SAAY,OAAS,EAAIA,EAAGI,aAAa,cAAgB,MAAQH,SAAY,EAAIA,EAAKI,SAC1N,CAWK,MAACC,EAAI,CAACC,EAAUC,KAAcC,KAC/B,IAAIC,EAAQ,KACZ,IAAIxB,EAAM,KAEV,IAAIyB,EAAS,MACb,IAAIC,EAAa,MACjB,MAAMC,EAAgB,GACtB,MAAMC,EAAQC,IACV,IAAK,IAAIC,EAAI,EAAGA,EAAID,EAAEE,OAAQD,IAAK,CAC/BN,EAAQK,EAAEC,GACV,GAAIE,MAAMC,QAAQT,GAAQ,CACtBI,EAAKJ,EACrB,MACiB,GAAIA,GAAS,aAAeA,IAAU,UAAW,CAClD,GAAKC,SAAgBJ,IAAa,aAAeZ,EAAce,GAAS,CACpEA,EAAQU,OAAOV,EACnC,CAMgB,GAAIC,GAAUC,EAAY,CAEtBC,EAAcA,EAAcI,OAAS,GAAGI,GAAUX,CACtE,KACqB,CAEDG,EAAcS,KAAKX,EAASY,EAAS,KAAMb,GAASA,EACxE,CACgBE,EAAaD,CAC7B,CACA,GAEIG,EAAKL,GACL,GAAID,EAAW,CAIX,GAAqBA,EAAUtB,IAAK,CAChCA,EAAMsB,EAAUtB,GAC5B,CAK6B,CACjB,MAAMsC,EAAYhB,EAAUiB,WAAajB,EAAUkB,MACnD,GAAIF,EAAW,CACXhB,EAAUkB,aACCF,IAAc,SACfA,EACAG,OAAOC,KAAKJ,GACTK,QAAQC,GAAMN,EAAUM,KACxBC,KAAK,IAClC,CACA,CACA,CAMI,UAAmCxB,IAAa,WAAY,CAExD,OAAOA,EAASC,IAAc,KAAO,GAAKA,EAAWK,EAAemB,EAC5E,CACI,MAAMC,EAAQV,EAAShB,EAAU,MACjC0B,EAAMC,EAAU1B,EAChB,GAAIK,EAAcI,OAAS,EAAG,CAC1BgB,EAAME,EAAatB,CAC3B,CACuB,CACfoB,EAAMG,EAAQlD,CACtB,CAII,OAAO+C,CAAK,EAUhB,MAAMV,EAAW,CAACc,EAAKC,KACnB,MAAML,EAAQ,CACVM,EAAS,EACTC,EAAOH,EACPhB,EAAQiB,EACRG,EAAO,KACPN,EAAY,MAES,CACrBF,EAAMC,EAAU,IACxB,CACuB,CACfD,EAAMG,EAAQ,IACtB,CAII,OAAOH,CAAK,EAEX,MAACS,EAAO,GAOb,MAAMC,EAAUC,GAASA,GAAQA,EAAKJ,IAAUE,EAQhD,MAAMV,EAAc,CAChBa,QAAS,CAACpC,EAAUqC,IAAOrC,EAASsC,IAAIC,GAAiBH,QAAQC,GACjEC,IAAK,CAACtC,EAAUqC,IAAOrC,EAASsC,IAAIC,GAAiBD,IAAID,GAAIC,IAAIE,IASrE,MAAMD,EAAmBJ,IAAI,CACzBM,OAAQN,EAAKV,EACbiB,UAAWP,EAAKT,EAChBiB,KAAMR,EAAKR,EACXiB,MAAOT,EAAKU,EACZC,KAAMX,EAAKJ,EACXgB,MAAOZ,EAAKvB,IAWhB,MAAM4B,EAAoBL,IACtB,UAAWA,EAAKW,OAAS,WAAY,CACjC,MAAM/C,EAAYmB,OAAO8B,OAAO,GAAIb,EAAKM,QACzC,GAAIN,EAAKQ,KAAM,CACX5C,EAAUtB,IAAM0D,EAAKQ,IACjC,CACQ,GAAIR,EAAKS,MAAO,CACZ7C,EAAUkD,KAAOd,EAAKS,KAClC,CACQ,OAAO/C,EAAEsC,EAAKW,KAAM/C,KAAeoC,EAAKO,WAAa,GAC7D,CACI,MAAMlB,EAAQV,EAASqB,EAAKW,KAAMX,EAAKY,OACvCvB,EAAMC,EAAUU,EAAKM,OACrBjB,EAAME,EAAaS,EAAKO,UACxBlB,EAAMG,EAAQQ,EAAKQ,KACnBnB,EAAMqB,EAASV,EAAKS,MACpB,OAAOpB,CAAK,EAkShB,MAAM0B,EAAqB,CAACC,EAAWC,KAEnC,GAAID,GAAa,OAASjE,EAAciE,GAAY,CAMhD,GAAwBC,EAAW,EAA6B,CAE5D,OAAOC,WAAWF,EAC9B,CACQ,GAAwBC,EAAW,EAA6B,CAG5D,OAAOzC,OAAOwC,EAC1B,CAEQ,OAAOA,CACf,CAGI,OAAOA,CAAS,EAEpB,MAAMG,EAAcC,GAA0BC,GAAWD,GAAwB,cAC5E,MAACE,EAAc,CAACF,EAAKN,EAAMS,KAC5B,MAAMC,EAAML,EAAWC,GACvB,MAAO,CACHK,KAAOC,GAIIC,EAAUH,EAAKV,EAAM,CACxBc,WAAYL,EAAQ,GACpBM,YAAaN,EAAQ,GACrBO,cAAeP,EAAQ,GACvBG,WAGX,EASL,MAAMC,EAAY,CAACH,EAAKV,EAAMiB,KAC1B,MAAMC,EAAKC,GAAIC,GAAGpB,EAAMiB,GACxBP,EAAIW,cAAcH,GAClB,OAAOA,CAAE,EAEb,MAAMI,EAAkC,IAAIC,QAC5C,MAAMC,EAAgB,CAACxG,EAASyG,EAASC,KACrC,IAAIC,EAAQC,GAAOC,IAAI7G,GACvB,GAAI8G,IAAoCJ,EAAS,CAC7CC,EAASA,GAAS,IAAII,cACtB,UAAWJ,IAAU,SAAU,CAC3BA,EAAQF,CACpB,KACa,CACDE,EAAMK,YAAYP,EAC9B,CACA,KACS,CACDE,EAAQF,CAChB,CACIG,GAAOK,IAAIjH,EAAS2G,EAAM,EAE9B,MAAMO,EAAW,CAACC,EAAoBC,EAASC,KAC3C,IAAIhG,EACJ,MAAMrB,EAAUsH,EAAWF,GAC3B,MAAMT,EAAQC,GAAOC,IAAI7G,GAMzBmH,EAAqBA,EAAmBI,WAAa,GAAsCJ,EAAqB/F,GAChH,GAAIuF,EAAO,CACP,UAAWA,IAAU,SAAU,CAC3BQ,EAAqBA,EAAmB3F,MAAQ2F,EAChD,IAAIK,EAAgBlB,EAAkBO,IAAIM,GAC1C,IAAIM,EACJ,IAAKD,EAAe,CAChBlB,EAAkBW,IAAIE,EAAqBK,EAAgB,IAAIE,IAC/E,CACY,IAAKF,EAAcG,IAAI3H,GAAU,CAOxB,CACDyH,EAAWrG,GAAIwG,cAAc,SAC7BH,EAASI,UAAYlB,EAErB,MAAMmB,GAASzG,EAAK8E,GAAI4B,KAAa,MAAQ1G,SAAY,EAAIA,EAAKF,EAAyBC,IAC3F,GAAI0G,GAAS,KAAM,CACfL,EAASO,aAAa,QAASF,EACvD,CAIoBX,EAAmBc,aAAaR,EAAUN,EAAmB1F,cAAc,QAC/F,CAEgB,GAAI2F,EAAQvD,EAAU,EAAqC,CACvD4D,EAASI,WAAalH,CAC1C,CACgB,GAAI6G,EAAe,CACfA,EAAcU,IAAIlI,EACtC,CACA,CACA,MACa,IAA+BmH,EAAmBgB,mBAAmBC,SAASzB,GAAQ,CACvFQ,EAAmBgB,mBAAqB,IAAIhB,EAAmBgB,mBAAoBxB,EAC/F,CACA,CACI,OAAO3G,CAAO,EAElB,MAAMqI,EAAgBC,IAClB,MAAMlB,EAAUkB,EAAQC,EACxB,MAAM7C,EAAM4C,EAAQE,cACpB,MAAM/C,EAAQ2B,EAAQvD,EACtB,MAAM4E,EAAkBrI,EAAW,eAAgBgH,EAAQsB,GAC3D,MAAM1I,EAAUkH,EAA8CxB,EAAIiD,WAAajD,EAAIiD,WAAajD,EAAIkD,cAAexB,GACnH,GAAiE3B,EAAQ,GAA6C,CAQlHC,EAAI,QAAU1F,EACd0F,EAAImD,UAAUX,IAAIlI,EAAU,KAIpC,CACIyI,GAAiB,EAErB,MAAMnB,EAAa,CAACwB,EAAKzB,IAAS,MAAuGyB,EAAa,EAyBtJ,MAAMC,EAAc,CAACrD,EAAKsD,EAAYC,EAAUC,EAAUC,EAAO1D,KAC7D,GAAIwD,IAAaC,EAAU,CACvB,IAAIE,EAASC,GAAkB3D,EAAKsD,GACpC,IAAIM,EAAKN,EAAWO,cACpB,GAAuBP,IAAe,QAAS,CAC3C,MAAMH,EAAYnD,EAAImD,UACtB,MAAMW,EAAaC,EAAeR,GAClC,MAAMS,EAAaD,EAAeP,GAClCL,EAAUc,UAAUH,EAAWrG,QAAQd,GAAMA,IAAMqH,EAAWtB,SAAS/F,MACvEwG,EAAUX,OAAOwB,EAAWvG,QAAQd,GAAMA,IAAMmH,EAAWpB,SAAS/F,KAChF,MACa,GAAuB2G,IAAe,QAAS,CAE3B,CACjB,IAAK,MAAMY,KAAQX,EAAU,CACzB,IAAKC,GAAYA,EAASU,IAAS,KAAM,CACrC,GAAgCA,EAAKxB,SAAS,KAAM,CAChD1C,EAAIiB,MAAMkD,eAAeD,EACrD,KAC6B,CACDlE,EAAIiB,MAAMiD,GAAQ,EAC9C,CACA,CACA,CACA,CACY,IAAK,MAAMA,KAAQV,EAAU,CACzB,IAAKD,GAAYC,EAASU,KAAUX,EAASW,GAAO,CAChD,GAAgCA,EAAKxB,SAAS,KAAM,CAChD1C,EAAIiB,MAAMmD,YAAYF,EAAMV,EAASU,GAC7D,KACyB,CACDlE,EAAIiB,MAAMiD,GAAQV,EAASU,EACnD,CACA,CACA,CACA,MACa,GAAqBZ,IAAe,YAEpC,GAAqBA,IAAe,MAAO,CAE5C,GAAIE,EAAU,CACVA,EAASxD,EACzB,CACA,MACa,IACkB0D,GACnBJ,EAAW,KAAO,KAClBA,EAAW,KAAO,IAAK,CAKvB,GAAIA,EAAW,KAAO,IAAK,CAQvBA,EAAaA,EAAWe,MAAM,EAC9C,MACiB,GAAIV,GAAkBW,GAAKV,GAAK,CAKjCN,EAAaM,EAAGS,MAAM,EACtC,KACiB,CAMDf,EAAaM,EAAG,GAAKN,EAAWe,MAAM,EACtD,CACY,GAAId,GAAYC,EAAU,CAItB,MAAMe,EAAUjB,EAAWkB,SAASC,GAEpCnB,EAAaA,EAAWoB,QAAQC,EAAqB,IACrD,GAAIpB,EAAU,CACV9C,GAAImE,IAAI5E,EAAKsD,EAAYC,EAAUgB,EACvD,CACgB,GAAIf,EAAU,CACV/C,GAAIoE,IAAI7E,EAAKsD,EAAYE,EAAUe,EACvD,CACA,CACA,KACuC,CAE3B,MAAMO,EAAYvJ,EAAciI,GAChC,IAAKE,GAAWoB,GAAatB,IAAa,QAAWC,EAAO,CACxD,IACI,IAAKzD,EAAIpF,QAAQ8H,SAAS,KAAM,CAC5B,MAAMqC,EAAIvB,GAAY,KAAO,GAAKA,EAElC,GAAIF,IAAe,OAAQ,CACvBI,EAAS,KACrC,MAC6B,GAAIH,GAAY,MAAQvD,EAAIsD,IAAeyB,EAAG,CAC/C/E,EAAIsD,GAAcyB,CAC9C,CACA,KACyB,CACD/E,EAAIsD,GAAcE,CAC1C,CACA,CACgB,MAAOwB,GAIvB,CACA,CAeY,GAAIxB,GAAY,MAAQA,IAAa,MAAO,CACxC,GAAIA,IAAa,OAASxD,EAAIhE,aAAasH,KAAgB,GAAI,CAItD,CACDtD,EAAIiF,gBAAgB3B,EAC5C,CACA,CACA,MACiB,KAAMI,GAAU3D,EAAQ,GAA8B0D,KAAWqB,EAAW,CAC7EtB,EAAWA,IAAa,KAAO,GAAKA,EAI/B,CACDxD,EAAIsC,aAAagB,EAAYE,EACjD,CACA,CACA,CACA,GAEA,MAAM0B,EAAsB,KAM5B,MAAMnB,EAAkBoB,IAAYA,EAAQ,GAAKA,EAAMC,MAAMF,GAC7D,MAAMT,EAAuB,UAC7B,MAAME,EAAsB,IAAIU,OAAOZ,EAAuB,KAC9D,MAAMa,EAAgB,CAACC,EAAUC,EAAUhL,EAAW8I,KAIlD,MAAMtD,EAAMwF,EAASnH,EAAMwD,WAAa,IAAuC2D,EAASnH,EAAMoH,KACxFD,EAASnH,EAAMoH,KACfD,EAASnH,EACf,MAAMqH,EAAiBH,GAAYA,EAASzH,GAAY5C,EACxD,MAAMyK,EAAgBH,EAAS1H,GAAW5C,EACrB,CAEjB,IAAKoI,KAAcoC,EAAe,CAC9B,KAAMpC,KAAcqC,GAAgB,CAChCtC,EAAYrD,EAAKsD,EAAYoC,EAAcpC,GAAarH,UAAWzB,EAAWgL,EAASrH,EACvG,CACA,CACA,CAEI,IAAKmF,KAAcqC,EAAe,CAC9BtC,EAAYrD,EAAKsD,EAAYoC,EAAcpC,GAAaqC,EAAcrC,GAAa9I,EAAWgL,EAASrH,EAC/G,GAYA,MAAMyH,EAAY,CAACC,EAAgBC,EAAgBC,EAAYC,KAE3D,MAAM7I,EAAW2I,EAAe/H,EAAWgI,GAC3C,IAAInJ,EAAI,EACR,IAAIoD,EACJ,IAAIiG,EAoBJ,GAAsB9I,EAASF,IAAW,KAAM,CAE5C+C,EAAM7C,EAASkB,EAAQ3C,GAAIwK,eAAe/I,EAASF,EAC3D,KAMS,CACD,IAAkBzC,EAAW,CACzBA,EAAY2C,EAASiB,IAAU,KAC3C,CAEQ4B,EAAM7C,EAASkB,EACT3C,GAAIyK,gBAAgB3L,EAAYW,EAASC,EAErC+B,EAASiB,GAInB,GAAiB5D,GAAa2C,EAASiB,IAAU,gBAAiB,CAC9D5D,EAAY,KACxB,CAEiC,CACrB8K,EAAc,KAAMnI,EAAU3C,EAC1C,CACQ,GAAyCa,EAAMf,IAAY0F,EAAI,UAAY1F,EAAS,CAGhF0F,EAAImD,UAAUX,IAAKxC,EAAI,QAAU1F,EAC7C,CACQ,GAAI6C,EAASY,EAAY,CACrB,IAAKnB,EAAI,EAAGA,EAAIO,EAASY,EAAWlB,SAAUD,EAAG,CAE7CqJ,EAAYL,EAAUC,EAAgB1I,EAAUP,GAEhD,GAAIqJ,EAAW,CAEXjG,EAAIoG,YAAYH,EACpC,CACA,CACA,CACuB,CACX,GAAI9I,EAASiB,IAAU,MAAO,CAE1B5D,EAAY,KAC5B,MACiB,GAAIwF,EAAIpF,UAAY,gBAAiB,CAEtCJ,EAAY,IAC5B,CACA,CACA,CAGIwF,EAAI,QAAUzF,EAyBd,OAAOyF,CAAG,EAkEd,MAAMqG,EAAY,CAACL,EAAWM,EAAQC,EAAaC,EAAQC,EAAUC,KACjE,IAAIC,EAAY,EAChB,IAAIV,EACJ,GAAuBU,EAAa1D,YAAc0D,EAAa/L,UAAYL,EAAa,CACpFoM,EAAeA,EAAa1D,UACpC,CACI,KAAOwD,GAAYC,IAAUD,EAAU,CACnC,GAAID,EAAOC,GAAW,CAClBR,EAAYL,EAAU,KAAMW,EAAaE,GACzC,GAAIR,EAAW,CACXO,EAAOC,GAAUpI,EAAQ4H,EACzBU,EAAapE,aAAa0D,EAA0DK,EACpG,CACA,CACA,GAaA,MAAMM,EAAe,CAACJ,EAAQC,EAAUC,KACpC,IAAK,IAAIG,EAAQJ,EAAUI,GAASH,IAAUG,EAAO,CACjD,MAAMhJ,EAAQ2I,EAAOK,GACrB,GAAIhJ,EAAO,CACP,MAAMmC,EAAMnC,EAAMQ,EAClByI,EAAiBjJ,GACjB,GAAImC,EAAK,CAgBLA,EAAIiE,QACpB,CACA,CACA,GAuEA,MAAM8C,EAAiB,CAACf,EAAWgB,EAAO7J,EAAU8J,EAAOC,EAAkB,SACzE,IAAIC,EAAc,EAClB,IAAIC,EAAc,EAClB,IAAIC,EAAW,EACf,IAAIzK,EAAI,EACR,IAAI0K,EAAYN,EAAMnK,OAAS,EAC/B,IAAI0K,EAAgBP,EAAM,GAC1B,IAAIQ,EAAcR,EAAMM,GACxB,IAAIG,EAAYR,EAAMpK,OAAS,EAC/B,IAAI6K,EAAgBT,EAAM,GAC1B,IAAIU,EAAcV,EAAMQ,GACxB,IAAIjJ,EACJ,IAAIoJ,EACJ,MAAOT,GAAeG,GAAaF,GAAeK,EAAW,CACzD,GAAIF,GAAiB,KAAM,CAEvBA,EAAgBP,IAAQG,EACpC,MACa,GAAIK,GAAe,KAAM,CAC1BA,EAAcR,IAAQM,EAClC,MACa,GAAII,GAAiB,KAAM,CAC5BA,EAAgBT,IAAQG,EACpC,MACa,GAAIO,GAAe,KAAM,CAC1BA,EAAcV,IAAQQ,EAClC,MACa,GAAII,EAAYN,EAAeG,EAAeR,GAAkB,CAKjEY,EAAMP,EAAeG,EAAeR,GACpCK,EAAgBP,IAAQG,GACxBO,EAAgBT,IAAQG,EACpC,MACa,GAAIS,EAAYL,EAAaG,EAAaT,GAAkB,CAI7DY,EAAMN,EAAaG,EAAaT,GAChCM,EAAcR,IAAQM,GACtBK,EAAcV,IAAQQ,EAClC,MACa,GAAII,EAAYN,EAAeI,EAAaT,GAAkB,CAkB/DY,EAAMP,EAAeI,EAAaT,GAkBlClB,EAAUzD,aAAagF,EAAclJ,EAAOmJ,EAAYnJ,EAAM0J,aAC9DR,EAAgBP,IAAQG,GACxBQ,EAAcV,IAAQQ,EAClC,MACa,GAAII,EAAYL,EAAaE,EAAeR,GAAkB,CAmB/DY,EAAMN,EAAaE,EAAeR,GAMlClB,EAAUzD,aAAaiF,EAAYnJ,EAAOkJ,EAAclJ,GACxDmJ,EAAcR,IAAQM,GACtBI,EAAgBT,IAAQG,EACpC,KACa,CASDC,GAAY,EACO,CACf,IAAKzK,EAAIuK,EAAavK,GAAK0K,IAAa1K,EAAG,CACvC,GAAIoK,EAAMpK,IAAMoK,EAAMpK,GAAGoB,IAAU,MAAQgJ,EAAMpK,GAAGoB,IAAU0J,EAAc1J,EAAO,CAC/EqJ,EAAWzK,EACX,KACxB,CACA,CACA,CACY,GAAqByK,GAAY,EAAG,CAGhCO,EAAYZ,EAAMK,GAClB,GAAIO,EAAUxJ,IAAUsJ,EAActJ,EAAO,CAEzCI,EAAOoH,EAAUoB,GAASA,EAAMI,GAAcjK,EAAUkK,EAC5E,KACqB,CACDS,EAAMF,EAAWF,EAAeR,GAGhCF,EAAMK,GAAYpL,UAClBuC,EAAOoJ,EAAUvJ,CACrC,CACgBqJ,EAAgBT,IAAQG,EACxC,KACiB,CAKD5I,EAAOoH,EAAUoB,GAASA,EAAMI,GAAcjK,EAAUiK,GACxDM,EAAgBT,IAAQG,EACxC,CACY,GAAI5I,EAAM,CAKD,CACD+I,EAAclJ,EAAM2J,WAAWzF,aAAa/D,EAAM+I,EAAclJ,EACpF,CACA,CACA,CACA,CACI,GAAI8I,EAAcG,EAAW,CAEzBjB,EAAUL,EAAWiB,EAAMQ,EAAY,IAAM,KAAO,KAAOR,EAAMQ,EAAY,GAAGpJ,EAAOlB,EAAU8J,EAAOG,EAAaK,EAC7H,MACS,GAAuBL,EAAcK,EAAW,CAIjDb,EAAaI,EAAOG,EAAaG,EACzC,GAqBA,MAAMO,EAAc,CAACI,EAAWC,EAAYhB,EAAkB,SAG1D,GAAIe,EAAU7J,IAAU8J,EAAW9J,EAAO,CAStC,IAAsB8I,EAAiB,CACnC,OAAOe,EAAUjK,IAAUkK,EAAWlK,CAClD,CACQ,OAAO,IACf,CACI,OAAO,KAAK,EAmBhB,MAAM8J,EAAQ,CAACK,EAAUhL,EAAU+J,EAAkB,SACjD,MAAMlH,EAAO7C,EAASkB,EAAQ8J,EAAS9J,EACvC,MAAM+J,EAAcD,EAASpK,EAC7B,MAAMsK,EAAclL,EAASY,EAC7B,MAAME,EAAMd,EAASiB,EACrB,MAAMF,EAAOf,EAASF,EAEtB,GAAuBiB,IAAS,KAAM,CACnB,CAGX1D,EAAYyD,IAAQ,MAAQ,KAAOA,IAAQ,gBAAkB,MAAQzD,CACjF,CACkD,CACtC,GAAkByD,IAAQ,YAErB,CAIDqH,EAAc6C,EAAUhL,EAAU3C,EAClD,CACA,CACQ,GAAuB4N,IAAgB,MAAQC,IAAgB,KAAM,CAGjEtB,EAAe/G,EAAKoI,EAAajL,EAAUkL,EAAanB,EACpE,MACa,GAAImB,IAAgB,KAAM,CAE3B,GAAyCF,EAASlL,IAAW,KAAM,CAE/D+C,EAAIsI,YAAc,EAClC,CAEYjC,EAAUrG,EAAK,KAAM7C,EAAUkL,EAAa,EAAGA,EAAYxL,OAAS,EAChF,MACa,GAAuBuL,IAAgB,KAAM,CAE9CxB,EAAawB,EAAa,EAAGA,EAAYvL,OAAS,EAC9D,CACQ,GAAiBrC,GAAayD,IAAQ,MAAO,CACzCzD,EAAY,KACxB,CACA,MAKS,GAAsB2N,EAASlL,IAAWiB,EAAM,CAGjD8B,EAAIuI,KAAOrK,CACnB,GA0LA,MAAM4I,EAAoB0B,IACH,CACfA,EAAM1K,GAAW0K,EAAM1K,EAAQ8B,KAAO4I,EAAM1K,EAAQ8B,IAAI,MACxD4I,EAAMzK,GAAcyK,EAAMzK,EAAWY,IAAImI,EACjD,GAeA,MAAM2B,EAAa,CAAC7F,EAAS8F,EAAiBC,EAAgB,SAE1D,MAAMC,EAAUhG,EAAQE,cAExB,MAAMqF,EAAWvF,EAAQiG,GAAW1L,EAAS,KAAM,MAMnD,MAAM2L,EAAYvK,EAAOmK,GAAmBA,EAAkBxM,EAAE,KAAM,KAAMwM,GAC5EnO,EAAcqO,EAAQhO,QA0BtB,GAAI+N,GAAiBG,EAAUhL,EAAS,CACpC,IAAK,MAAMhD,KAAOyC,OAAOC,KAAKsL,EAAUhL,GAAU,CAS9C,GAAI8K,EAAQG,aAAajO,KAAS,CAAC,MAAO,MAAO,QAAS,SAAS4H,SAAS5H,GAAM,CAC9EgO,EAAUhL,EAAQhD,GAAO8N,EAAQ9N,EACjD,CACA,CACA,CACIgO,EAAU1K,EAAQ,KAClB0K,EAAU3K,GAAW,EACrByE,EAAQiG,EAAUC,EAClBA,EAAUzK,EAAQ8J,EAAS9J,EAA2BuK,EAAQ3F,YAAc2F,EACvC,CACjCtO,EAAUsO,EAAQ,OAC1B,CAQId,EAAMK,EAAUW,EAAWH,EAAc,EA6H7C,MAAMK,EAAmB,CAACpG,EAASqG,KAC/B,GAA0BA,IAAsBrG,EAAQsG,GAAqBD,EAAkB,OAAQ,CACnGA,EAAkB,OAAO/L,KAAK,IAAIiM,SAASC,GAAOxG,EAAQsG,EAAoBE,IACtF,GAEA,MAAMC,EAAiB,CAACzG,EAAS+F,KACW,CACpC/F,EAAQzE,GAAW,EAC3B,CACI,GAA0ByE,EAAQzE,EAAU,EAAyC,CACjFyE,EAAQzE,GAAW,IACnB,MACR,CACI6K,EAAiBpG,EAASA,EAAQ0G,GAIlC,MAAMC,EAAW,IAAMC,EAAc5G,EAAS+F,GAC9C,OAAyBc,GAAUF,EAAsB,EAY7D,MAAMC,EAAgB,CAAC5G,EAAS+F,KAE5B,MAAMe,EAAchP,EAAW,iBAAkBkI,EAAQC,EAAUG,GACnE,MAAM2G,EAA4B/G,EAAQgH,EAa1C,IAAIC,EAkCJH,IACA,OAAOI,EAAQD,GAAc,IAAME,EAAgBnH,EAAS+G,EAAUhB,IAAe,EAkBzF,MAAMmB,EAAU,CAACD,EAAcG,IAAOC,EAAWJ,GAAgBA,EAAaK,KAAKF,GAAMA,IAWzF,MAAMC,EAAcJ,GAAiBA,aAAwBV,SACxDU,GAAgBA,EAAaK,aAAeL,EAAaK,OAAS,WAWvE,MAAMH,EAAkBI,MAAOvH,EAAS+G,EAAUhB,KAC9C,IAAIhN,EACJ,MAAMqE,EAAM4C,EAAQE,cACpB,MAAMsH,EAAY1P,EAAW,SAAUkI,EAAQC,EAAUG,GACzD,MAAMqH,EAAKrK,EAAI,QACf,GAAmB2I,EAAe,CAE9BhG,EAAaC,EACrB,CACI,MAAM0H,EAAY5P,EAAW,SAAUkI,EAAQC,EAAUG,GAOpD,CACDuH,EAAW3H,EAAS+G,EAAU3J,EAAK2I,EAC3C,CAuBI,GAA0B0B,EAAI,CAI1BA,EAAG1L,KAAKD,GAAOA,MACfsB,EAAI,QAAU/D,SACtB,CACIqO,IACAF,IACwB,CACpB,MAAMI,GAAoB7O,EAAKqE,EAAI,UAAY,MAAQrE,SAAY,EAAIA,EAAK,GAC5E,MAAM8O,EAAa,IAAMC,EAAoB9H,GAC7C,GAAI4H,EAAiB3N,SAAW,EAAG,CAC/B4N,GACZ,KACa,CACDtB,QAAQwB,IAAIH,GAAkBN,KAAKO,GACnC7H,EAAQzE,GAAW,EACnBqM,EAAiB3N,OAAS,CACtC,CACA,GAiBA,MAAM0N,EAAa,CAAC3H,EAAS+G,EAAU3J,EAAK2I,KAQxC,IAMIgB,EAAyBA,EAASiB,SACN,CACxBhI,EAAQzE,IAAY,EAChC,CACmC,CACvByE,EAAQzE,GAAW,CAC/B,CACgD,CACG,CAO9B,CACDsK,EAAW7F,EAAS+G,EAAUhB,EAClD,CACA,CAUA,CACA,CACI,MAAO3D,GACH6F,GAAa7F,EAAGpC,EAAQE,cAChC,CAEI,OAAO,IAAI,EAGf,MAAM4H,EAAuB9H,IACzB,MAAMhI,EAAUgI,EAAQC,EAAUG,EAClC,MAAMhD,EAAM4C,EAAQE,cACpB,MAAMgI,EAAgBpQ,EAAW,aAAcE,GAC/C,MAAM+O,EAA4B/G,EAAQgH,EAC1C,MAAMX,EAAoBrG,EAAQ0G,EAWlC,KAAM1G,EAAQzE,EAAU,IAAyC,CAC7DyE,EAAQzE,GAAW,GAC6B,CAE5C4M,GAAgB/K,EAC5B,CAC8B,CAIlBgL,GAASrB,EAAU,mBAI/B,CAEQmB,IACwB,CACpBlI,EAAQqI,EAAiBjL,GACzB,IAAKiJ,EAAmB,CACpBiC,GAChB,CACA,CACA,KACS,CAeDJ,GACR,CACwC,CAChClI,EAAQuI,EAAoBnL,EACpC,CAG4B,CACpB,GAAI4C,EAAQsG,EAAmB,CAC3BtG,EAAQsG,IACRtG,EAAQsG,EAAoBjN,SACxC,CACQ,GAAI2G,EAAQzE,EAAU,IAAoC,CACtDiN,IAAS,IAAM/B,EAAezG,EAAS,QACnD,CACQA,EAAQzE,KAAa,EAA0C,IACvE,GAkBA,MAAM+M,EAAcG,IAGU,CACtBN,GAAgBrP,GAAI4P,gBAC5B,CAIIF,IAAS,IAAMjL,EAAUmE,GAAK,UAAW,CAAEpE,OAAQ,CAAEqL,UAAWlR,MAAe,EAenF,MAAM2Q,GAAW,CAACrB,EAAU6B,EAAQC,KAChC,GAAI9B,GAAYA,EAAS6B,GAAS,CAC9B,IACI,OAAO7B,EAAS6B,GAAQC,EACpC,CACQ,MAAOzG,GACH6F,GAAa7F,EACzB,CACA,CACI,OAAO/I,SAAS,EAmBpB,MAAM8O,GAAmB/K,GACnBA,EAAImD,UAAUX,IAAI,YAgBxB,MAAMkJ,GAAW,CAAC9L,EAAK+L,IAAa9L,GAAWD,GAAKgM,EAAiBzK,IAAIwK,GACzE,MAAME,GAAW,CAACjM,EAAK+L,EAAUG,EAAQpK,KAErC,MAAMkB,EAAU/C,GAAWD,GAC3B,MAAMI,EAAuB4C,EAAQE,cACrC,MAAMiJ,EAASnJ,EAAQgJ,EAAiBzK,IAAIwK,GAC5C,MAAM5L,EAAQ6C,EAAQzE,EACtB,MAAMwL,EAA4B/G,EAAQgH,EAC1CkC,EAASvM,EAAmBuM,EAAQpK,EAAQsK,EAAUL,GAAU,IAEhE,MAAMM,EAAaC,OAAOC,MAAMJ,IAAWG,OAAOC,MAAML,GACxD,MAAMM,EAAiBN,IAAWC,IAAWE,EAC7C,MAA0BlM,EAAQ,IAA8CgM,IAAW9P,YAAcmQ,EAAgB,CAGrHxJ,EAAQgJ,EAAiBrK,IAAIoK,EAAUG,GASvC,GAAuBnC,EAAU,CAE7B,GAA2BjI,EAAQ2K,GAActM,EAAQ,IAAmC,CACxF,MAAMuM,EAAe5K,EAAQ2K,EAAWV,GACxC,GAAIW,EAAc,CAEdA,EAAa3N,KAAK4N,IACd,IAEI5C,EAAS4C,GAAiBT,EAAQC,EAAQJ,EACtE,CACwB,MAAO3G,GACH6F,GAAa7F,EAAGhF,EAC5C,IAEA,CACA,CACY,IACKD,GAAS,EAAiC,OAA4C,EAAgC,CAUvHsJ,EAAezG,EAAS,MACxC,CACA,CACA,GAYA,MAAM4J,GAAiB,CAACC,EAAM/K,EAAS3B,KACnC,IAAIpE,EACJ,MAAM+Q,EAAYD,EAAKC,UAwBvB,GAAoBhL,EAAQsK,EAAW,CACnC,GAA2BS,EAAKE,SAAU,CACtCjL,EAAQ2K,EAAaI,EAAKE,QACtC,CAEQ,MAAMC,EAAUrP,OAAOsP,QAAQnL,EAAQsK,GACvCY,EAAQjO,KAAI,EAAE2E,GAAawJ,OACvB,GACKA,EAAc,IACU/M,EAAQ,GAAmC+M,EAAc,GAA+B,CAEjHvP,OAAOwP,eAAeL,EAAWpJ,EAAY,CACzC,GAAAnC,GAEI,OAAOuK,GAASsB,KAAM1J,EAC9C,EACoB,GAAA/B,CAAIiC,GAiBAqI,GAASmB,KAAM1J,EAAYE,EAAU9B,EAC7D,EACoBuL,aAAc,KACdC,WAAY,MAEhC,MACiB,GAEDnN,EAAQ,GACR+M,EAAc,GAA8B,CAE5CvP,OAAOwP,eAAeL,EAAWpJ,EAAY,CACzC,KAAA6B,IAASgI,GACL,IAAIxR,EACJ,MAAMiE,EAAMC,GAAWmN,MACvB,OAAQrR,EAAKiE,IAAQ,MAAQA,SAAa,OAAS,EAAIA,EAAIwN,KAAyB,MAAQzR,SAAY,OAAS,EAAIA,EAAGuO,MAAK,KAAQ,IAAIvO,EAAI,OAAQA,EAAKiE,EAAIgK,KAAoB,MAAQjO,SAAY,OAAS,EAAIA,EAAG2H,MAAe6J,EAAK,GAClQ,GAEA,KAEQ,GAAkDpN,EAAQ,EAA2C,CACjG,MAAMsN,EAAqB,IAAIC,IAC/BZ,EAAUa,yBAA2B,SAAUC,EAAUjK,EAAUC,GAC/D/C,GAAIgN,KAAI,KACJ,IAAI9R,EACJ,MAAMgQ,EAAW0B,EAAmBlM,IAAIqM,GAkCxC,GAAIR,KAAKU,eAAe/B,GAAW,CAC/BnI,EAAWwJ,KAAKrB,UACTqB,KAAKrB,EACpC,MACyB,GAAIe,EAAUgB,eAAe/B,WACvBqB,KAAKrB,KAAc,UAC1BqB,KAAKrB,IAAanI,EAAU,CAI5B,MACxB,MACyB,GAAImI,GAAY,KAAM,CAGvB,MAAM/I,EAAU/C,GAAWmN,MAC3B,MAAMjN,EAAQ6C,IAAY,MAAQA,SAAiB,OAAS,EAAIA,EAAQzE,EAKxE,GAAI4B,KACEA,EAAQ,IACVA,EAAQ,KACRyD,IAAaD,EAAU,CAEvB,MAAMoG,EAA4B/G,EAAQgH,EAC1C,MAAM+D,GAAShS,EAAK+F,EAAQ2K,KAAgB,MAAQ1Q,SAAY,OAAS,EAAIA,EAAG6R,GAChFG,IAAU,MAAQA,SAAe,OAAS,EAAIA,EAAMlP,SAASmP,IACzD,GAAIjE,EAASiE,IAAiB,KAAM,CAChCjE,EAASiE,GAAcC,KAAKlE,EAAUnG,EAAUD,EAAUiK,EAC9F,IAEA,CACwB,MACxB,CACoBR,KAAKrB,GAAYnI,IAAa,aAAewJ,KAAKrB,KAAc,UAAY,MAAQnI,CAAQ,GAEhH,EAMYiJ,EAAKqB,mBAAqBhR,MAAMiR,KAAK,IAAI/L,IAAI,IACtCzE,OAAOC,MAAM7B,EAAK+F,EAAQ2K,KAAgB,MAAQ1Q,SAAY,EAAIA,EAAK,OACvEiR,EACEnP,QAAO,EAAEuQ,EAAGC,KAAOA,EAAE,GAAK,KAC1BtP,KAAI,EAAEgN,EAAUsC,MAEjB,MAAMT,EAAWS,EAAE,IAAMtC,EACzB0B,EAAmB9L,IAAIiM,EAAU7B,GAIjC,OAAO6B,CAAQ,MAGnC,CACA,CACI,OAAOf,CAAI,EAYf,MAAMyB,GAAsB/D,MAAOnK,EAAK4C,EAASlB,EAASyM,KACtD,IAAI1B,EAEJ,IAAK7J,EAAQzE,EAAU,MAAiD,EAAG,CAEvEyE,EAAQzE,GAAW,GAC4B,CAI3CsO,EAAO2B,GAAW1M,GAClB,GAAI+K,EAAKvC,KAAM,CAEX,MAAMmE,EAAUxT,IAChB4R,QAAaA,EACb4B,GAChB,CAIY,IAAqB5B,EAAK6B,UAAW,CAIR,CACrB5M,EAAQ2K,EAAaI,EAAKE,QAC9C,CACgBH,GAAeC,EAAM/K,EAAS,GAC9B+K,EAAK6B,UAAY,IACjC,CACY,MAAMC,EAAiB7T,EAAW,iBAAkBgH,EAAQsB,GAI1C,CACdJ,EAAQzE,GAAW,CACnC,CAKY,IACI,IAAIsO,EAAK7J,EACzB,CACY,MAAOoC,GACH6F,GAAa7F,EAC7B,CAC8B,CACdpC,EAAQzE,IAAY,CACpC,CACqC,CACrByE,EAAQzE,GAAW,GACnC,CACYoQ,GAEZ,CASQ,GAAmB9B,EAAKxL,MAAO,CAE3B,IAAIA,EAAQwL,EAAKxL,MAOjB,MAAM3G,EAAUsH,EAAWF,GAC3B,IAAKR,GAAOe,IAAI3H,GAAU,CACtB,MAAMkU,EAAoB9T,EAAW,iBAAkBgH,EAAQsB,GAQ/DlC,EAAcxG,EAAS2G,KAAUS,EAAQvD,EAAU,IACnDqQ,GAChB,CACA,CACA,CAEI,MAAMvF,EAAoBrG,EAAQ0G,EAClC,MAAMmF,EAAW,IAAMpF,EAAezG,EAAS,MAC/C,GAA0BqG,GAAqBA,EAAkB,QAAS,CAOtEA,EAAkB,QAAQ/L,KAAKuR,EACvC,KACS,CACDA,GACR,GAEA,MAAMC,GAAyB/E,IAAD,EAK9B,MAAMgF,GAAqB3O,IACvB,IAAKS,GAAItC,EAAU,KAA8C,EAAG,CAChE,MAAMyE,EAAU/C,GAAWG,GAC3B,MAAM0B,EAAUkB,EAAQC,EACxB,MAAM+L,EAAelU,EAAW,oBAAqBgH,EAAQsB,GAK7D,KAAMJ,EAAQzE,EAAU,GAAkC,CAEtDyE,EAAQzE,GAAW,EA0BK,CAGpB,IAAI8K,EAAoBjJ,EACxB,MAAQiJ,EAAoBA,EAAkBjB,YAAciB,EAAkBxD,KAAO,CAGjF,GAIIwD,EAAkB,OAAQ,CAG1BD,EAAiBpG,EAAUA,EAAQ0G,EAAsBL,GACzD,KACxB,CACA,CACA,CAGY,GAA8CvH,EAAQsK,EAAW,CAC7DzO,OAAOsP,QAAQnL,EAAQsK,GAAWrN,KAAI,EAAE2E,GAAawJ,OACjD,GAAIA,EAAc,IAA8B9M,EAAI0N,eAAepK,GAAa,CAC5E,MAAM6B,EAAQnF,EAAIsD,UACXtD,EAAIsD,GACXtD,EAAIsD,GAAc6B,CAC1C,IAEA,CAQiB,CACD+I,GAAoBlO,EAAK4C,EAASlB,EAClD,CACA,KACa,CAMD,GAAIkB,IAAY,MAAQA,SAAiB,OAAS,EAAIA,EAAQgH,QAGzD,GAAIhH,IAAY,MAAQA,SAAiB,OAAS,EAAIA,EAAQiM,EAAkB,CACjFjM,EAAQiM,EAAiB3E,MAAK,IAAMwE,MACpD,CACA,CACQE,GACR,GAaA,MAAME,GAAsBnF,IAAD,EAQ3B,MAAMoF,GAAuB5E,MAAOnK,IAChC,IAAKS,GAAItC,EAAU,KAA8C,EAAG,CAChE,MAAMyE,EAAU/C,GAAWG,GAUtB,GAAI4C,IAAY,MAAQA,SAAiB,OAAS,EAAIA,EAAQgH,QAG9D,GAAIhH,IAAY,MAAQA,SAAiB,OAAS,EAAIA,EAAQiM,EAAkB,CACjFjM,EAAQiM,EAAiB3E,MAAK,IAAM4E,MAChD,CACA,GAiiBK,MAACE,GAAgB,CAACC,EAAaC,EAAU,MAC1C,IAAIvT,EAKJ,MAAMwT,EAAezU,IACrB,MAAM0U,EAAU,GAChB,MAAMC,EAAUH,EAAQG,SAAW,GACnC,MAAMC,EAAiBhL,GAAIgL,eAC3B,MAAMxT,EAAOJ,GAAII,KACjB,MAAMyT,EAA4BzT,EAAKC,cAAc,iBACrD,MAAMyT,EAA2B9T,GAAIwG,cAAc,SACnD,MAAMuN,EAA6B,GAEnC,IAAIC,EACJ,IAAIC,EAAkB,KAEtBpS,OAAO8B,OAAOoB,GAAKyO,GACnBzO,GAAImP,EAAiB,IAAIC,IAAIX,EAAQY,cAAgB,KAAMpU,GAAIqU,SAASC,KAgBxE,IAAIC,EAAoB,MACxBhB,EAAYtQ,KAAKuR,IACbA,EAAW,GAAGvR,KAAKwR,IACf,IAAIxU,EACJ,MAAM+F,EAAU,CACZvD,EAASgS,EAAY,GACrBnN,EAAWmN,EAAY,GACvBnE,EAAWmE,EAAY,GACvBC,EAAaD,EAAY,IAI7B,GAAIzO,EAAQvD,EAAU,EAAqC,CACvD8R,EAAoB,IACpC,CAC8B,CACdvO,EAAQsK,EAAYmE,EAAY,EAChD,CAOqC,CACrBzO,EAAQ2K,GAAc1Q,EAAKwU,EAAY,MAAQ,MAAQxU,SAAY,EAAIA,EAAK,EAC5F,CAKY,MAAMf,EAEA8G,EAAQsB,EACd,MAAMqN,EAAc,cAAcC,YAE9B,WAAAC,CAAYC,GAERC,MAAMD,GACNA,EAAOxD,KACP0D,GAAaF,EAAM9O,GACnB,GAAuBA,EAAQvD,EAAU,EAA0C,CAK3D,CAOX,CACDqS,EAAKG,aAAa,CAAEhP,KAAM,QAC1D,CACA,CAIA,CACA,CACgB,iBAAAgN,GACI,GAAIe,EAAiB,CACjBkB,aAAalB,GACbA,EAAkB,IAC1C,CACoB,GAAIC,EAAiB,CAEjBF,EAA2BvS,KAAK8P,KACxD,KACyB,CACDvM,GAAIgN,KAAI,IAAMkB,GAAkB3B,OACxD,CACA,CACgB,oBAAA+B,GACItO,GAAIgN,KAAI,IAAMsB,GAAqB/B,OACvD,CACgB,gBAAA6D,GACI,OAAOhR,GAAWmN,MAAM6B,CAC5C,GAsCYnN,EAAQoP,EAAiBZ,EAAW,GACpC,IAAKb,EAAQ3M,SAAS9H,KAAa0U,EAAenO,IAAIvG,GAAU,CAC5DwU,EAAQlS,KAAKtC,GACb0U,EAAeyB,OAAOnW,EAAS4R,GAAe6D,EAAa3O,EAAS,GACpF,IACU,IAGN,GAAIuO,EAAmB,CACnBT,EAAWrN,WAAalH,CAChC,CAEyF,CACjFuU,EAAWrN,WAAaiN,EAAUpU,CAC1C,CAEI,GAAIwU,EAAWrN,UAAUtF,OAAQ,CAC7B2S,EAAWlN,aAAa,cAAe,IAEvC,MAAMF,GAASzG,EAAK8E,GAAI4B,KAAa,MAAQ1G,SAAY,EAAIA,EAAKF,EAAyBC,IAC3F,GAAI0G,GAAS,KAAM,CACfoN,EAAWlN,aAAa,QAASF,EAC7C,CAGQtG,EAAKyG,aAAaiN,EAAYD,EAAcA,EAAYxH,YAAcjM,EAAKkV,WACnF,CAEIrB,EAAkB,MAClB,GAAIF,EAA2B5S,OAAQ,CACnC4S,EAA2B9Q,KAAK8G,GAASA,EAAKkJ,qBACtD,KACS,CAII,CACDlO,GAAIgN,KAAI,IAAOiC,EAAkBuB,WAAW/F,EAAY,KACpE,CACA,CAEIiE,GAAc,EAEb,MAAC+B,GAAW,CAAClD,EAAG3R,IAAaA,EA0E7B,MAAC8U,GAAY/O,GAAW3B,GAAI4B,EAAUD,EA4M3C,MAAMgP,GAEA,IAAIvQ,QAOV,MAAMhB,GAAcD,GAAQwR,GAASjQ,IAAIvB,GASpC,MAACyR,GAAmB,CAACC,EAAc1O,IAAYwO,GAAS7P,IAAKqB,EAAQgH,EAAiB0H,EAAe1O,GAU1G,MAAM8N,GAAe,CAACa,EAAa7P,KAC/B,MAAMkB,EAAU,CACZzE,EAAS,EACT2E,cAAeyO,EACf1O,EAAWnB,EACXkK,EAAkB,IAAI0B,KAKU,CAChC1K,EAAQwK,EAAsB,IAAIjE,SAASC,GAAOxG,EAAQuI,EAAsB/B,GACxF,CAC4B,CACpBxG,EAAQiM,EAAmB,IAAI1F,SAASC,GAAOxG,EAAQqI,EAAmB7B,IAC1EmI,EAAY,OAAS,GACrBA,EAAY,QAAU,EAC9B,CAEI,OAAOH,GAAS7P,IAAIgQ,EAAa3O,EAAQ,EAE7C,MAAMe,GAAoB,CAAC3D,EAAKsD,IAAeA,KAActD,EAC7D,MAAM6K,GAAe,CAAC7F,EAAGwM,KAAO,EAAgBC,QAAQC,OAAO1M,EAAGwM,GAWlE,MAAMG,GAA2B,IAAIrE,IACrC,MAAMc,GAAa,CAAC1M,EAASkB,EAASuL,KAElC,MAAMyD,EAAalQ,EAAQsB,EAAU0B,QAAQ,KAAM,KACnD,MAAMmN,EAAWnQ,EAAQoP,EAKzB,MAAMgB,EAAuCH,GAAWxQ,IAAI0Q,GAC5D,GAAIC,EAAQ,CACR,OAAOA,EAAOF,EACtB;qCAEI,OAAOG,OAKP,KAAKF,aAA4F,MAAM3H,MAAM8H,IACxE,CAC7BL,GAAWpQ,IAAIsQ,EAAUG,EACrC,CACQ,OAAOA,EAAeJ,EAAW,GAClC/G,GAAa,EAEpB,MAAM3J,GAAuB,IAAIoM,IAEjC,MAAMhJ,UAAa2N,SAAW,YAAcA,OAAS,GACrD,MAAMvW,GAAM4I,GAAI4N,UAAY,CAAEpW,KAAM,IAGpC,MAAM2E,GAAM,CACRtC,EAAS,EACTyR,EAAgB,GAChBnC,IAAMvR,GAAMA,IACZiW,IAAMjW,GAAMkW,sBAAsBlW,GAClC2I,IAAK,CAAC2M,EAAIa,EAAWC,EAAU/R,IAASiR,EAAGe,iBAAiBF,EAAWC,EAAU/R,GACjFqE,IAAK,CAAC4M,EAAIa,EAAWC,EAAU/R,IAASiR,EAAGgB,oBAAoBH,EAAWC,EAAU/R,GACpFG,GAAI,CAAC2R,EAAW9R,IAAS,IAAIkS,YAAYJ,EAAW9R,IAsBnD,MAACmS,GAAkBpX,GAAM6N,QAAQwJ,QAAQrX,GAC9C,MAAM8F,GACc,MACZ,IACI,IAAIC,cACJ,cAAc,IAAIA,eAAgBC,cAAgB,UAC9D,CACQ,MAAO0D,GAAG,CACV,OAAO,KACV,EAPe,GASpB,MAAM4N,GAAgB,GACtB,MAAMC,GAAiB,GAEvB,MAAMC,GAAY,CAACC,EAAOC,IAAWtU,IACjCqU,EAAM7V,KAAKwB,GACX,IAAKjE,EAAc,CACfA,EAAe,KACf,GAAIuY,GAASvS,GAAItC,EAAU,EAAkC,CACzDiN,GAAS6H,GACrB,KACa,CACDxS,GAAI0R,IAAIc,GACpB,CACA,GAEA,MAAMC,GAAWH,IACb,IAAK,IAAInW,EAAI,EAAGA,EAAImW,EAAMlW,OAAQD,IAAK,CACnC,IACImW,EAAMnW,GAAGuW,YAAYC,MACjC,CACQ,MAAOpO,GACH6F,GAAa7F,EACzB,CACA,CACI+N,EAAMlW,OAAS,CAAC,EAoBpB,MAAMoW,GAAQ,KAOVC,GAAQN,IAqBH,CACDM,GAAQL,IACR,GAAKpY,EAAemY,GAAc/V,OAAS,EAAI,CAG3C4D,GAAI0R,IAAIc,GACpB,CACA,GAEA,MAAM7H,GAAY1M,GAAOgU,KAAiBxI,KAAKxL,GAE/C,MAAM+K,GAA0BqJ,GAAUD,GAAgB,a"}