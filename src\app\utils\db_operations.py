import logging
from typing import List, Dict, Optional

# Import the PostgreSQL database operations
from src.app.database.operations import db_ops
from src.app.database.connection import init_db as init_database

def init_db():
    """Initialize PostgreSQL database."""
    try:
        init_database()
        logging.info("PostgreSQL database initialization completed successfully")
    except Exception as e:
        logging.error(f"Database initialization failed: {e}")
        raise

def save_response_to_db(responses, id_user, tenant_code, tenant_id, random_id, src_app='winpluspharma'):
    """
    Save response data to PostgreSQL database.
    """
    try:
        id_bl = db_ops.save_response_to_db(responses, id_user, tenant_code, tenant_id, random_id, src_app)
        logging.info(f"Saved response data to PostgreSQL with ID: {id_bl}")
        return id_bl
    except Exception as e:
        logging.error(f"Error in save_response_to_db: {e}")
        raise


def get_all_pre_bl_ocr(user_id: str, tenant_id: str, src_app: str = None):
    """
    Get all pre_bl_ocr records for a user and tenant from PostgreSQL database.
    """
    try:
        return db_ops.get_all_pre_bl_ocr(user_id, tenant_id, src_app)
    except Exception as e:
        logging.error(f"Error in get_all_pre_bl_ocr: {e}")
        raise


def get_pre_bl_ocr_by_id(id_bl: int, user_id: str, tenant_id: str, src_app: str = None):
    """
    Get a specific pre_bl_ocr record by ID from PostgreSQL database.
    """
    try:
        result = db_ops.get_pre_bl_ocr_by_id(id_bl, user_id, tenant_id, src_app)
        if result:
            logging.info(f"Retrieved record with ID: {id_bl}")
        return result
    except Exception as e:
        logging.error(f"Error in get_pre_bl_ocr_by_id: {e}")
        return None


def get_pre_bl_ocr_by_random_id(random_id: str, user_id: str, tenant_id: str):
    """
    Get a specific pre_bl_ocr record by random_id from PostgreSQL database.
    """
    try:
        result = db_ops.get_pre_bl_ocr_by_random_id(random_id, user_id, tenant_id)
        if result:
            logging.info(f"Retrieved record with random_id: {random_id}")
        return result
    except Exception as e:
        logging.error(f"Error in get_pre_bl_ocr_by_random_id: {e}")
        return None


def get_all_pre_bl_ocr_by_user(user_id: str):
    """
    Get all pre_bl_ocr records for a specific user (across all tenants).
    Note: This function bypasses tenant isolation and should be used carefully.
    """
    try:
        from src.app.database.connection import get_session
        from src.app.database.models import PreBlOcr

        with get_session() as session:
            records = session.query(PreBlOcr).filter(
                PreBlOcr.status.in_(['EN_COURS', 'VALIDER']),
                PreBlOcr.ID_USER == user_id
            ).all()

            return [record.to_dict() for record in records]
    except Exception as e:
        logging.error(f"Error in get_all_pre_bl_ocr_by_user: {e}")
        return []


def get_bl_scannes_with_criteria(criteria: dict, id_hash_user: str, src_app: str = None):
    """
    Get pre_bl_ocr records with dynamic filtering criteria for windoc endpoints.
    """
    try:
        return db_ops.get_bl_scannes_with_criteria(criteria, id_hash_user, src_app)
    except Exception as e:
        logging.error(f"Error in get_bl_scannes_with_criteria: {e}")
        raise


def get_bl_scanne_by_id_with_criteria(id_bl: int, id_hash_user: str, src_app: str = None):
    """
    Get a specific pre_bl_ocr record by ID for windoc endpoints.
    """
    try:
        result = db_ops.get_bl_scanne_by_id_with_criteria(id_bl, id_hash_user, src_app)
        if result:
            logging.info(f"Retrieved windoc record with ID: {id_bl}")
        return result
    except Exception as e:
        logging.error(f"Error in get_bl_scanne_by_id_with_criteria: {e}")
        return None


def update_bl_status_valider_windoc(criteria: dict, id_hash_user: str):
    """
    Update BL status to 'VALIDER' for windoc endpoints using criteria.
    """
    try:
        result = db_ops.update_bl_status_valider_windoc(criteria, id_hash_user)
        if result:
            logging.info(f"Updated BL status to VALIDER for criteria: {criteria}")
        return result
    except Exception as e:
        logging.error(f"Error in update_bl_status_valider_windoc: {e}")
        return 0


def update_bl_status(bl_id, new_status, id_BL_origine, date_BL_origine, supplier_name, supplier_id):
    """
    Update BL status in PostgreSQL database.
    """
    try:
        return db_ops.update_bl_status(bl_id, new_status, id_BL_origine, date_BL_origine, supplier_name, supplier_id)
    except Exception as e:
        logging.error(f"Error in update_bl_status: {e}")
        return False


def update_bl_status_valider(bl_id: int, user_id: str, tenant_id: str):
    """
    Update BL status to 'VALIDER' in PostgreSQL database.
    """
    try:
        return db_ops.update_bl_status_valider(bl_id, user_id, tenant_id)
    except Exception as e:
        logging.error(f"Error in update_bl_status_valider: {e}")
        return False


def get_database_health():
    """
    Get health status of the PostgreSQL database.
    Returns a dictionary with the status of the PostgreSQL connection.
    """
    try:
        return db_ops.get_database_health()
    except Exception as e:
        logging.error(f"Error checking database health: {e}")
        return {'available': False, 'error': str(e)}