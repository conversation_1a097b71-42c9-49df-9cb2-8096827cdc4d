@import url("https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap");

* {
    font-family: "Poppins", sans-serif;
    text-align: center;
}

.loading-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.loading-content p {
    font-size: 16px;
    font-weight: 500;
    color: #333333;
    margin-bottom: 10px;
}

// svg loading animation 
::ng-deep svg.circles{
    width: 40px !important; 
    height: 40px !important; 
    filter: invert(29%) sepia(100%) saturate(3273%) hue-rotate(219deg) brightness(101%) contrast(100%);
    margin-bottom: 20px !important; 
}

.loading-content p span {
    font-size: 18px;
    font-weight: 700;
}

.head-progress-bar{
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}
.head-progress-bar p {
    font-size: 18px;
    font-weight: 700;
    color: #333333;
    margin-bottom: 10px;
}

ion-progress-bar::part(progress) {
    background: #0040ff;
}

ion-progress-bar {
    height: 10px;
    width: 70%;
    border-radius: 50px;
}

ion-spinner{
    width: 40px;
    height: 40px;
    color: #0040ff;
    margin-bottom: 10px;
}

p {
    text-align: center;
    font-size: 16px;
    font-weight: 500;
    color: #333333;
    margin-top: 10px;
  }

