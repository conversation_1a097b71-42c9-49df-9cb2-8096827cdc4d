import {
  Component,
  OnInit,
  inject,
  ElementRef,
  ViewChild,
  AfterViewInit,
  NgZone,
  HostListener,
  OnDestroy,
} from '@angular/core';
import {
  NavController,
  LoadingController,
  AlertController,
  IonFabButton,
  ModalController,
  ToastController,
} from '@ionic/angular';
import { ApiService } from '../services/api.service';
import { Camera, CameraResultType, CameraSource } from '@capacitor/camera';
import { Filesystem, Directory } from '@capacitor/filesystem';
import { SignalService } from '../services/signal.service';
import * as JSZip from 'jszip'; // Import jszip
import { PDFDocument, degrees } from 'pdf-lib';
import { environment } from 'src/environments/environment';
import { NetworkService } from '../services/network.service';
import { WebSocketService } from '../services/websocket.service';
import * as jpegJs from 'jpeg-js';
import * as ExifReader from 'exifreader';
import { Subject, takeUntil } from 'rxjs';
import { ScannerService } from '../services/scanner.service';
import { ScannerModalComponent } from '../components/scanner-modal/scanner-modal.component';
import { ProcessDocData } from 'src/models/ProcessDocData';

enum OcrMode {
  STANDARD = 'STANDARD',
  MINDEE_ADVANCED = 'MINDEE_ADVANCED'
}

@Component({
  selector: 'app-scan-bl',
  templateUrl: './scan-bl.page.html',
  styleUrls: ['./scan-bl.page.scss'],
})
export class ScanBLPage implements OnInit, AfterViewInit, OnDestroy  {
  activeButton: string = 'scan-bl';
  isConnected = true; // Track network status
  progress = 0;
  isLoading = false;
  isFabOpen = false;
  jobId: string | undefined;
  imagePreviewUrl: string | undefined;
  imageWidth: number | undefined;
  imageHeight: number | undefined;
  private isToggling: boolean = false;
  isProcessing = false;
  private readonly DEBOUNCE_TIME = 300; // milliseconds
  private lastClickTime = 0;

  readonly TOGGLE_DELAY = 300; // Match with CSS transition duration
  isAnimating = false;

  currentOcrMode: OcrMode = OcrMode.MINDEE_ADVANCED;
  // Expose OcrMode to the template
  readonly OcrMode = OcrMode;

  // Destroy subject for cleanup
  private destroy$ = new Subject<void>();

  isWeb: boolean = false;
  scannedImages: string[] = [];

  @ViewChild('fabButton', { static: false })
  fabButton!: ElementRef<HTMLIonFabButtonElement>;

  signalService = inject(SignalService);

  slidesData: ProcessDocData[] = [];
  signalDataIsEmpty = false;

  constructor(
    private navCtrl: NavController,
    private apiService: ApiService,
    private loadingController: LoadingController,
    private alertController: AlertController,
    private webSocketService: WebSocketService,
    private networkService: NetworkService,
    private modalController: ModalController,
    private scannerService: ScannerService,
    private toastController: ToastController, 
  ) {
    // this.jobId = this.apiService.generateJobId(); // Generate job ID once
    this.isWeb = environment.platform === 'web';
    console.log('isWeb:', this.isWeb);
    if (this.isWeb) {
      // Subscribe to scanner results
      this.scannerService.resultScan$
      .pipe(takeUntil(this.destroy$))
      .subscribe((scans) => {
        this.scannedImages = scans;
        this.openScannerModal(); // Call modal when scans are received
      });
    }
    this.loadOcrMode();

    // Initialize the forceSupplierGlobal to false
    localStorage.setItem('forceSupplierGlobal', 'false');
  }

  ionViewWillEnter() {
    this.resetStates();
    console.log('ScanBL ionViewWillEnter called');
    this.initializeComponent();
  }

  ionViewWillLeave() {
    this.resetStates();
  }

  private resetStates() {
    this.isFabOpen = false;
    this.isLoading = false;
    this.isProcessing = false;
    this.isToggling = false;
    this.lastClickTime = 0;
  }

  @HostListener('document:click', ['$event'])
  handleClickOutside(event: Event) {
    const fabElement = this.fabButton?.nativeElement;
    const clickedElement = event.target as HTMLElement;
    
    if (this.isFabOpen && fabElement && !fabElement.contains(clickedElement)) {
      // Check if click was outside the FAB and its children
      if (!clickedElement.closest('ion-fab')) {
        this.onFabToggle();
      }
    }
  }

  ngOnInit() {
    console.log('ScanBL ngOnInit called');
    this.initializeComponent();
  }


  private initializeComponent() {
    // Subscribe to the network status
    this.networkService.getNetworkStatus().subscribe((connected: boolean) => {
      this.isConnected = connected;
    });

    this.resetStates();

    console.log("this.signalService.getData():", this.signalService.getData());

    // Check and update signal data
    const currentData = this.signalService.getData();
    if (currentData.length === 0) {
      this.signalDataIsEmpty = true;
      localStorage.removeItem('forceSupplierGlobal');
    } else {
      this.slidesData = currentData;
      this.signalDataIsEmpty = false;
      console.log('Slides data:', this.slidesData);
    }

    this.loadOcrMode();
    
    // Check if the data is empty then remove the localStorage of forceSupplierGlobal
    this.signalService.checkDataisEmpty();
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
    this.scannerService.close();
  }

  // eslint-disable-next-line @angular-eslint/no-empty-lifecycle-method
  ngAfterViewInit() { }

  
  async openScannerModal() {
    const modal = await this.modalController.create({
      component: ScannerModalComponent,
      componentProps: {
        scans: this.scannedImages,
      },
      cssClass: 'scanner-modal'
    });

    await modal.present();

    const { data } = await modal.onWillDismiss();
    if (data?.selectedImages) {
      // Process selected images
      for (const scan of data.selectedImages) {
        const imageData = `data:image/jpeg;base64,${scan}`;
        const response = await fetch(imageData);
        const blob = await response.blob();
        const file = new File([blob], `scanned_image_${Date.now()}.jpg`, {
          type: 'image/jpeg'
        });
        
        await this.sendImageToBackend(file, true);
      }
    }
  }

  // Load OCR mode from localStorage
  // Update load method
  private loadOcrMode() {
    const savedMode = localStorage.getItem('ocrMode');
    this.currentOcrMode = ( savedMode as OcrMode ) || OcrMode.MINDEE_ADVANCED; // Default if not set
  }

  // Update OCR mode change handler
  setOcrMode(mode: 'STANDARD' | 'MINDEE_ADVANCED') {
    this.currentOcrMode = mode as OcrMode;
    localStorage.setItem('ocrMode', mode);
  }

  // Helper method to check current mode
  isMindeeMode(): boolean {
    return this.currentOcrMode === OcrMode.MINDEE_ADVANCED;
  }

  // onFabToggle() {
  //   // Add debounce to prevent rapid toggling
  //   if (this.isToggling) return;

  //   this.isToggling = true;
  //   this.isFabOpen = !this.isFabOpen;

  //   setTimeout(() => {
  //     this.isToggling = false;
  //   }, 300); // Match transition duration
  // }

  async onFabToggle() {
    // Prevent toggle while animating
    if (this.isAnimating) {
      return;
    }
  
    this.isAnimating = true;
    this.isFabOpen = !this.isFabOpen;
  
    // Reset animation lock after transition completes
    setTimeout(() => {
      this.isAnimating = false;
    }, this.TOGGLE_DELAY);
  }

  triggerFabButtonClick() {
    try {
      if (this.fabButton?.nativeElement) {
        this.fabButton.nativeElement.click();
      }
    } catch (err) {
      console.error('Error triggering fab button:', err);
      // Fallback behavior
      this.isFabOpen = !this.isFabOpen;
    }
  }

  async startScanning() {
    const loading = await this.showLoading();
    
    try {
      const canScan = await this.scannerService.launchScan();
      await loading.dismiss();
      
      if (!canScan) {
        await this.showErrorToast();
      }
    } catch (error) {
      await loading.dismiss();
      await this.showErrorToast();
    }
  }

  private async showLoading() {
    const loading = await this.loadingController.create({
      message: 'Tentative de connexion au scanner...',
      spinner: 'circular',
      translucent: true,
      cssClass: 'custom-loading',
      duration: 10000 // Will auto-dismiss after 10 seconds
    });
    await loading.present();
    return loading;
  }

  private async showErrorToast() {
    const toast = await this.toastController.create({
      message: "L'application PostAgent n'est pas ouverte. Merci de l'ouvrir avant de prendre une photo.",
      duration: 3000,
      position: 'top',
      color: 'warning',
      buttons: [
        {
          text: 'OK',
          role: 'cancel'
        }
      ]
    });
    await toast.present();
  }

  async openCamera() {
    this.onFabToggle();

    if (this.isWeb) {
      // Launch scanner for web
      await this.startScanning();
    } else {
      try {
        // Logic to open the camera and capture an image
        const image = await Camera.getPhoto({
          quality: 100,
          allowEditing: false,
          resultType: CameraResultType.Uri,
          source: CameraSource.Camera,
        });

        if (image.webPath) {
          const response = await fetch(image.webPath);
          const blob = await response.blob();
          const file = new File([blob], 'captured-image.jpg', {
            type: 'image/jpeg',
          });

          // Create an HTML image element to load the image and get its dimensions
          const img = new Image();
          this.imageWidth = img.width;
          this.imageHeight = img.height;
          img.src = image.webPath;
          img.onload = async () => {
            const size = { width: img.height, height: img.width };
            const isScanner = this.isWeb
            await this.sendImageToBackend(file, isScanner);
          };
        }
      } catch (error) {
        console.log(error);
        // Handle error appropriately
      }
    }
  }

  verfifySelectedImageBlobType(blob: Blob) {
    //  should start with image/*
    const type = blob.type.split('/')[0];
    if(type !== 'image') {
      this.alertController.create({
        animated: true,
        header: 'Erreur',
        message: 'Veuillez sélectionner une image valide',
        buttons: ['OK']
      }).then(alert => alert.present());
      return false;
    }
    return true;
  }

  async openGallery() {
    this.onFabToggle();
    // Logic to open the gallery and select an image
    try {
      const image = await Camera.getPhoto({
        quality: 100,
        allowEditing: false,
        resultType: CameraResultType.Uri,
        source: CameraSource.Photos,
      });
  
      if (image.webPath) {
        // this.imagePreviewUrl = image.webPath; // Set the image preview URL
        const response = await fetch(image.webPath);
        const blob = await response.blob();
  
        if(!this.verfifySelectedImageBlobType(blob)) {
          return;
        }
  
        const file = new File([blob], `selected-image.${image.format}`, {
          type: image.format ? `image/${image.format}` : 'image/jpeg'
        });
  
        // Create an HTML image element to load the image and get its dimensions
        const img = new Image();
        img.src = image.webPath;
        img.onload = async () => {
          this.imageWidth = img.width;
          this.imageHeight = img.height;
          const size = { width: img.height, height: img.width };
          await this.sendImageToBackend(file, this.isWeb);
        };
      }
    } catch (error) {
      console.log("getPhoto Error : ",(error as unknown as Error)?.message);
    }
  }

  async zipImageFile(file: File): Promise<File> {
    const zip = new JSZip();
    zip.file(file.name, file);
    const zipBlob = await zip.generateAsync({ type: 'blob' });
    return new File([zipBlob], 'image.zip', { type: 'application/zip' });
  }

  async convertImageToPDF(file: File): Promise<File> {
    try {
      const pdfDoc = await PDFDocument.create();
      const jpgImageBytes = await file.arrayBuffer();
      const jpgImage = await pdfDoc.embedJpg(jpgImageBytes);

      // Read EXIF data to determine the correct orientation
      const exifData = ExifReader.load(jpgImageBytes);
      const orientation = exifData.Orientation ? exifData.Orientation.value : 1;

      // Use image dimensions
      // const width = this.imageWidth ?? 0;
      // const height = this.imageHeight ?? 0;
      const { width, height } = jpgImage.size();
      console.log('Image dimensions jpgImage.size();:', width, height);

      // Determine the page size
      let pageWidth = width;
      let pageHeight = height;

      // Adjust page size based on rotation
      if (orientation === 6 || orientation === 8) {
        pageWidth = height;
        pageHeight = width;
      }

      // Add a new page with the image dimensions based on its orientation
      const page = pdfDoc.addPage([pageWidth, pageHeight]);

      let rotateAngle = degrees(0);
      let x = 0;
      let y = 0;

      console.log('Orientation:', orientation);
      console.log('Page dimensions Edited Oriented:', pageWidth, pageHeight);

      // Adjust rotation and coordinates based on EXIF orientation
      switch (orientation) {
        case 6: // Rotate 90 degrees
          rotateAngle = degrees(270);
          x = 0;
          y = pageHeight;
          break;
        case 3: // Rotate 180 degrees
          rotateAngle = degrees(180);
          x = -pageWidth;
          y = -pageHeight;
          break;
        case 8: // Rotate 270 degrees
          rotateAngle = degrees(90);
          x = pageWidth;
          y = 0;
          break;
        default:
          rotateAngle = degrees(0);
          x = 0;
          y = 0;
      }

      // Draw the image on the page with the correct rotation
      page.drawImage(jpgImage, {
        x: x,
        y: y,
        width: width,
        height: height,
        rotate: rotateAngle,

      });

      const pdfBytes = await pdfDoc.save();
      return new File([pdfBytes], 'document.pdf', { type: 'application/pdf' });
    } catch (error) {
      console.error('Error converting image to PDF:', error);
      const errorMessage = `
      <h3>Erreur lors de la reconnaissance du document</h3>
      <ul>
        <li>Verifier que l'image est un document</li>
        <li>Verifier la qualité de l'image</li>
        <li>Supprimer les objets inutiles dans l'image</li>
      </ul>
      
      `;
      this.apiService.showErrorAlert(errorMessage);
      this.isLoading = false;
      this.webSocketService.close(this.jobId!);
      this.navCtrl.navigateRoot('/request-error');

      throw new Error('Failed to convert image to PDF');
    }
  }

  async sendImageToBackend(file: File, isScanner: boolean = false) {
    this.jobId = this.apiService.generateJobId(); // Generate job ID
    const websocketUrl = `${environment.webSocketUrl}/${this.jobId}`;
    this.webSocketService.connect(websocketUrl, this.jobId);
    console.log('WebSocket URL scan-bl-list:', websocketUrl);

    this.webSocketService.onMessage(this.jobId).subscribe((message) => {
      if (message.progress !== undefined) {
        this.progress = message.progress;
        console.log('progress __ :', this.progress);
      }
    });

    // const loading = await this.presentLoading(); // Show loading spinner
    this.isLoading = true;
    // const zipFile = await this.zipImageFile(file);
    // const pdfFile = await this.convertImageToPDF(file);
    const that = this;

    // --- show the pdf file in a new tab
    // const pdfUrl = URL.createObjectURL(pdfFile);
    // window.open(pdfUrl, '_blank');

    // this.apiService.smartCrop(zipFile).subscribe((response) => {
    // this.apiService.smartCrop(file).subscribe((response) => {
    this.apiService.smartCrop(file, this.jobId, isScanner).subscribe(
      (response) => {
        // loading.dismiss();
        this.isLoading = false;
        const coordinates = response.coordinates;
        const uuid = response.uuid;
        const needs_rotation = response.needs_rotation;
        console.log(response);
        this.navCtrl.navigateForward('/crop-doc', {
          state: {
            imageUrl: URL.createObjectURL(file),
            coordinates: coordinates,
            uuid: uuid,
            needs_rotation: needs_rotation,
          },
        });

        // Disconnect WebSocket after completion
        this.webSocketService.close(this.jobId!);
      },
      (error) => {
        // loading.dismiss();
        this.isLoading = false;

        if (error.status === 400) {
            let errorMessage = `
            <h3>Erreur lors de la reconnaissance du document</h3>
            `;
    
            if (error.error && Array.isArray(error.error.user_messages)) {
                errorMessage += '<ul>';
                for (const message of error.error.user_messages) {
                    errorMessage += `<li>${message}</li>`;
                }
                errorMessage += '</ul>';
            } else {
                errorMessage += `
                <ul>
                  <li>Une erreur inattendue s'est produite.</li>
                </ul>`;
            }
    
            this.apiService.showErrorAlert(errorMessage);
            console.error('API error:', JSON.stringify(error));
        } else {
            const errorMessage = `
            <h3>Erreur lors de la reconnaissance du document</h3>
            <ul>
              <li>Vérifier que l'image est un document</li>
              <li>Vérifier la qualité de l'image</li>
              <li>Supprimer les objets inutiles dans l'image</li>
            </ul>
            `;
            this.apiService.showErrorAlert(errorMessage);
            console.error('API error:', JSON.stringify(error));
        }

        // Disconnect WebSocket after completion
        this.webSocketService.close(this.jobId!);

        this.navCtrl.navigateRoot('/request-error');
      }
    );

    this.webSocketService.onMessage(this.jobId).subscribe((message) => {
      if (message.progress !== undefined) {
        this.progress = message.progress;
      }
    });
  }

  setActive(buttonName: string) {
    this.activeButton = buttonName;
  }

  doc_list() {
    this.navCtrl.navigateRoot('/doc-list'); 
  }

  async presentLoading() {
    const loading = await this.loadingController.create({
      message: 'Chargement...',
      spinner: 'circles',
      // duration: 30000 // Optional: specify a timeout for the loading spinner
    });
    await loading.present();
    return loading;
  }

  async showInfoAlert(message: string) {
    const alert = await this.alertController.create({
      header: 'Attention !',
      message: message,
      buttons: ['OK'],
    });
    await alert.present();
  }

  async navToProcessDoc() {
    if (this.signalService.getData().length > 0) {
      // Prevent multiple rapid clicks
      const currentTime = new Date().getTime();
      if (currentTime - this.lastClickTime < this.DEBOUNCE_TIME) {
        return;
      }
      this.lastClickTime = currentTime;

      // Prevent processing if already in progress
      if (this.isProcessing) {
        return;
      }

      try {
        this.isProcessing = true;

        if (this.signalService.getData().length > 0) {
          // Reset states
          this.isFabOpen = false;
          this.isLoading = false;

          // Add a small delay to ensure UI updates
          await new Promise((resolve) => setTimeout(resolve, 100));

          // Navigate to process doc page
          await this.navCtrl.navigateForward('/process-doc');
        } else {
          // Show options for adding new document
          await this.showDocumentOptions();
        }
      } catch (error) {
        console.error('Navigation error:', error);
      } finally {
        this.isProcessing = false;
      }
    } else {
      this.showInfoAlert('Veuillez scanner un document avant de continuer.');
    }
  }

  private async showDocumentOptions() {
    const alert = await this.alertController.create({
      header: 'Ajouter un document',
      buttons: [
        {
          text: 'Scanner',
          handler: () => {
            this.openCamera();
          }
        },
        {
          text: 'Galerie',
          handler: () => {
            this.openGallery();
          }
        },
        {
          text: 'Annuler',
          role: 'cancel'
        }
      ]
    });

    await alert.present();
  }

  async confirmDeleteCard(index: number) {
    const alert = await this.alertController.create({
      header: 'Supprimer le document',
      message: `Êtes-vous sûr de vouloir supprimer ce document ?`,
      buttons: [
        {
          text: 'Annuler',
          role: 'cancel',
          cssClass: 'custom-alert-button cancel',
          handler: () => {
            console.log('Confirm Cancel');
          },
        },
        {
          text: 'Oui, Supprimer !',
          cssClass: 'custom-alert-button danger',
          handler: () => {
            if (this.slidesData.length == 0) {
              localStorage.removeItem('selectedSupplier');
              this.navCtrl.navigateRoot('/scan-bl');
            }
          },
        },
      ],
    });

    await alert.present();
  }



  async logout() {
    await this.apiService.logout();  // Wait for the confirmation dialog
    this.navCtrl.navigateRoot('/login');  // Then navigate to login
  }
}
