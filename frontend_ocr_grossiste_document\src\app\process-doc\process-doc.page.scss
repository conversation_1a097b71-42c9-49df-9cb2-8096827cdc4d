@import url("https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap");

* {
  font-family: "Inter", sans-serif;
  font-optical-sizing: auto;
}

ion-content::part(scroll) {
  overflow-y: hidden !important;
  --overflow: hidden !important;
}

ion-header {
  height: 80px;
  --border: 0;
}
ion-header ion-toolbar {
  height: 100%;
  --border: 0;
  --border-width: 0;
  display: flex;
  justify-content: center;
  align-items: flex-end;
  flex-direction: row;
}

ion-header ion-toolbar ion-title {
  font-size: 26px;
  font-weight: 700;
  color: #2f4fcd;
  text-align: left;
  width: 100%;
  padding-left: 2rem;
}

::ng-deep ion-header ion-toolbar app-custom-icon img {
  width: 40px !important;
  height: 40px !important;
  margin-right: 5px !important;
}

.process-doc-content{
  background: url("/assets/bg-scan-bl.png") no-repeat center center fixed;
  background-size: cover;
}

.process-doc-wrapper {
  width: 100%;
  display: flex;
  flex-direction: column;
  // align-items: stretch;
  // justify-content: center;
  align-items: center;
}

// select 
.process-doc-content .select-supplier-wrapper{
  margin-top: 1rem;
  margin-bottom: 1rem;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-grow: 1;
  ion-select{
    width: 60%;
    padding: 0 10px;
    background: #fff;
    border-radius: 10px;
    box-shadow: 0px 6px 10px rgb(0, 0, 0, 0.2); /* Add shadow */
  }

  ion-select::part(text){
    text-align: left;
  }
}
.process-doc-wrapper .image-container{
  flex-grow: 8;
  margin-top: 1rem;

}

::ng-deep ion-alert button[aria-checked="true"] .alert-button-inner{
  background-color: rgba(49, 81, 207, 0.8);
  border-bottom: 4px solid #E98862;
  .alert-radio-label{
    color: #fff !important;
  }
  .alert-radio-icon .alert-radio-inner{
    border-color: #fff !important;
  }
}

::ng-deep ion-select{
  --border-width: 0px !important;
}

::ng-deep .process-doc-wrapper .file-import-icon img {
  width: 150px !important;
  height: 150px !important;
}
::ng-deep .process-doc-wrapper .arrow-bottom-icon {
  // margin-top: 50px;
  img {
    width: 100px !important;
    height: 100px !important;
  }
}

.process-doc-wrapper .content {
  padding: 10px 50px 0 50px;
}
.process-doc-wrapper .content h2 {
  color: #9a9a9a;
  font-size: 22px;
  font-weight: 700;
}

.process-doc-wrapper .content p {
  color: #9a9a9a;
  font-size: 12px;
  text-align: justify;
  padding: 5px 10px;
}

.process-doc-content {
  --offset-top : -10px !important;
  --background: rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.document-icon {
  font-size: 100px;
  color: #c4c4c4;
}

h2 {
  color: #555555;
  font-size: 18px;
  margin-top: 20px;
}

p {
  color: #888888;
  font-size: 14px;
  margin-top: 10px;
  margin-bottom: 30px;
}

.arrow-icon {
  font-size: 30px;
  color: #3b82f6; // Adjust the color as needed
}

ion-footer {
  background-color: #e5e7eb; // Adjust the background color as needed
}

ion-footer ion-toolbar ion-buttons,
ion-footer ion-toolbar ion-buttons ion-button {
  display: flex;
  justify-content: space-evenly;
  align-items: center;
  flex-direction: row;
}

::ng-deep ion-button.menu-button app-custom-icon img {
  width: 30px !important;
  height: 30px !important;
  color: #000;
}

::ng-deep .menu-button.active app-custom-icon img {
  color: #2f4fcd;
}

::ng-deep .menu-button-middle {
  background-color: #2f4fcd;
  padding: 2px 12px;
  border-radius: 14px;
  width: 160px;
  height: 60px;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: row;
  margin-bottom: 15px;
  app-custom-icon img {
    width: 35px !important;
    height: 35px !important;
    color: #fff;
  }
  span{
    color: #fff;
    font-weight: 500;
    font-size: 16px;
    padding-left: 10px;
  }
}


ion-toolbar {
  --background: transparent;
  --ion-color-primary: #3b82f6; // Adjust the color as needed
}

ion-icon {
  font-size: 24px;
}

ion-footer {
  position: relative;
  background-color: #dddbff;
  height: 110px;
  // border-top: 1px solid #2563eb;
  width: 100%;

  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: row;
}

ion-footer ion-toolbar {
  --border-width: 0;
}

ion-fab {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}

::ng-deep ion-fab-button.menu-button-middle::part(native) {
  background: none;
  border: 0;
  box-shadow: none;
  width: 100% !important;
  color: #fff;
}

::ng-deep ion-fab ion-fab-list {
  display: flex;
  flex-direction: row !important;
  justify-content: space-around;
  align-items: flex-end;
  width: auto !important;
  // padding: 10px 0;
  padding: 10px 20px;
  margin-bottom: 100px;
  height: 100vh;
  transition: all 0.3s ease;
  &.fab-active {
    // background-color: rgba(0, 0, 0, 0.2);
    visibility: visible;
    opacity: 1;
    pointer-events: all;
  }

  &.fab-hidden {
    visibility: hidden;
    opacity: 0;
    pointer-events: none;
  }
}

.bg-hide{
  background-color: rgba(0, 0, 0, 0.1);
}
.initial-bg{
  --background: none;
}

.content-fab-buttom {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}
::ng-deep .content-fab-buttom app-custom-icon img {
  width: 50px !important;
  height: 50px !important;
}
::ng-deep .content-fab-buttom ion-label {
  color: #2f4fcd;
  font-size: 20px;
}

::ng-deep ion-fab ion-fab-list ion-fab-button {
  padding: 10px;
  width: 200px;
  height: 135px;
  transform: translateY(40px);
  transition: all 0.6s ease-in-out;
  &.fab-active {
    transform: translateY(0);
  }
}
::ng-deep ion-fab ion-fab-list ion-fab-button::part(native) {
  border-radius: 16px !important;
}

/***  Swiper  ***/

.swiper {
  height: 60vh;

  &.swiper-large {
    width: 100%;
  }

  &.swiper-small {
    width: 25%;
  }
}

/* General Swiper Slide Styling */
.swiper .swiper-slide {
  background-position: center;
  background-size: cover;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

// # delete botton cercle
.swiper .swiper-slide.swiper-slide-active .process-doc-wrapper{
  position: relative;
  .remove-doc{
    position: absolute;
    border-radius: 50%;
    top: 0;
    left: 0;
    transition: all 0.3s ease;
    z-index: 1;
    // pointer-events: none;
    cursor: pointer;
    font-weight: bold;
  }
  .remove-doc ion-icon{
    font-size: 35px;
    color: #1e2531;
  }

}

::ng-deep .swiper .swiper-slide-active .swiper-slide-shadow,
::ng-deep .swiper .swiper-slide-active .swiper-slide-shadow-left,
::ng-deep .swiper .swiper-slide-active .swiper-slide-shadow-right {
  background: none !important;
}

::ng-deep .swiper .swiper-slide-active .process-doc-wrapper {
  width: 85%;

}

::ng-deep .swiper .swiper-slide.swiper-slide-active {
  transform: translate3d(0px, 0px, 0) rotateX(0deg) rotateY(0) scale(1) !important;
  .image-container{
    box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.2);
  }
}
::ng-deep .swiper .swiper-slide:not(.swiper-slide-active) {
  transform: translate3d(0px, 0px, 0) rotateX(0deg) rotateY(0) scale(0.7) !important;
  top: 20px;
  .image-container{
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.8);
  }
}

::ng-deep .swiper .swiper-slide.swiper-slide-prev {
  left: 110px;
}
::ng-deep .swiper .swiper-slide.swiper-slide-next {
  right: 110px;
}

::ng-deep .swiper .swiper-slide:not(.swiper-slide-active) .swiper-slide-shadow,
::ng-deep .swiper .swiper-slide:not(.swiper-slide-active) .swiper-slide-shadow-left,
::ng-deep .swiper .swiper-slide:not(.swiper-slide-active) .swiper-slide-shadow-right {
  background: none !important;
}

::ng-deep .swiper .swiper-slide.last-swiper-slide-scan{
  height: auto;
  margin-top: 1rem;
  .scan-bl-wrapper{
    height: 100%;
    width: 80%;
    margin-left: 1rem;
    // border-collapse: separate;
    // border-spacing: 50px;
    // border-style: dashed;
    // border-radius: 20px;
    // border: 2px dotted #555555;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    background-image: url("data:image/svg+xml,%3csvg width='100%25' height='100%25' xmlns='http://www.w3.org/2000/svg'%3e%3crect width='100%25' height='100%25' fill='none' rx='30' ry='30' stroke='%23333' stroke-width='2' stroke-dasharray='10%2c 14' stroke-dashoffset='0' stroke-linecap='square'/%3e%3c/svg%3e");
border-radius: 30px;
  }
  
}

::ng-deep .scan-bl-wrapper .file-import-icon img {
  width: 150px !important;
  height: 150px !important;
}
::ng-deep .scan-bl-wrapper .arrow-bottom-icon {
  // margin-top: 50px;
  img {
    width: 100px !important;
    height: 100px !important;
  }
}

.scan-bl-wrapper .content {
  padding: 10px 10px 0 10px;
}
.scan-bl-wrapper .content h2 {
  color: #9a9a9a;
  font-size: 22px;
  font-weight: 700;
}

.scan-bl-wrapper .content p {
  color: #9a9a9a;
  font-size: 12px;
  text-align: justify;
  padding: 5px 10px;
  text-align: center;
}

// / *** Styles alert dialog ***/
::ng-deep .custom-alert-button, custom-alert-button-rename-doc {
  display: inline-block;
  text-align: center;
  font-size: 14px !important;
  font-weight: bold;
}

::ng-deep .custom-alert-button.cancel, ::ng-deep .custom-alert-button-rename-doc.cancel {
  color: #2563eb;
  width: 48%;
}
::ng-deep .custom-alert-button-rename-doc.cancel {
  font-size: 16px;
}
::ng-deep .custom-alert-button.danger {
  color: red;
  width: 50%;
}
::ng-deep .custom-alert-button.warning {
  color: #f6ad55;
  font-size: 13px;
  width: 50%;
}
::ng-deep .custom-alert-button-rename-doc.rename {
  font-size: 16px;
  color: #535353;
}

// rotate

// .process-doc-wrapper {
//   display: flex;
//   justify-content: center;
//   align-items: center;
//   height: 100%;
// }

// #cropper {
//   display: none;
//   position: absolute;
//   top: 0;
//   left: 0;
//   right: 0;
//   bottom: 0;
// }

// image-cropper {
//   width: 100%;
//   height: 100%;
//   transition: transform 0.3s ease;
// }


/* **** Filter Image Scan **** */

.image-container {
    position: relative;
    display: inline-block;
    width: 90%;
  }
  
  #unfilteredImage {
    width: 100%;
    display: block;
  }
  
  .filter-animation {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    // height: 100%;
    overflow: hidden;
    pointer-events: none;
  }
  
  .filter-animation img {
    width: 100%;
    height: 100%;
    display: none;
  }
  
  .filter-animation.d-block-img img {
    display: block;
  }
  
  @keyframes scanEffect {
    from {
      clip-path: inset(0 0 100% 0);
    }
    to {
      clip-path: inset(0 0 0 0);
    }
  }
  
  .scanning {
    animation: scanEffect 2s forwards;
  }
  
  .scanning-active {
    box-shadow: 0px 0px 20px 10px rgba(0, 128, 0, 0.5);
  }
  
  .scan-line {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background-color: #63D0C5;
    box-shadow: 0 0 70px 30px rgba(0, 128, 0, 0.9);
    animation: scanLineMove 2s forwards;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .scan-line::before {
    content: '◥◤';
    font-size: 15px;
    color: green;
    margin-left: -8px;
    margin-bottom: 28px;
    transform: rotate(-90deg);
  }
  
  .scan-line::after {
    content: '◥◤';
    font-size: 15px;
    color: green;
    margin-right: -8px;
    margin-bottom: 28px;
    transform: rotate(90deg);
  }
  
  .hide-animation{
      .scan-line{
          display: none;
      }
  }
  

  .loading-spinner {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 10;
  }
  
  .hidden {
    visibility: hidden;
  }
  
  .image-container {
    position: relative;
    width: 100%;
    height: 100%;
  }

  
  @keyframes scanLineMove {
    from {
      top: 0;
    }
    to {
      top: 100%;
    }
  }
  

@media (prefers-color-scheme: dark) {
  .process-doc-content{
    background: url("/assets/bg-scan-bl.png") no-repeat center center fixed;
    background-size: cover;
  }
  ion-content {
    --background: #fff
  }
  ion-header ion-toolbar {
    --background: #fff !important;
  }
}

@media (prefers-color-scheme: light) {
  .process-doc-content{
    background: url("/assets/bg-scan-bl.png") no-repeat center center fixed;
    background-size: cover;
  }
  ion-content {
    --background: #fff
  }
  ion-header ion-toolbar {
    --background: #fff !important;
  }
}





