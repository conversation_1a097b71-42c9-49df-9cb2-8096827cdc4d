import { Component, OnInit } from '@angular/core';
import { WebSocketService } from '../services/websocket.service';
import { HttpClient, HttpHeaders, HttpErrorResponse } from '@angular/common/http';
import { catchError, tap } from 'rxjs/operators';
import { Observable, throwError  } from 'rxjs';

@Component({
  selector: 'app-home',
  templateUrl: 'home.page.html',
  styleUrls: ['home.page.scss'],
})
export class HomePage implements OnInit {
  progress = 0;

  constructor(private webSocketService: WebSocketService, private http: HttpClient) {}

  ngOnInit() {
    this.sendImageToBackend();
  }

  async sendImageToBackend() {
    const clientId = 'client_' + Math.random().toString(36).substr(2, 9);
    const websocketUrl = `wss://ocr-api.abdohero.com/ws/${clientId}`;
    this.webSocketService.connect(websocketUrl, clientId);

    this.webSocketService.onMessage(clientId).subscribe((message) => {
      if (message.progress !== undefined) {
        this.progress = message.progress;
        console.log('Progress:', this.progress);
      }
    });

    this.http.post<any>(`https://ocr-api.abdohero.com/test_progress/${clientId}`, {}).subscribe(
      (response) => console.log('HTTP POST Response:', response),
      (error) => console.error('HTTP POST Error:', error)
    );
  }
}
