import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { RouteReuseStrategy } from '@angular/router';

import { IonicModule, IonicRouteStrategy } from '@ionic/angular';

import { AppComponent } from './app.component';
import { AppRoutingModule } from './app-routing.module';

import { defineCustomElements } from '@ionic/pwa-elements/loader';
import { DomSanitizer } from '@angular/platform-browser';
import { IonicStorageModule } from '@ionic/storage-angular';
import { register } from 'swiper/element/bundle';
import { HttpClient, HttpClientModule } from '@angular/common/http';
import { HTTP_INTERCEPTORS } from '@angular/common/http';
import { HttpErrorInterceptor } from './interceptors/http-error.service';
import { NetworkService } from './services/network.service';
import { AuthInterceptor } from './interceptors/auth.interceptor';
import { ScannerModalComponent } from './components/scanner-modal/scanner-modal.component';


@NgModule({
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  declarations: [AppComponent],
  imports: [
    BrowserModule,
    IonicModule.forRoot( { mode: 'ios', innerHTMLTemplatesEnabled: true }),
    AppRoutingModule,
    IonicStorageModule.forRoot(),
    HttpClientModule
  ],
  providers: [
    { provide: RouteReuseStrategy, useClass: IonicRouteStrategy },
    { provide: HTTP_INTERCEPTORS, useClass: AuthInterceptor, multi: true, },
    { provide: HTTP_INTERCEPTORS, useClass: HttpErrorInterceptor, multi: true },
    NetworkService,
    {
      provide: 'DARK_MODE',
      useValue: false
    }
  ],
  bootstrap: [AppComponent],
})
export class AppModule {
  constructor(private sanitizer: DomSanitizer) {
    defineCustomElements(window);
    // register();
  }
}
