import json
from datetime import datetime
from src.app.utils import constants
from collections import OrderedDict
from src.app.utils.document_model import DocumentModel
from fuzzywuzzy import process, fuzz
import logging
import traceback
from src.app.utils.helpers import *
import re

"""-----******************************    Correct Additional Info in BL    ******************************-----"""
""" ------------------------------------------------------------------------------------------------------------------ """


def is_additional_line(line, terms):
    try:
        words = line.split()
        first_word = correct_number(words[0])
        first_word_contain_number = contains_number(words[0])

        if not words:
            return False

        # Perform fuzzy matching with STABLE_FAMILLE_TARIFAIRE_TVA and terms
        match_count = 0
        for word in words:
            if fuzzywazzy_part_with_array_parameter(word, terms, 80):
                match_count += 1
            if match_count >= 3:
                return True

        # Check if the first word is a number
        if words[0].isdigit():
            return False

        # Check if the line contains a number less than 3 characters long
        if any(first_word_contain_number and len(words[0]) < 3 for word in words):
            return False

        # Check if the line contains any word from STABLE_FAMILLE_TARIFAIRE_TVA
        if not (check_presence(constants.STABLE_FAMILLE_TARIFAIRE_TVA, word) for word in words):
            return False

        # Check if the line contains any word from CORRECT_ADDITIONAL_BL
        if not (check_presence(terms, word) for word in words):
            return False

        # # Perform fuzzy matching with STABLE_FAMILLE_TARIFAIRE_TVA and terms
        for word in words:
            if fuzzywazzy_part_with_array_parameter(word, constants.STABLE_FAMILLE_TARIFAIRE_TVA, 80) and '%' in word:
                return True

        if not fuzzywazzy_part_with_array_parameter(words[0], terms, 80):
            return False

        return True

    except (ValueError, TypeError) as e:
        logging.error(f"Error in is_additional_line: {traceback.format_exc()}")
        raise e


def correct_additional_line(line, terms, percentage_terms, percentage_tva):
    try:
        line = line.replace(',', '.')
        line = line.replace('. ', '.')

        words = line.split()
        corrected_words = []

        for word in words:
            # Apply fuzzy matching for terms correction
            best_match_score = 0
            best_match = None
            if terms:
                best_match, match_score = process.extractOne(word, terms, scorer=fuzz.ratio)
                best_match_score = match_score if match_score is not None else 0
            if best_match_score >= percentage_terms:
                corrected_words.append(best_match)
            else:
                # Check if the word contains '%' and correct the number if needed
                if any(char.isdigit() for char in word) or '%' in word:
                    # Apply fuzzy matching for STABLE_FAMILLE_TARIFAIRE_TVA
                    best_match_tva, match_score_tva = process.extractOne(word, constants.STABLE_FAMILLE_TARIFAIRE_TVA,
                                                                         scorer=fuzz.ratio)

                    if match_score_tva >= percentage_tva:
                        corrected_words.append(best_match_tva)
                    else:
                        corrected_words.append(word)

                else:
                    corrected_words.append(word)
        return ' '.join(corrected_words)

    except (ValueError, TypeError) as e:
        logging.error(f"Error in correct_additional_line: {traceback.format_exc()}")
        raise e


def process_model_text_additional_section(model, text_lines):
    try:
        terms = constants.CORRECT_ADDITIONAL_BL.get(model, [])
        corrected_lines = []
        is_additional_section = False
        additional_section_start_index = None

        for index, line in enumerate(text_lines):
            # Check if the line contains any of the terms

            if additional_section_start_index is None:
                if is_additional_line(line, terms):
                    additional_section_start_index = index
            if additional_section_start_index:
                corrected_line = correct_additional_line(line, terms, 80, 70)
                corrected_lines.append(corrected_line)
            else:
                corrected_lines.append(line)

        return corrected_lines, additional_section_start_index

    except (ValueError, TypeError) as e:
        logging.error(f"Error in process_model_text_additional_section: {traceback.format_exc()}")
        raise e


"""-----****************************** Correct Numbers (Qty, PPV, PU, TTC, ...) ******************************-----"""
""" ------------------------------------------------------------------------------------------------------------------ """


def correct_chiffres_calculated(data_to_loop, model, additional_section_start_index):
    try:
        attribute_indexes = {
            'quantity': [],
            'quantity_ordered': [],    # Added for SPR
            'quantity_delivered': [],  # Added for SPR
            'quantity_duplicate': [],  # Added for other models
            'forme_galenique': [],
            'designation': [],
            'ppv': [],
            'pph': [],
            'total_ttc': [],
            'tva': [],
            'num_lot': [],
            'date_per': [],
            'code_produit': [],
            'word_index': [],
            'gf': []
        }

        if additional_section_start_index is None:
            check_to_index = len(data_to_loop)
        else:
            check_to_index = additional_section_start_index

        if model == constants.SPR:
            data_to_loop = clean_chars_ocr_lines_last_parts(data_to_loop, 3, additional_section_start_index)
        elif model == constants.COOPER_PHARMA_CASA:
            data_to_loop = clean_chars_ocr_lines_last_parts(data_to_loop, 7, additional_section_start_index)
        elif model == constants.GPM:
            data_to_loop = clean_chars_ocr_lines_last_parts(data_to_loop, 3, additional_section_start_index)
        elif model == constants.SOPHADIMS or model == constants.RECAMED:
            data_to_loop = clean_chars_ocr_lines_last_parts(data_to_loop, 2, additional_section_start_index)
        # elif model == constants.SOPHACA:
        #     data_to_loop = clean_chars_ocr_lines_last_parts(data_to_loop, 3)

        def clean_numeric_field(value):
            # Remove leading dots, dashes, or spaces
            cleaned = re.sub(r'^[.\-\s]+', '', value)
            # Remove trailing dots or spaces
            cleaned = re.sub(r'[.\s]+$', '', cleaned)
            # Ensure there's only one decimal point
            parts = cleaned.split('.')
            if len(parts) > 2:
                cleaned = parts[0] + '.' + ''.join(parts[1:])
            return cleaned

        numeric_fields = ['quantity_delivered', 'quantity_ordered', 'quantity_duplicate', 'ppv', 'pph', 'total_ttc',
                          'gf']

        # for index, line in enumerate(data_to_loop[:check_to_index]):
        #     parts = line.split()
        #     if not parts:
        #         continue
        #
        #     selected_clean_word, word_index = select_name_root_from_line(line)
        #     # logging.info("selected_clean_word : ", selected_clean_word)
        #     # logging.info("word_index : ", word_index)
        #     # logging.info("line___ : ", line)
        #     # logging.info("index : ", index)
        #     # logging.info("length : ", len(data_to_loop))
        #     # logging.info("check_to_index : ", check_to_index)

        if model == constants.SPR:
            data_to_loop = clean_chars_ocr_lines_last_parts(data_to_loop, 3, additional_section_start_index)

            for index, line in enumerate(data_to_loop[:check_to_index]):
                # Preprocess the line to remove leading zeros and special characters
                line = re.sub(r'^[0\s\\]*', '', line).strip()
                parts = line.split()
                if not parts:
                    continue

                # Remove '=' from the beginning of any part
                parts = [part.lstrip('=') for part in parts]

                selected_clean_word, word_index = select_name_root_from_line(' '.join(parts))

                # Process Qty Ordered / Delivered
                if word_index is not None:
                    if word_index == 0:
                        line = '0 0 ' + ' '.join(parts)
                    elif word_index == 1:
                        if len(parts[0]) <= 3 or (len(parts[0]) == 4 and parts[0].endswith(',')):
                            corrected_qty_delivered = correct_number(
                                ''.join(constants.quantity_correction_dict.get(c, c) for c in parts[0]))
                            line = '0 ' + corrected_qty_delivered + ' ' + ' '.join(parts[1:])
                        else:
                            line = '0 0 ' + ' '.join(parts)
                    elif word_index >= 2:
                        if (len(parts[0]) <= 3 or (len(parts[0]) == 4 and parts[0].endswith(','))) and \
                                (len(parts[1]) <= 3 or (len(parts[1]) == 4 and parts[1].endswith(','))):
                            corrected_qty_ordered = correct_number(
                                ''.join(constants.quantity_correction_dict.get(c, c) for c in parts[0]))
                            corrected_qty_delivered = correct_number(
                                ''.join(constants.quantity_correction_dict.get(c, c) for c in parts[1]))
                            line = corrected_qty_ordered + ' ' + corrected_qty_delivered + ' ' + ' '.join(parts[2:])
                        elif len(parts[0]) <= 3 or (len(parts[0]) == 4 and parts[0].endswith(',')):
                            corrected_qty_ordered = correct_number(
                                ''.join(constants.quantity_correction_dict.get(c, c) for c in parts[0]))
                            line = corrected_qty_ordered + ' 0 ' + ' '.join(parts[1:])
                        elif len(parts[1]) <= 3 or (len(parts[1]) == 4 and parts[1].endswith(',')):
                            corrected_qty_delivered = correct_number(
                                ''.join(constants.quantity_correction_dict.get(c, c) for c in parts[1]))
                            line = '0 ' + corrected_qty_delivered + ' ' + ' '.join(parts[2:])
                        else:
                            line = '0 0 ' + ' '.join(parts)
                else:
                    line = '0 0 ' + ' '.join(parts)

                parts = line.split()

                # Process P.P.M - Price
                if len(parts) >= 3:
                    price_ppm = normalize_price_string_count_after_coma(parts[-3], 2)
                    parts[-3] = price_ppm

                # Reassemble the line
                data_to_loop[index] = ' '.join(parts)

                # Set attribute indexes
                attribute_indexes['quantity_ordered'].append(0)
                attribute_indexes['quantity_delivered'].append(1)
                attribute_indexes['designation'].append(2)
                attribute_indexes['pph'].append(-3)

                # Set other indexes to None
                attribute_indexes['quantity_duplicate'].append(None)
                attribute_indexes['ppv'].append(None)
                attribute_indexes['total_ttc'].append(None)
                attribute_indexes['gf'].append(None)
                attribute_indexes['tva'].append(None)
                attribute_indexes['word_index'].append(word_index if word_index else None)

        elif model == constants.COOPER_PHARMA_CASA:

            for index, line in enumerate(data_to_loop[:check_to_index]):
                parts = line.split()
                if not parts:
                    continue

                selected_clean_word, word_index = select_name_root_from_line(line)
                # Handle quantity and forme_galenique
                if len(parts) >= 2:
                    # Quantity is always first
                    attribute_indexes['quantity'].append(0)

                    # Forme galenique is second if it's 2-3 letters
                    if len(parts[1]) <= 3 and parts[1].isalpha():
                        attribute_indexes['forme_galenique'].append(1)
                        designation_start = 2
                    else:
                        attribute_indexes['forme_galenique'].append(None)
                        designation_start = 1

                    # Find where pricing information starts
                    for i in range(designation_start, len(parts)):
                        if is_float(parts[i]):
                            price_start_index = i
                            break
                    else:
                        price_start_index = len(parts)

                    # Store designation index and length
                    attribute_indexes['designation'].append((designation_start, price_start_index))

                    # Find date_per and related fields
                    date_index, corrected_date = find_date_pattern(parts)
                    if date_index is not None:
                        parts[date_index] = corrected_date
                        attribute_indexes['date_per'].append(date_index)

                        # Find num_lot based on date position
                        num_lot_index, num_lot = get_num_lot(parts, date_index)
                        if num_lot_index is not None:
                            attribute_indexes['num_lot'].append(num_lot_index)
                        else:
                            attribute_indexes['num_lot'].append(None)
                    else:
                        attribute_indexes['date_per'].append(None)
                        attribute_indexes['num_lot'].append(None)

                    # Find code_produit
                    code_index, code = get_code_produit(parts)
                    if code_index is not None:
                        attribute_indexes['code_produit'].append(code_index)
                    else:
                        attribute_indexes['code_produit'].append(None)

                    # Map the pricing fields
                    if len(parts) >= price_start_index:
                        attribute_indexes['ppv'].append(price_start_index)
                        attribute_indexes['pph'].append(
                            price_start_index + 1 if price_start_index + 1 < len(parts) else None)
                        attribute_indexes['total_ttc'].append(
                            price_start_index + 2 if price_start_index + 2 < len(parts) else None)
                        attribute_indexes['tva'].append(
                            price_start_index + 3 if price_start_index + 3 < len(parts) else None)
                    else:
                        for field in ['ppv', 'pph', 'total_ttc', 'tva']:
                            attribute_indexes[field].append(None)

                attribute_indexes['word_index'].append(word_index)

                # Clean numeric values
                for field in ['ppv', 'pph', 'total_ttc']:
                    if attribute_indexes[field][-1] is not None:
                        idx = attribute_indexes[field][-1]
                        if idx < len(parts):
                            parts[idx] = normalize_price_string_count_after_coma(parts[idx], 3)

                # Clean TVA
                if attribute_indexes['tva'][-1] is not None:
                    idx = attribute_indexes['tva'][-1]
                    if idx < len(parts):
                        parts[idx] = correct_number(parts[idx])

                data_to_loop[index] = ' '.join(parts)

        elif model == constants.GPM or model == constants.CPRE:

            for index, line in enumerate(data_to_loop[:check_to_index]):
                parts = line.split()
                if not parts:
                    continue

                selected_clean_word, word_index = select_name_root_from_line(line)

                # Initialize variables that might be used later
                pph_index = None
                ppv_index = None

                # Initialize indexes dictionary for this line
                line_indexes = {
                    'quantity': None,
                    'designation': None,
                    'ppv': None,
                    'pph': None,
                    'tva': None,
                    'date_per': None,
                    'code_produit': None
                }

                # Process quantity (always first number)
                if parts[0].isdigit() or contains_number(parts[0]):
                    line_indexes['quantity'] = 0
                    parts[0] = correct_number(parts[0])

                # Find TVA (search from end, looking for pattern with %)
                tva_index = None
                for i in range(len(parts) - 1, -1, -1):
                    if '%' in parts[i]:
                        tva_index = i
                        # Clean TVA value
                        tva_value = parts[i].replace('%', '')
                        parts[i] = correct_number(tva_value) + '%'
                        line_indexes['tva'] = i
                        break

                if tva_index is not None and tva_index > 0:
                    # PPH is the number before TVA
                    pph_index = tva_index - 1
                    if is_float(parts[pph_index]):
                        line_indexes['pph'] = pph_index
                        parts[pph_index] = correct_number_except_coma(parts[pph_index])

                        # PPV is the number before PPH
                        if pph_index > 0:
                            ppv_index = pph_index - 1
                            if is_float(parts[ppv_index]):
                                line_indexes['ppv'] = ppv_index
                                parts[ppv_index] = correct_number_except_coma(parts[ppv_index])

                # Find date (format mm/yy)
                date_index, corrected_date = find_date_pattern(parts)
                if date_index is not None:
                    line_indexes['date_per'] = date_index
                    parts[date_index] = corrected_date

                # Get code_produit (word before date)
                if date_index is not None and date_index > 0:
                    code_index = date_index - 1
                    if len(parts[code_index]) >= 4:  # Assuming code is at least 4 chars
                        line_indexes['code_produit'] = code_index

                # Set designation range (from after quantity to before pph)
                if word_index is not None:
                    designation_start = word_index
                    # Find the earliest price-related index that exists
                    price_indexes = [i for i in [ppv_index, pph_index, tva_index] if i is not None]
                    designation_end = min(price_indexes) if price_indexes else len(parts)
                    line_indexes['designation'] = (designation_start, designation_end)

                # Store indexes for this line
                for key in line_indexes:
                    attribute_indexes[key].append(line_indexes[key])

                # Reassemble the line
                data_to_loop[index] = ' '.join(parts)

        elif model == constants.SOPHADIMS or model == constants.RECAMED or model == constants.SOPHAFAS or model == constants.SOPHACHARK or model == constants.REPHAK or model == constants.SOPHASAIS:
            for index, line in enumerate(data_to_loop[:check_to_index]):
                parts = line.split()
                if not parts:
                    continue

                selected_clean_word, word_index = select_name_root_from_line(line)

                # Initialize indexes for this line
                line_indexes = {
                    'quantity': None,
                    'designation': None,
                    'ppv': None,
                    'pph': None,
                    'total_ttc': None,
                    'tva': None,
                    'quantity_duplicate': None,
                    'code_produit': None
                }

                # Process quantity (first position)
                # If first part is not a valid quantity, insert 0 but preserve the rest
                if not parts[0].isdigit() and not contains_number(parts[0]):
                    parts.insert(0, '0')
                else:
                    parts[0] = correct_number(parts[0])
                line_indexes['quantity'] = 0

                # Find PPH (first number with at least 3 digits after designation)
                pph_index = None
                for i in range(1, len(parts)):
                    current_part = parts[i]
                    # Remove any decimal point and leading/trailing spaces
                    cleaned_number = current_part.replace('.', '').strip()

                    # Check if the next part exists
                    has_next_part = i + 1 < len(parts)
                    next_part = parts[i + 1] if has_next_part else ''

                    # Conditions:
                    # 1. Current part must be at least 3 characters
                    # 2. All characters in cleaned number must be digits
                    # 3. Next part (if exists) must not contain any letters
                    if (len(cleaned_number) >= 3 and
                            cleaned_number.isdigit() and
                            (not has_next_part or not any(c.isalpha() for c in next_part))):
                        pph_index = i
                        line_indexes['pph'] = i
                        parts[i] = normalize_price_string_count_after_coma(parts[i], 2)
                        break

                if pph_index:
                    # Total TTC is next after PPH
                    if pph_index + 1 < len(parts) and contains_number(parts[pph_index + 1]):
                        line_indexes['total_ttc'] = pph_index + 1
                        parts[pph_index + 1] = normalize_price_string_count_after_coma(parts[pph_index + 1], 2)

                    # TVA follows Total TTC
                    if pph_index + 2 < len(parts):
                        line_indexes['tva'] = pph_index + 2
                        parts[pph_index + 2] = correct_number(parts[pph_index + 2])

                    # Find PPV (last numeric value)
                    for i in range(len(parts) - 1, -1, -1):
                        if contains_number(parts[i]) and len(parts[i]) >= 3:
                            line_indexes['ppv'] = i
                            parts[i] = normalize_price_string_count_after_coma(parts[i], 2)
                            break

                    # Set designation range (from after quantity to before pph)
                    if word_index is not None:
                        designation_start = word_index
                        designation_end = pph_index
                        line_indexes['designation'] = (designation_start, designation_end)
                    else:
                        # If word_index is None, set the designation to the entire line
                        line_indexes['designation'] = (0, len(parts))

                # Handle quantity duplicate (if exists)
                if len(parts) > 1 and (parts[1].isdigit() or contains_number(parts[1])):
                    line_indexes['quantity_duplicate'] = 1
                    parts[1] = correct_number(parts[1])

                # Store indexes for this line
                for key in line_indexes:
                    attribute_indexes[key].append(line_indexes[key])

                # Reassemble the line
                data_to_loop[index] = ' '.join(parts)


        elif model == constants.SOPHACA or model == constants.SOPHAGHARB or model == constants.UGP or model == constants.GIPHAR or model == constants.DIPHARM:

            for index, line in enumerate(data_to_loop[:check_to_index]):
                parts = line.split()
                if not parts:
                    continue

                selected_clean_word, word_index = select_name_root_from_line(line)

                # Initialize line indexes
                line_indexes = {
                    'quantity': None,
                    'designation': None,
                    'ppv': None,
                    'pph': None,
                    'total_ttc': None,
                    'tva': None
                }

                # Process first word as quantity
                first_word = parts[0] if parts else ''

                # Check if first word contains any characters that need correction
                needs_correction = any(c in constants.quantity_correction_dict for c in first_word)
                is_quantity = first_word.isdigit() or needs_correction

                # if is_quantity:
                #     # Apply corrections from quantity_correction_dict
                #     corrected_quantity = ''.join(constants.quantity_correction_dict.get(c, c) for c in first_word)
                #     # If the corrected quantity is still not valid, set it to 0
                #     if not corrected_quantity.isdigit():
                #         corrected_quantity = '0'
                #     parts[0] = corrected_quantity
                #     line_indexes['quantity'] = 0
                # else:
                #     # If first word is not a quantity, insert 0 at the beginning
                #     parts.insert(0, '0')
                #     line_indexes['quantity'] = 0

                # Check if first word is numeric or represents quantity
                if first_word.isdigit() and len(first_word) <= 3:
                    line_indexes['quantity'] = 0
                else:
                    # If first word is not valid quantity, prepend '0'
                    parts.insert(0, '0')
                    line_indexes['quantity'] = 0

                # Process PPV (second word with at least 3 numbers)
                ppv_found = False
                current_index = 1  # Start from second word
                while current_index < len(parts):
                    cleaned_number = parts[current_index].replace('.', '').strip()
                    if cleaned_number.isdigit() and len(cleaned_number) >= 3:
                        line_indexes['ppv'] = current_index
                        parts[current_index] = normalize_price_string_count_after_coma(parts[current_index], 2)
                        ppv_found = True
                        break
                    current_index += 1
                if not ppv_found:
                    line_indexes['ppv'] = 1
                    parts.insert(1, "0")

                # Find PPH (first number with at least 3 digits after designation)
                if word_index:
                    pph_index = None
                    for i in range(word_index, len(parts)):
                        current_part = parts[i]
                        cleaned_number = current_part.replace('.', '').strip()

                        has_next_part = i + 1 < len(parts)
                        next_part = parts[i + 1] if has_next_part else ''

                        if (len(cleaned_number) >= 3 and
                                cleaned_number.isdigit() and
                                (not has_next_part or not any(c.isalpha() for c in next_part))):
                            pph_index = i
                            line_indexes['pph'] = i
                            parts[i] = normalize_price_string_count_after_coma(parts[i], 2)
                            break

                    # Set total_ttc (next number after PPH)
                    if pph_index and pph_index + 1 < len(parts):
                        total_ttc = parts[pph_index + 1]
                        if contains_number(total_ttc) and len(total_ttc) >= 3:
                            line_indexes['total_ttc'] = pph_index + 1
                            parts[pph_index + 1] = normalize_price_string_count_after_coma(total_ttc, 2)

                    # Set designation range
                    if word_index and pph_index:
                        line_indexes['designation'] = (word_index, pph_index)

                # Store indexes for this line
                for key in line_indexes:
                    if key not in attribute_indexes:
                        attribute_indexes[key] = []
                    attribute_indexes[key].append(line_indexes[key])

                # Reassemble the line
                data_to_loop[index] = ' '.join(parts)

        data_to_loop = replace_double_spaces(data_to_loop)  # was filtered_and_formatted_lines
        return data_to_loop, attribute_indexes
    except (ValueError, TypeError) as e:
        logging.error(f"Error in correct_chiffres_calculated: {traceback.format_exc()}")
        # Return default values instead of None
        return [], {key: [] for key in attribute_indexes.keys()}


"""-----******************************  Structuring data to JSON Format  ******************************-----"""
""" ------------------------------------------------------------------------------------------------------------------ """

"""-----************ MODEL BL (SPR) ************ -----"""


def correct_date_SPR(date_str):
    try:
        def correct_date_validate(date):
            parts = date.split('/')
            if len(parts) == 3:
                day, month, year = parts

                # Apply corrections from the dictionary to each part
                day = "".join(constants.quantity_correction_dict.get(char, char) for char in day if
                              char.isdigit() or char in constants.quantity_correction_dict)
                month = "".join(constants.quantity_correction_dict.get(char, char) for char in month if
                                char.isdigit() or char in constants.quantity_correction_dict)
                year = "".join(constants.quantity_correction_dict.get(char, char) for char in year if
                               char.isdigit() or char in constants.quantity_correction_dict)

                # Validate and correct the day
                if day.isdigit() and int(day) > 31:
                    day = str(day)[-1]  # Use the last digit if the day is nonsensically high

                # Validate and correct the month
                if month.isdigit() and int(month) > 12:
                    month = '12'

                if day.isdigit() and month.isdigit() and year.isdigit():
                    # Reconstruct the date string with corrections
                    corrected_date_str = f"{int(day):02}/{int(month):02}/{year}"
                    return corrected_date_str

            return "Invalid Date"  # Return a placeholder if parsing fails

        # Replace common OCR errors
        date_str = date_str.replace('o', '0').replace('O', '0').replace('l', '1').replace('I', '1')

        # Try to parse the date using different expected formats
        for fmt in ("%d/%m/%Y", "%d%m%Y", "%d/%m/%y"):  # You can add more formats as needed
            try:
                # If the date is correctly parsed, return it in a uniform format
                parsed_date = datetime.strptime(date_str, fmt)
                return parsed_date.strftime("%d/%m/%Y")
            except ValueError:
                continue  # Try the next format if the current one fails

        return correct_date_validate(date_str)
        # return "Invalid Date"  # Return a placeholder if all parses fail

    except (ValueError, TypeError) as e:
        logging.error(f"Error in correct_date_SPR: {traceback.format_exc()}")
        return "Invalid Date"  # Return a placeholder if all parses fail


def structure_data_SPR(lines, attribute_indexes, additional_section_start_index):
    try:
        structured_data = []
        tnp_result = []

        # Model of data
        document = DocumentModel()

        # logging.info('attribute_indexes : ', attribute_indexes)
        # Determine the lines to check based on additional_section_start_index
        if additional_section_start_index is not None:
            lines_to_check = lines[:additional_section_start_index]
            lines_to_append_as_is = lines[additional_section_start_index:]
        else:
            lines_to_check = lines
            lines_to_append_as_is = []

        for i, line in enumerate(lines_to_check):  # This skips the first element of the list
            parts = line.split()
            # parts = clean_ocr_line_last_seven_parts(line.split())
            # logging.info('parts : ', parts)
            if not parts:
                continue

            selected_clean_word, word_index = select_name_root_from_line(line)

            if word_index or parts[attribute_indexes['quantity_delivered'][i]]:

                # Indexes :
                ordered_qty_index = attribute_indexes['quantity_ordered'][i]
                delivered_qty_index = attribute_indexes['quantity_delivered'][i]
                price_ppm_index = attribute_indexes['pph'][i]
                designation_index = attribute_indexes['designation'][i] if attribute_indexes['designation'][
                    i] else 1  # must be ' else 2'

                ordered_qty = parts[ordered_qty_index] if ordered_qty_index is not None else None
                delivered_qty = parts[delivered_qty_index] if delivered_qty_index is not None else None

                # Process P.P.M - Price
                price_ppm = parts[price_ppm_index] if price_ppm_index is not None else None

                # Designation
                designation = ' '.join(parts[designation_index:len(parts) - 3])

                if designation != '':
                    structured_data.append({
                        'ordered_quantity': ordered_qty,
                        'delivered_quantity': delivered_qty,
                        'designation': designation,
                        'ppm': price_ppm,
                        'lot_number': parts[-2],
                        'date': parts[-1],
                        'additional_info': "",
                    })
                else:
                    structured_data.append({
                        'ordered_quantity': "",
                        'delivered_quantity': "",
                        'designation': "",
                        'ppm': "",
                        'lot_number': "",
                        'date': "",
                        'additional_info': line,
                    })

                # Generate the new JSON structure for TNP
                TNP = {
                    "ID": "",
                    "product_category_id_label": "",
                    "product_galenic_form_id_label": "",
                    "name": designation,
                    "barcode": "",
                    "barcode_2": "",
                    "prix_vente": price_ppm
                }
                tnp_result.append(TNP)

        for i, line in enumerate(lines_to_append_as_is):
            structured_data.append({
                'ordered_quantity': "",
                'delivered_quantity': "",
                'designation': "",
                'ppm': "",
                'lot_number': "",
                'date': "",
                'additional_info': line,
            })

        for data in structured_data:
            table_row = DocumentModel.Table()
            table_row.qty_ordered = data['ordered_quantity']
            table_row.quantity = data['delivered_quantity']
            table_row.designation = data['designation']
            table_row.pph = str(data['ppm'])
            table_row.num_lot = data['lot_number']
            table_row.date_per = correct_date_SPR(data['date'])
            table_row.additional_info = data['additional_info']

            document.table.append(table_row)

        return document, json.dumps(tnp_result, indent=4, ensure_ascii=False)


    except (IndexError, ValueError, AttributeError) as e:
        logging.error(f"Error in structure_data_SPR: {traceback.format_exc()}")
        return None, None


"""-----************ MODEL BL (SOPHACA) ************ -----"""


def structure_data_SOPHACA(data_to_loop, attribute_indexes, additional_section_start_index):
    try:
        structured_data = []
        tnp_result = []
        document = DocumentModel()

        if additional_section_start_index is not None:
            lines_to_check = data_to_loop[:additional_section_start_index]
            lines_to_append_as_is = data_to_loop[additional_section_start_index:]
        else:
            lines_to_check = data_to_loop
            lines_to_append_as_is = []

        for i, line in enumerate(lines_to_check):
            parts = line.split()
            if not parts:
                continue

            # Get indexes from attribute_indexes
            quantity_index = attribute_indexes['quantity'][i]
            designation_range = attribute_indexes['designation'][i]
            ppv_index = attribute_indexes['ppv'][i]
            pph_index = attribute_indexes['pph'][i]
            total_ttc_index = attribute_indexes['total_ttc'][i] if 'total_ttc' in attribute_indexes else None

            # Initialize data dictionary
            data = {
                'quantity': None,
                'designation': None,
                'ppv': None,
                'pph': None,
                'total_ttc': None,
                'additional_info': None
            }

            # Extract quantity
            if quantity_index is not None and quantity_index < len(parts):
                data['quantity'] = parts[quantity_index]

            # Extract designation
            if designation_range and isinstance(designation_range, tuple):
                start, end = designation_range
                data['designation'] = ' '.join(parts[start:end])

            # Extract pricing information
            if ppv_index is not None and ppv_index < len(parts):
                data['ppv'] = parts[ppv_index]
            if pph_index is not None and pph_index < len(parts):
                data['pph'] = parts[pph_index]
            if total_ttc_index is not None and total_ttc_index < len(parts):
                data['total_ttc'] = parts[total_ttc_index]

            # Create table row
            table_row = DocumentModel.Table()
            table_row.quantity = data['quantity']
            table_row.designation = data['designation']
            table_row.ppv = str(data['ppv']) if data['ppv'] else ''
            table_row.pph = str(data['pph']) if data['pph'] else ''
            table_row.total_ttc = str(data['total_ttc']) if data['total_ttc'] else ''

            document.table.append(table_row)

            # Generate TNP structure
            TNP = {
                "ID": "",
                "product_category_id_label": "",
                "product_galenic_form_id_label": "",
                "name": data['designation'],
                "barcode": "",
                "barcode_2": "",
                "prix_vente": data['ppv'] if data['ppv'] else ""
            }
            tnp_result.append(TNP)

        # Handle additional lines
        for line in lines_to_append_as_is:
            table_row = DocumentModel.Table()
            table_row.additional_info = line
            document.table.append(table_row)

        return document, json.dumps(tnp_result, indent=4, ensure_ascii=False)

    except Exception as e:
        logging.error(f"Error in structure_data_SOPHACA: {traceback.format_exc()}")
        return None, None


"""-----************ MODEL BL (COOPER_PHARMA_CASA) ************ -----"""


# def structure_data_COOPER_PHARMA_CASA(data_to_loop, attribute_indexes, additional_section_start_index):
#     try:
#         tnp_result = []
#
#         # Determine the lines to check based on additional_section_start_index
#         if additional_section_start_index is not None:
#             lines_to_check = data_to_loop[:additional_section_start_index]
#             lines_to_append_as_is = data_to_loop[additional_section_start_index:]
#         else:
#             lines_to_check = data_to_loop
#             lines_to_append_as_is = []
#
#         # Model of data
#         document = DocumentModel()
#
#         for i, line in enumerate(lines_to_check):  # This skips the first element of the list
#             parts = line.split()
#             # parts = clean_ocr_line_last_seven_parts(line.split())
#             # logging.info('parts : ', parts)
#             if not parts:
#                 continue
#
#             # Indexes :
#             quantity_index = attribute_indexes['quantity_delivered'][i]
#             total_ttc_index = attribute_indexes['total_ttc'][i]
#             pph_index = attribute_indexes['pph'][i]
#             ppv_index = attribute_indexes['ppv'][i]
#             designation_index = attribute_indexes['designation'][i] if attribute_indexes['designation'][
#                 i] else 1  # must be ' else 2'
#
#             # Extract quantity
#             quantity = parts[quantity_index] if quantity_index is not None else None
#
#             # Pricing information
#             if len(parts) >= 6:
#                 code_produit = parts[-1]
#                 date_per = parts[-2]
#                 num_lot = parts[-3]
#
#                 total_numeric = parts[total_ttc_index] if total_ttc_index is not None else None
#                 pu_numeric = parts[pph_index] if pph_index is not None else None
#                 ppv_numeric = parts[ppv_index] if ppv_index is not None else None
#
#             else:
#                 # total_numeric = parts[-4]
#                 # pu_numeric = parts[-5]
#                 # ppv_numeric = parts[-6]
#                 total_numeric = pu_numeric = ppv_numeric = "0.000"
#                 code_produit = date_per = num_lot = ""
#
#             # Determine where pricing information starts (it should be the last three parts)
#             if len(parts) >= 6:
#                 description_end_index = len(
#                     parts) - 6  # 6 is the sum of parts after description (ppv, pph, ttc, code_produit, lot, date)
#             else:
#                 description_end_index = len(parts)  # Fallback if not enough parts
#
#             # Code and Description
#             if attribute_indexes['word_index'][i] and len(parts) > attribute_indexes['word_index'][i]:
#                 code = parts[1] if len(parts[1]) >= 2 else (parts[2] if len(parts) > 2 else None)
#                 description = ' '.join(parts[designation_index:description_end_index])
#             else:
#                 code = None
#                 description = None
#
#             # Store the data as table_row in the document model, then append it in the table model
#             table_row = DocumentModel.Table()
#
#             table_row.quantity = quantity
#             table_row.forme_galenique = code
#             table_row.designation = description
#             table_row.ppv = ppv_numeric
#             table_row.pph = pu_numeric
#             table_row.total_ttc = total_numeric
#             table_row.num_lot = num_lot
#             table_row.code_produit = code_produit
#             table_row.date_per = date_per
#
#             document.table.append(table_row)
#
#             # Generate the new JSON structure for TNP
#             TNP = {
#                 "ID": "",
#                 "product_category_id_label": "",
#                 "product_galenic_form_id_label": code,
#                 "name": description,
#                 "barcode": "",
#                 "barcode_2": "",
#                 "prix_vente": ppv_numeric
#             }
#             tnp_result.append(TNP)
#
#         # Store Additional Lines
#         for i, line in enumerate(lines_to_append_as_is):
#             table_row = DocumentModel.Table()
#             table_row.additional_info = line
#             document.table.append(table_row)
#
#         return document, json.dumps(tnp_result, indent=4, ensure_ascii=False)
#
#     except (IndexError, ValueError, AttributeError) as e:
#         logging.error(f"Error in structure_data_COOPER_PHARMA_CASA: {traceback.format_exc()}")
#         return None, None


def structure_data_COOPER_PHARMA_CASA(data_to_loop, attribute_indexes, additional_section_start_index):
    try:
        tnp_result = []
        document = DocumentModel()

        if additional_section_start_index is not None:
            lines_to_check = data_to_loop[:additional_section_start_index]
            lines_to_append_as_is = data_to_loop[additional_section_start_index:]
        else:
            lines_to_check = data_to_loop
            lines_to_append_as_is = []

        for i, line in enumerate(lines_to_check):
            parts = line.split()
            if not parts:
                continue

            table_row = DocumentModel.Table()

            # Extract quantity
            if attribute_indexes['quantity'][i] is not None:
                table_row.quantity = parts[attribute_indexes['quantity'][i]]

            # Extract forme_galenique
            if attribute_indexes['forme_galenique'][i] is not None:
                table_row.forme_galenique = parts[attribute_indexes['forme_galenique'][i]]

            # Extract designation
            if attribute_indexes['designation'][i] is not None:
                start, end = attribute_indexes['designation'][i]
                table_row.designation = ' '.join(parts[start:end])

            # Extract date_per
            if attribute_indexes['date_per'][i] is not None:
                idx = attribute_indexes['date_per'][i]
                if idx < len(parts):
                    table_row.date_per = parts[idx]

            # Extract num_lot
            if attribute_indexes['num_lot'][i] is not None:
                idx = attribute_indexes['num_lot'][i]
                if idx < len(parts):
                    table_row.num_lot = parts[idx]

            # Extract code_produit
            if attribute_indexes['code_produit'][i] is not None:
                idx = attribute_indexes['code_produit'][i]
                if idx < len(parts):
                    table_row.code_produit = parts[idx]

            # Extract pricing fields
            for field in ['ppv', 'pph', 'total_ttc', 'tva']:
                if attribute_indexes[field][i] is not None:
                    idx = attribute_indexes[field][i]
                    if idx < len(parts):
                        setattr(table_row, field, parts[idx])

            document.table.append(table_row)

            # Generate TNP structure
            TNP = {
                "ID": "",
                "product_category_id_label": "",
                "product_galenic_form_id_label": table_row.forme_galenique if hasattr(table_row,
                                                                                      'forme_galenique') else "",
                "name": table_row.designation if hasattr(table_row, 'designation') else "",
                "barcode": "",
                "barcode_2": "",
                "prix_vente": table_row.ppv if hasattr(table_row, 'ppv') else ""
            }
            tnp_result.append(TNP)

        # Handle additional lines
        for line in lines_to_append_as_is:
            table_row = DocumentModel.Table()
            table_row.additional_info = line
            document.table.append(table_row)

        return document, json.dumps(tnp_result, indent=4, ensure_ascii=False)

    except Exception as e:
        logging.error(f"Error in structure_data_COOPER_PHARMA_CASA: {traceback.format_exc()}")
        return None, None


"""-----************ MODEL BL (GPM) ************ -----"""


def structure_data_GPM(data_to_loop, attribute_indexes, additional_section_start_index):
    try:
        tnp_result = []
        document = DocumentModel()

        if additional_section_start_index is not None:
            lines_to_check = data_to_loop[:additional_section_start_index]
            lines_to_append_as_is = data_to_loop[additional_section_start_index:]
        else:
            lines_to_check = data_to_loop
            lines_to_append_as_is = []

        for i, line in enumerate(lines_to_check):
            parts = line.split()
            if not parts:
                continue
            data = {}

            # Get indexes from attribute_indexes
            quantity_index = attribute_indexes['quantity'][i]
            tva_index = attribute_indexes['tva'][i]
            pph_index = attribute_indexes['pph'][i]
            ppv_index = attribute_indexes['ppv'][i]
            designation_range = attribute_indexes['designation'][i]

            # Extract quantity
            data['Quantity'] = parts[quantity_index] if quantity_index is not None else None

            # Extract TVA
            data['TVA'] = parts[tva_index] if tva_index is not None else None

            # Extract PPH (PU)
            data['PU'] = parts[pph_index] if pph_index is not None else None

            # Extract PPV
            data['PPV'] = parts[ppv_index] if ppv_index is not None else None

            # Extract code_produit
            code_produit_index = attribute_indexes['code_produit'][i]
            data['Code'] = parts[code_produit_index] if code_produit_index is not None else None

            # Extract designation
            if designation_range and isinstance(designation_range, tuple):
                start, end = designation_range
                data['Description'] = ' '.join(parts[start:end])
            else:
                data['Description'] = ''

            # Create table row
            table_row = DocumentModel.Table()
            table_row.quantity = data['Quantity']
            table_row.designation = data['Description']
            table_row.pph = str(data['PU']) if data['PU'] else ''
            table_row.ppv = str(data['PPV']) if data['PPV'] else ''
            table_row.tva = data['TVA']
            table_row.code_produit = data['Code']

            # Add date_per if available
            if 'date_per' in attribute_indexes and attribute_indexes['date_per'][i] is not None:
                date_index = attribute_indexes['date_per'][i]
                table_row.date_per = parts[date_index] if date_index < len(parts) else ''

            document.table.append(table_row)

            # Generate TNP structure
            TNP = {
                "ID": "",
                "product_category_id_label": "",
                "product_galenic_form_id_label": "",
                "name": data['Description'],
                "barcode": "",
                "barcode_2": "",
                "prix_vente": data['PPV'] if data['PPV'] else ""
            }
            tnp_result.append(TNP)

        # Handle additional lines
        for line in lines_to_append_as_is:
            table_row = DocumentModel.Table()
            table_row.additional_info = line
            document.table.append(table_row)

        return document, json.dumps(tnp_result, indent=4, ensure_ascii=False)

    except Exception as e:
        logging.error(f"Error in structure_data_GPM: {traceback.format_exc()}")
        return None, None


"""-----************ MODEL BL (Sophadims) ************ -----"""


def structure_data_SOPHADIMS(data_to_loop, attribute_indexes, additional_section_start_index):
    try:
        tnp_result = []
        document = DocumentModel()

        if additional_section_start_index is not None:
            lines_to_check = data_to_loop[:additional_section_start_index]
            lines_to_append_as_is = data_to_loop[additional_section_start_index:]
        else:
            lines_to_check = data_to_loop
            lines_to_append_as_is = []

        for i, line in enumerate(lines_to_check):
            parts = line.split()
            if not parts:
                continue

            # Get indexes from attribute_indexes
            quantity_index = attribute_indexes['quantity'][i]
            designation_range = attribute_indexes['designation'][i]
            pph_index = attribute_indexes['pph'][i]
            ppv_index = attribute_indexes['ppv'][i]
            total_ttc_index = attribute_indexes['total_ttc'][i]
            tva_index = attribute_indexes['tva'][i]
            quantity_duplicate_index = attribute_indexes['quantity_duplicate'][i]

            # Initialize data dictionary
            data = {
                'quantity': None,
                'designation': None,
                'pph': None,
                'ppv': None,
                'total_ttc': None,
                'tva': None,
                'quantity_duplicate': None,
                'code_produit': None,
                'date_per': None
            }

            # Extract quantity
            if quantity_index is not None and quantity_index < len(parts):
                data['quantity'] = parts[quantity_index]

            # Extract quantity duplicate
            if quantity_duplicate_index is not None and quantity_duplicate_index < len(parts):
                data['quantity_duplicate'] = parts[quantity_duplicate_index]

            # Extract designation
            if designation_range and isinstance(designation_range, tuple):
                start, end = designation_range
                data['designation'] = ' '.join(parts[start:end])

            # Extract pricing information
            if pph_index is not None and pph_index < len(parts):
                data['pph'] = parts[pph_index]
            if ppv_index is not None and ppv_index < len(parts):
                data['ppv'] = parts[ppv_index]
            if total_ttc_index is not None and total_ttc_index < len(parts):
                data['total_ttc'] = parts[total_ttc_index]
            if tva_index is not None and tva_index < len(parts):
                data['tva'] = parts[tva_index]

            # Extract code_produit (assuming it's the second to last element)
            if len(parts) >= 2:
                data['code_produit'] = parts[-2]

            # Create table row
            table_row = DocumentModel.Table()
            table_row.quantity = data['quantity']
            table_row.qty_duplicate = data['quantity_duplicate']
            table_row.designation = data['designation']
            table_row.pph = str(data['pph']) if data['pph'] else ''
            table_row.ppv = str(data['ppv']) if data['ppv'] else ''
            table_row.total_ttc = str(data['total_ttc']) if data['total_ttc'] else ''
            table_row.tva = data['tva']
            table_row.code_produit = data['code_produit']

            document.table.append(table_row)

            # Generate TNP structure
            TNP = {
                "ID": "",
                "product_category_id_label": "",
                "product_galenic_form_id_label": "",
                "name": data['designation'],
                "barcode": "",
                "barcode_2": "",
                "prix_vente": data['ppv'] if data['ppv'] else ""
            }
            tnp_result.append(TNP)

        # Handle additional lines
        for line in lines_to_append_as_is:
            table_row = DocumentModel.Table()
            table_row.additional_info = line
            document.table.append(table_row)

        return document, json.dumps(tnp_result, indent=4, ensure_ascii=False)

    except Exception as e:
        logging.error(f"Error in structure_data_SOPHADIMS: {traceback.format_exc()}")
        return None, None


"""-----************ MODEL BL (RECAMED) ************ -----"""


def structure_data_RECAMED(data_to_loop, attribute_indexes, additional_section_start_index):
    try:
        tnp_result = []
        document = DocumentModel()

        if additional_section_start_index is not None:
            lines_to_check = data_to_loop[:additional_section_start_index]
            lines_to_append_as_is = data_to_loop[additional_section_start_index:]
        else:
            lines_to_check = data_to_loop
            lines_to_append_as_is = []

        for i, line in enumerate(lines_to_check):
            parts = line.split()
            if not parts:
                continue

            # Get indexes from attribute_indexes
            quantity_index = attribute_indexes['quantity'][i]
            designation_range = attribute_indexes['designation'][i]
            pph_index = attribute_indexes['pph'][i]
            ppv_index = attribute_indexes['ppv'][i]
            total_ttc_index = attribute_indexes['total_ttc'][i]
            tva_index = attribute_indexes['tva'][i]
            quantity_duplicate_index = attribute_indexes['quantity_duplicate'][i]

            # Initialize data dictionary
            data = {
                'quantity': None,
                'designation': None,
                'pph': None,
                'ppv': None,
                'total_ttc': None,
                'tva': None,
                'quantity_duplicate': None,
                'code_produit': None,
                'date_per': None
            }

            # Extract quantity
            if quantity_index is not None and quantity_index < len(parts):
                data['quantity'] = parts[quantity_index]

            # Extract quantity duplicate
            if quantity_duplicate_index is not None and quantity_duplicate_index < len(parts):
                data['quantity_duplicate'] = parts[quantity_duplicate_index]

            # Extract designation
            if designation_range and isinstance(designation_range, tuple):
                start, end = designation_range
                data['designation'] = ' '.join(parts[start:end])

            # Extract pricing information
            if pph_index is not None and pph_index < len(parts):
                data['pph'] = parts[pph_index]
            if ppv_index is not None and ppv_index < len(parts):
                data['ppv'] = parts[ppv_index]
            if total_ttc_index is not None and total_ttc_index < len(parts):
                data['total_ttc'] = parts[total_ttc_index]
            if tva_index is not None and tva_index < len(parts):
                data['tva'] = parts[tva_index]

            # Extract code_produit (assuming it's the second to last element)
            if len(parts) >= 2:
                data['code_produit'] = parts[-2]

            # Create table row
            table_row = DocumentModel.Table()
            table_row.quantity = data['quantity']
            table_row.qty_duplicate = data['quantity_duplicate']
            table_row.designation = data['designation']
            table_row.pph = str(data['pph']) if data['pph'] else ''
            table_row.ppv = str(data['ppv']) if data['ppv'] else ''
            table_row.total_ttc = str(data['total_ttc']) if data['total_ttc'] else ''
            table_row.tva = data['tva']
            table_row.code_produit = data['code_produit']

            document.table.append(table_row)

            # Generate TNP structure
            TNP = {
                "ID": "",
                "product_category_id_label": "",
                "product_galenic_form_id_label": "",
                "name": data['designation'],
                "barcode": "",
                "barcode_2": "",
                "prix_vente": data['ppv'] if data['ppv'] else ""
            }
            tnp_result.append(TNP)

        # Handle additional lines
        for line in lines_to_append_as_is:
            table_row = DocumentModel.Table()
            table_row.additional_info = line
            document.table.append(table_row)

        return document, json.dumps(tnp_result, indent=4, ensure_ascii=False)

    except Exception as e:
        logging.error(f"Error in structure_data_SOPHADIMS: {traceback.format_exc()}")
        return None, None


"""----- ************************************ FORMAT TAP ************************************ -----"""


def Export_Format_TAP(documentModel: DocumentModel):
    try:
        # Get data of table
        document = documentModel
        data_tap = []

        for row in document.table:
            # Generate the new JSON structure for TNP
            TNP = {
                "ID": "",
                "categorie": "",
                "forme_galenique": "",
                "designation": row.designation,
                "barcode": "",
                "barcode_2": "",
                "prix_vente": row.ppv if row.ppv else "",
            }
            data_tap.append(TNP)

        return data_tap

    except (ValueError, TypeError) as e:
        logging.error(f"Error in Export_Format_TAP: {traceback.format_exc()}")
        return None




"""----- ************************************ Utils Functions ************************************ -----"""



def find_date_pattern(parts):
    """Find date pattern mm/yy in parts and return index and corrected date"""
    try:
        for i, part in enumerate(parts):
            # Check for pattern like mm/yy or m/yy
            if '/' in part:
                month, year = part.split('/')
                # Correct numbers using correct_number function
                month = correct_number(month)
                year = correct_number(year)
                if len(month) <= 2 and len(year) == 2:
                    return i, f"{month.zfill(2)}/{year}"
        return None, None
    except ValueError:
        return None, None


def get_code_produit(parts):
    """Get code_produit based on the rules"""
    if not parts:
        return None, None

    # Try last word first
    last_word = parts[-1]
    if len(last_word) > 2:
        return -1, last_word
    # If last word is too short, try second to last
    elif len(parts) > 1:
        second_last = parts[-2]
        if len(second_last) > 2:
            return -2, second_last
    return None, None


def get_num_lot(parts, date_index):
    """Get num_lot based on date_index"""
    if date_index is None or date_index <= 0:
        return None, None

    lot = parts[date_index - 1]
    if len(lot) > 3:
        return date_index - 1, lot
    elif date_index > 1:
        # Try previous word if current is too short
        prev_lot = parts[date_index - 2]
        if len(prev_lot) > 3:
            return date_index - 2, prev_lot
    return None, None








