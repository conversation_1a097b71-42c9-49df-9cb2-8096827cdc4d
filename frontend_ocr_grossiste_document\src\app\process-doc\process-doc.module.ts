import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { ProcessDocPageRoutingModule } from './process-doc-routing.module';
import { SharedModule } from '../shared/shared.module'; // Import SharedModule

import { ProcessDocPage } from './process-doc.page';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    ProcessDocPageRoutingModule,
    SharedModule
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  declarations: [ProcessDocPage]
})
export class ProcessDocPageModule {}
