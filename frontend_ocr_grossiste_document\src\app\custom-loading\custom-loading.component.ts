import { Component, OnInit, Input  } from '@angular/core';

@Component({
  selector: 'app-custom-loading',
  templateUrl: './custom-loading.component.html',
  styleUrls: ['./custom-loading.component.scss'],
})
export class CustomLoadingComponent  implements OnInit {

  @Input() progress: number = 0;
  progress_displayed : number = 0

  constructor() { }

  ngOnInit() {
    setInterval(() => {
      // progress_displayed for be integer not have coma
      this.progress_displayed = Math.round(this.progress);
    }, 100);
  }

}
