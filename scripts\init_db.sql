-- PostgreSQL Database Initialization Script
-- This script is automatically executed when the PostgreSQL container starts

-- Create the main database (if not exists)
-- Note: The database is already created by POSTGRES_DB environment variable

-- Create extensions that might be useful
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- Create indexes for better performance (will be created by SQLAlchemy, but good to have as backup)
-- These will be created after the tables are created by the application

-- Set default timezone
SET timezone = 'UTC';

-- Create a function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Log successful initialization
DO $$
BEGIN
    RAISE NOTICE 'PostgreSQL database initialized successfully for OCR Document Grossiste';
END $$;
