@import url("https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap");

:host {
  // display: block;
  height: 100dvh;
}

* {
  font-family: "Inter", sans-serif;
  font-optical-sizing: auto;
}

ion-content::part(scroll) {
  // overflow-y: hidden !important;
  // --overflow: hidden !important;
  scrollbar-width: none !important; 
  -ms-overflow-style: none !important;  
}

ion-content{
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  --offset-top : 0px !important;
}

ion-header {
  height: 70px;
  --border: 0;
  display: flex;
  align-items: center;
  background-color: #fff;
}

ion-header ion-toolbar {
  --border: 0;
  --border-width: 0;
  display: flex;
  justify-content: center;
  align-items: flex-end;
  flex-direction: row;
}

ion-header ion-toolbar ion-title {
  font-size: 26px;
  font-weight: 700;
  color: #2f4fcd;
  text-align: left;
  width: 100%;
  padding-left: 2rem;
}

::ng-deep ion-header ion-toolbar app-custom-icon .custom-icon{
  width: 40px !important;
  height: 40px !important;
  margin-right: 5px !important;
}

ion-header ion-toolbar ion-icon {
  color: #101010;
  padding-right: 1rem;
}

.data-bl-wrapper {
  background: url("/assets/bg-scan-bl.png") no-repeat center center fixed;
  background-size: cover;
  height: 80%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin-top: 2rem;
}

::ng-deep .scan-bl-wrapper .file-import-icon img {
  width: 150px !important;
  height: 150px !important;
}

::ng-deep .scan-bl-wrapper .arrow-bottom-icon {
  // margin-top: 50px;
  img {
    width: 100px !important;
    height: 100px !important;
  }
}

.scan-bl-wrapper .content {
  padding: 10px 50px 0 50px;
}

.scan-bl-wrapper .content h2 {
  color: #9a9a9a;
  font-size: 22px;
  font-weight: 700;
}

.scan-bl-wrapper .content p {
  color: #9a9a9a;
  font-size: 12px;
  text-align: justify;
  padding: 5px 10px;
}

.scan-bl-content {
  --background: #ffffff;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.document-icon {
  font-size: 100px;
  color: #c4c4c4;
}

ion-label{
  margin-bottom: 20px;
}

ion-label h3{
  margin-top: 20px;
  font-weight: bold;
  color: #404040;
  font-size: 14px;
}

ion-label p{
  margin-top: 20px;
  color: #888888;
  font-size: 14px;
  margin-top: 10px;
  margin-bottom: 10px;
  opacity: 0.5;
}

h2 {
  color: #555555;
  font-size: 18px;
  margin-top: 20px;
}

.arrow-icon {
  font-size: 30px;
  color: #3b82f6; // Adjust the color as needed
}

ion-footer {
  background-color: #e5e7eb; // Adjust the background color as needed
}

.camera-button {
  --background: #3b82f6; // Adjust the color as needed
  --background-activated: #2563eb; // Adjust the color as needed
  border-radius: 50%;
  width: 60px;
  height: 60px;
  margin-top: -30px; // Position the button above the toolbar
}

ion-toolbar {
  --background: transparent;
  --ion-color-primary: #3b82f6; // Adjust the color as needed
}

ion-button {
  --color: #3b82f6; // Adjust the color as needed
}

ion-button[slot="icon-only"] {
  --color: #3b82f6; // Adjust the color as needed
}

ion-icon {
  font-size: 24px;
}

ion-footer {
  position: relative;
  background-color: #dddbff;
  height: 110px;
  // border-top: 1px solid #2563eb;
  width: 100%;

  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: row;
}

ion-footer ion-toolbar {
  --border-width: 0;
}

ion-footer ion-toolbar ion-buttons,
ion-footer ion-toolbar ion-buttons ion-button {
  display: flex;
  justify-content: space-evenly;
  align-items: center;
  flex-direction: row;
}

::ng-deep ion-button.menu-button app-custom-icon img {
  width: 30px !important;
  height: 30px !important;
  color: #000;
}

::ng-deep .menu-button.active app-custom-icon img {
  color: #2f4fcd;
}

::ng-deep .menu-button-middle {
  background-color: #2f4fcd;
  padding: 2px 12px;
  border-radius: 14px;
  width: 160px;
  height: 60px;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: row;
  margin-bottom: 15px;
  app-custom-icon img {
    width: 35px !important;
    height: 35px !important;
    color: #fff;
  }
  span{
    color: #fff;
    font-weight: 500;
    font-size: 16px;
    padding-left: 10px;
  }
}


.section-title {
  margin-top: 20px;
  margin-bottom: 0;
  color: #4b4b4b;
  font-size: 16px;
  margin-left: 16px;
  opacity: 0.5;
}

swiper-container.swiper ion-col{
  padding: 0;
  margin: 0;
  border-radius: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  // align-items: flex-start;
  justify-content: center;
  margin-top: 10px;
  margin-left: 10px;
}

.card-doc{
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  width: 90%;
  height: 100%;
  box-shadow: none;
  margin: 0;
  padding: 0;
  border: 2px solid #e5ebfd;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.card-doc img {
  width: 100%;
  height: 150px;
  object-fit: cover;
}

.card-doc ion-card-header{
  width: 100%;
  padding: 10px 20px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;

  ion-card-subtitle{
    width: 100%;
    font-size: 13px;
    text-align: left;
    font-weight: 600;
    color: #202020;
  }
}

.content-global-card{
  width: 100%;
  background-color: #fff;
  border-top: 1px solid #e5ebfd;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.card-doc ion-card-content{
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding-bottom: 10px;

  span:nth-child(1n){
    color: #4b4b4b;
    font-size: 14px;
    opacity: 0.5;
  }
  span:nth-child(2n){
    color: #070707;
    font-size: 14px;
    font-weight: 500;
    opacity: 1;
  }
}


/***  Swiper  ***/

.swiper {
  width: 100%;
  // height: 45%;
}

.swiper swiper-slide {
  background-position: center;
  background-size: cover;
  width: 250px;
}
// ::ng-deep swiper-slide{
//   width: 100% !important;
// }
::ng-deep .swiper swiper-slide {
  .swiper-slide-shadow, .swiper-slide-shadow-left, .swiper-slide-shadow-right{
  background: none !important;
  --background: none !important;
  }
}

.card-doc{
  left: 0px !important;
}



// card body styles

swiper-container.bottom-swiper{
  width: 100%;
}

.card-data-bl{
  width: 90%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  background-color: rgba(241, 244, 255, 0.4);
  // background-color: red;
  border-radius: 10px;
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.2);
  margin: 40px 22px;

}

.card-data-bl ion-card-content{
  padding: 0 5px;
}


::ng-deep .card-data-bl ion-item{
  font-size: 16px;
  font-weight: 600;
  color: #050505;
  text-align: left;
  --background: none; /* Background color for input fields */
  background-color: none;
  padding: 0 10px;
}

ion-item::part(native){
  padding-left: 0 !important;
  --padding-start: 0px !important;
  --inner-padding-end:  0px !important;
}

::ng-deep .card-data-bl ion-item ion-input div.label-text-wrapper{
  margin-bottom: 5px;
  font-size: 14px;
  color: #1F41BB;
  font-weight: bold;
}
::ng-deep .card-data-bl ion-item ion-input div.native-wrapper{
  width: 100%;
  font-size: 12px;
  font-weight: 600;
  color: #050505;
  text-align: left;
  margin: 5px 0;
  padding: 8px 10px;
  border: 1px solid #1F41BB;
  border-radius: 10px;
  --background: #fff; /* Background color for input fields */
  background-color: #fff;
}

::ng-deep .card-data-bl ion-row:last-child ion-item ion-input div.native-wrapper{
  margin-bottom: 20px;
}

::ng-deep .card-data-bl .native-wrapper{
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  flex-direction: column !important;
  padding: 14px 10px !important;
  button{
    display: none;
  }
}


.card-data-bl ion-card-header{
  padding-top: 30px;
  padding-bottom: 10px;
  text-align: center;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-direction: row;
  width: 100%;
}

.card-data-bl ion-card-header ion-card-subtitle{
  font-size: 12px;
  font-weight: 500;
  color: #202020;
  margin-top: 5px;
}
.card-data-bl ion-card-header ion-card-title{
  font-size: 18px;
  font-weight: bold;
  color: #202020;
}

::ng-deep .card-data-bl ion-card-header{
  app-custom-icon{
    color: #202020;
    opacity: 0.5;
    cursor: pointer;
    img{
      width: 45px !important;
      height: 45px !important;
    }
  }
  app-custom-icon:first-child{
    margin-left: 10px;
  }
  app-custom-icon:last-child{
    margin-right: 10px;
  }
}

.card-data-bl .hr-card{
  width: 80%;
  height: 3px;
  border-bottom: 2px dotted #8b8b8b;
  padding: 0 20px;
  margin-top: 0;
}


// Modal
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    // background-color: rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .modal-container {
    background-color: white;
    padding: 30px;
    border-radius: 20px;
    width: 90%;
    max-width: 400px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.2);
    position: relative;
    border: 1px solid #cacbce;
  }
  
  .close-button {
    position: absolute;
    top: 15px;
    right: 15px;
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #999;
  }
  
  .modal-title {
    font-size: 24px;
    margin-bottom: 15px;
    text-align: center;
  }
  
  .modal-message {
    text-align: center;
    color: #666;
    margin-bottom: 5px;
  }
  
  .modal-buttons {
    display: flex;
    justify-content: space-between;
    margin-top: 10px;
  }
  
  .button-modal {
    padding: 12px 25px;
    border-radius: 8px;
    font-size: 16px;
    cursor: pointer;
    border: none;
  }
  
  .button-cancel {
    background-color: #f1f1f1;
    color: #333;
  }
  
  .button-success {
    background-color: #2f4fcd;
    color: white;
  }

  .btn_swal_custom {
    background: var(--ion-color-primary, #0054e9);
    padding: 15px 20px;
    color: #fff;
    text-decoration: none;
    font-weight: bold;
    border-radius: 10px;
  }

  ::ng-deep div:where(.swal2-container) div:where(.swal2-footer) {
    border-top: 0px !important;
    margin: 1em 0 10px;
  }

  ion-content {
    --offset-top : 0px !important;
    background: #fff;
    height: 80vh;
  }
  ::ng-deep .swal2-container {
    height: 100vh !important;
  }
  
  ::ng-deep .swal2-html-container{
    padding: 1em 0.6em 0.3em !important;
    ul li{
      text-align: left;
      padding: 13px 10px 0 0px;
    }
  }

  ion-content {
    --offset-top : 0px !important;
    background: #fff;
    height: 80vh;
  }
  ::ng-deep .swal2-container {
    height: 100vh !important;
  }
  
  ::ng-deep .swal2-html-container{
    padding: 1em 0.6em 0.3em !important;
    ul li{
      text-align: left;
      padding: 13px 10px 0 0px;
    }
  }

  ::ng-deep .swal2-footer {
    padding: 0 !important;
    margin: 1.5em 0.5rem 2rem;
    a{
      position: relative;
      font-size: 16px;
      font-weight: bold;
      text-decoration: none;
      padding: 20px 2%;
      background-color: #2f4fcd;
      color: #fff;
      line-height: 1.5;
      border-radius: 10px;
      margin-bottom: 10px;
      line-height: 1.5;
      box-shadow: 0 0 12px 0px #0053e5;
      animation: ping 1.2s infinite ease-out;
  
    }
    a::before {
      content: "";
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(47, 79, 205, 0.5);
      border-radius: 5px;
      transform: scale(1);
      opacity: 0;
      animation: _ngcontent-ng-c2504662896_ping-pulse 2s infinite;
    }
  }

  .required-star {
    color: red;
  }

  ::ng-deep ion-item ion-input div.label-text-wrapper{
    margin-bottom: 5px;
    font-size: 14px;
    color: #1F41BB;
    font-weight: bold;
  }
  ::ng-deep ion-item ion-input div.native-wrapper{
    width: 100%;
    font-size: 12px;
    font-weight: 600;
    color: #050505;
    text-align: left;
    margin: 5px 0;
    padding: 8px 10px;
    border: 1px solid #1F41BB;
    border-radius: 10px;
    --background: #fff; /* Background color for input fields */
    background-color: #fff;
  }

  ion-item .label-wrapper {
    width: 100%;
    touch-action: manipulation;
  }

  ion-item .label-wrapper label {
    font-size: 12px;
  }
  ::ng-deep .btn_swal_custom {
    background: var(--ion-color-primary, #0054e9);
    padding: 15px 20px;
    color: #fff;
    text-decoration: none;
    font-weight: bold;
    border-radius: 10px;
  }


  ion-input {
    --padding-start: 10px;
    --padding-end: 10px;
    --padding-top: 10px;
    --padding-bottom: 10px;

    touch-action: manipulation;
    
    &::part(native) {
      padding: 0;
      min-height: 40px;
    }
  }
  
  // Fix for iOS
  :host-context(.ios) {
    ion-input {
      --padding-top: 12px;
      --padding-bottom: 12px;
    }
  }
  
  // Ensure input is clickable
  .input-item {
    pointer-events: auto !important;
    
    ion-input {
      --background: #ffffff;
      z-index: 1;
    }
  }