// services/opencv.service.ts
import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

declare var cv: any;

@Injectable({
  providedIn: 'root'
})
export class OpenCVService {
  private openCVLoadedSubject = new BehaviorSubject<boolean>(false);
  openCVLoaded$ = this.openCVLoadedSubject.asObservable();

  constructor() {
    this.initOpenCV();
  }

  private initOpenCV() {
    // Check if OpenCV is already loaded
    if (typeof window !== 'undefined' && (window as any).cv) {
      console.log('OpenCV already available');
      this.openCVLoadedSubject.next(true);
      return;
    }

    // If not loaded, create a promise to wait for it
    const loadOpenCV = new Promise<void>((resolve) => {
      (window as any).onOpenCVReady = () => {
        console.log('OpenCV is ready');
        resolve();
      };
    });
    

    // Wait for OpenCV to be ready
    loadOpenCV.then(() => {
      this.openCVLoadedSubject.next(true);
    });
  }

  public getOpenCV(): Promise<any> {
    return new Promise((resolve) => {
      if (this.openCVLoadedSubject.value) {
        resolve((window as any).cv);
      } else {
        this.openCVLoaded$.subscribe((loaded) => {
          if (loaded) {
            resolve((window as any).cv);
          }
        });
      }
    });
  }
}
