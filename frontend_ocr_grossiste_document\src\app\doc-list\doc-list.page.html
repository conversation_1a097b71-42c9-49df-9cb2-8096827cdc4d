<ion-header [ngClass]="{'loading': isLoading}">
  <ion-toolbar>
    <ion-title>Liste des pages</ion-title>
    <ion-buttons slot="end">
      <ion-button (click)="scan_bl()">
        <app-custom-icon
          name="camera-plus"
          class="camera-plus"
        ></app-custom-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content [ngClass]="{'loading': isLoading}" class="doc-list-wrapper">
  <app-check-network></app-check-network>
  <!-- <h2 class="section-title">Récent</h2>
  <swiper-container effect="cards" grab-cursor="true" class="swiper" #swiperContainer >
    <swiper-slide *ngFor="let slide of slidesData" class="test">
      <ion-row>
        <ion-col size="12" class="slide-content">
          <ion-card class="card-doc">
            <img [src]="slide.filtered_image" class="slide-image" />
            <div class="content-global-card">
              <ion-card-header>
                <ion-card-subtitle>{{ slide.title }}</ion-card-subtitle>
              </ion-card-header>
              <ion-card-content>
                <span>{{ slide.date }}</span>
                <span>Page {{ slide.page_index }}</span>
              </ion-card-content>
            </div>
          </ion-card>
        </ion-col>
      </ion-row>
    </swiper-slide>
  </swiper-container> -->

  <h2 class="section-title">Pages</h2>
  <div class="doc-list">
    <ion-item-sliding class="document-card" *ngFor="let slide of slidesData; let i = index">
      <ion-item>
        <ion-thumbnail slot="start">
          <img [src]="slide.filtered_image" />
        </ion-thumbnail>
        <ion-label>
          <h3>{{ slide.title }}</h3>
          <p>{{ slide.date }}</p>
        </ion-label>
        <div class="page-count">Page {{ slide.page_index }}</div>
      </ion-item>
      <ion-item-options side="end">
        <ion-item-option (click)="renameCard(i)" class="renameCard">
          <div class="content-item-option">
            <ion-icon slot="start" name="create-outline"></ion-icon>
            <span>Renommer</span>
          </div>
        </ion-item-option>
        <ion-item-option (click)="confirmDeleteCard(i)" class="confirmDeleteCard">
          <div class="content-item-option">
            <ion-icon slot="start" name="trash"></ion-icon>
            <span>Supprimer</span>
          </div>
        </ion-item-option>
      </ion-item-options>
    </ion-item-sliding>
  </div>
</ion-content>



<div class="alert-progress" [ngClass]="{'loading': isLoading}">
  <app-custom-alert [progress]="progress"></app-custom-alert>
</div>

<ion-footer [ngClass]="{'loading': isLoading}">
  <ion-toolbar>
    <ion-buttons>
      <ion-button class="menu-button active" size="small" [routerLink]="['/process-doc']">
        <app-custom-icon name="files"></app-custom-icon>
      </ion-button>
      <ion-button class="menu-button-middle" (click)="processOcrMulti()">
        <app-custom-icon name="extract"></app-custom-icon>
        <span>EXTRAIRE</span>
      </ion-button>
      <ion-button class="menu-button" size="small" (click)="removeAllDoc()">
        <app-custom-icon name="delete"></app-custom-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-footer>
