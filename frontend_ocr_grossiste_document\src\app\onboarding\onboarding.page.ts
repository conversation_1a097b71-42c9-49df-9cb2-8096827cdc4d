import { Component, ViewChild, AfterViewInit, ElementRef, ViewEncapsulation } from '@angular/core';
import { NavController } from '@ionic/angular';
import { StorageService } from '../services/storage.service';
import { Router } from '@angular/router';

@Component({
  selector: 'app-onboarding',
  templateUrl: './onboarding.page.html',
  styleUrls: ['./onboarding.page.scss'],
  // encapsulation: ViewEncapsulation.ShadowDom
})
export class OnboardingPage implements AfterViewInit {
  @ViewChild('swiper', { static: true }) swiper: ElementRef | undefined;

  slidesData = [
    // {
    //   image: 'assets/onboarding_images/slide1.svg',
    //   title: 'Capturez vos documents facilement',
    //   description: 'Simplifiez la gestion de vos Bons de Livraison avec notre technologie OCR avancée'
    // },
    // {
    //   image: 'assets/onboarding_images/slide2.svg',
    //   title: 'Scannez vos documents',
    //   description: 'Jusqu\'à 5 pages par Bon de Livraison. Assurez-vous que toutes les pages appartiennent au même Bon de Livraison pour une meilleure précision.'
    // },
    // {
    //   image: 'assets/onboarding_images/slide3.svg',
    //   title: 'Recadrez vos documents',
    //   description: 'Utilisez notre outil de recadrage pour sélectionner uniquement les parties importantes du document avant d\'extraire les données.'
    // },
    // {
    //   image: 'assets/onboarding_images/slide4.svg',
    //   title: 'Choisissez une source d\'image',
    //   description: 'Caméra ou Galerie. Prenez une photo directement ou sélectionnez une image existante depuis votre galerie.'
    // },
    // {
    //   image: 'assets/onboarding_images/slide5.svg',
    //   title: 'Identifiez le fournisseur',
    //   description: 'Pour des résultats optimaux. Choisissez le fournisseur du document avant de soumettre le formulaire.'
    // },
    {
      image: 'assets/onboarding_images/windoc.svg',
      title: 'Scannez votre BL avec WinDoc',
      description: "Utilisez l'application WinDoc pour capturer une image claire de votre Bon de Livraison (BL). L'application détecte automatiquement les contours et améliore l'image pour une meilleure reconnaissance."
    },
    {
      image: 'assets/onboarding_images/slide2.svg',
      title: 'Extraction et vérification des données',
      description: 'WinDoc extrait automatiquement les informations clés de votre BL, comme la désignation, la quantité et le PPV. Vérifiez et complétez les informations si nécessaire avant de soumettre.'
    },
    {
      image: 'assets/onboarding_images/winpluspharm.svg',
      title: 'Retrouvez vos résultats sur WinPlusPharma',
      description: 'Une fois le BL soumis, retrouvez les résultats directement dans votre espace personnel sur WinPlusPharma.'
    },
    // Add other slides here...
  ];

  constructor(private storageService: StorageService, private navCtrl: NavController, private router: Router) {}

  ngAfterViewInit() {
    if (this.swiper && this.swiper.nativeElement) {
      const swiperInstance = this.swiper.nativeElement.swiper;
    }
  }

  skip() {
    // this.navCtrl.navigateRoot('/scan-bl');
    this.completeOnboarding();  
  }

  welcome(){
    this.navCtrl.navigateRoot('/welcome'); 
  }

  next() {
    if (this.swiper && this.swiper.nativeElement) {
      const swiperInstance = this.swiper.nativeElement.swiper;
      const currentIndex = swiperInstance.activeIndex;
      const totalSlides = swiperInstance.slides.length - 1;

      if (currentIndex === totalSlides) {
        // // Navigate to 'scan-bl' page if it's the last slide
        // this.welcome()

        // Call completeOnboarding on the last slide
        this.completeOnboarding();

      } else {
        // Otherwise, navigate to the next slide
        swiperInstance.slideNext();
      }
    }
  }

  async completeOnboarding() {
    console.log('Setting hasSeenOnboarding to true');
    // await this.storageService.set('hasSeenOnboarding', true);
    this.router.navigate(['/welcome']); // Redirect to the main app
  }
}
