
<div class="fix-wrapper">
<ion-header> </ion-header>

<ion-content >
  <div class="onboarding-wrapper">
    <swiper-container #swiper pagination="true">
      <swiper-slide *ngFor="let slide of slidesData" class="test">
        <ion-row class="img-details-content">
          <ion-col size="12" class="slide-content">
            <div class="content-slide">
              <h2>{{ slide.title }}</h2>
            </div>
            <div class="image-wrapper" data-icon="✅">
              <img
                [src]="slide.image"
                class="slide-image"
                (click)="openImageModal(slide.image)"
              />
            </div>
          </ion-col>
     
          <div class="content-slide">
            <p>{{ slide.description }}</p>
          </div>
          <ion-col class="img-details-col" [size]="12">
            <div
              *ngFor="let item of slide.images_details"
              class="image-wrapper"
              data-icon="❌"
            >
              <img
                [src]="item.image"
                class="{{item.class}}"
                (click)="openImageModal(item.image)"
              />
            </div>
          </ion-col>
        </ion-row>
      </swiper-slide>
    </swiper-container>


  </div>
</ion-content>
<ion-footer>
  <div class="buttons_nav">
    <ion-button fill="solid" class="next-button" (click)="next()">
      <span>SUIVANT</span>
      <ion-icon slot="end" name="arrow-forward-outline"></ion-icon>
    </ion-button>
    <!-- <ion-button fill="clear" class="skip-button" (click)="skip()">PASSER</ion-button> -->
  </div>
</ion-footer>
</div>