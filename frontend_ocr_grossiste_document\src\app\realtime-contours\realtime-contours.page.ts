import { Component, OnInit, ElementRef, <PERSON>Child, <PERSON><PERSON><PERSON><PERSON>, ChangeDetectorRef } from '@angular/core';
import { Platform } from '@ionic/angular';
import { OpenCVService } from '../services/open-cv.service';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-realtime-contours',
  templateUrl: './realtime-contours.page.html',
  styleUrls: ['./realtime-contours.page.scss'],
})
export class RealtimeContoursPage implements OnInit, OnDestroy {
  @ViewChild('video') video: ElementRef | undefined;
  @ViewChild('canvas') canvas: ElementRef | undefined;

  streaming = false;
  width = 640;
  height = 480;
  isOpenCVReady = false;
  private cv: any;
  private openCVSubscription: Subscription | undefined;

  constructor(
    private platform: Platform,
    private openCVService: OpenCVService,
    private changeDetector: ChangeDetectorRef
  ) {}

  async ngOnInit() {
    try {
      this.cv = await this.openCVService.getOpenCV();
      this.isOpenCVReady = true;
      this.changeDetector.detectChanges();
      await this.startCamera();
    } catch (error) {
      console.error('Error initializing OpenCV:', error);
    }
  }

  // Update your template to fix the accessibility warning:
  async startCamera() {
    if (!this.isOpenCVReady) {
      console.warn('OpenCV is not ready yet');
      return;
    }

    try {
      // Get actual device dimensions
      const constraints = {
        video: {
          facingMode: 'environment',
          width: { ideal: this.width },
          height: { ideal: this.height }
        },
        audio: false
      };

      const stream = await navigator.mediaDevices.getUserMedia(constraints);
      
      if (this.video && this.video.nativeElement) {
        const videoElement = this.video.nativeElement;
        videoElement.srcObject = stream;
        
        // Wait for video metadata to load
        videoElement.onloadedmetadata = () => {
          // Update canvas dimensions to match video
          const track = stream.getVideoTracks()[0];
          const settings = track.getSettings();
          this.width = settings.width || this.width;
          this.height = settings.height || this.height;
          
          if (this.canvas) {
            this.canvas.nativeElement.width = this.width;
            this.canvas.nativeElement.height = this.height;
          }
          
          videoElement.play();
          this.streaming = true;
          this.processVideo();
        };
      }
    } catch (err) {
      console.error('Error accessing camera:', err);
    }
  }

  
  processVideo() {
    try {
      if (!this.streaming) return;

      const video = this.video!.nativeElement;
      const canvas = this.canvas!.nativeElement;
      const context = canvas.getContext('2d');
      
      // Draw video frame to canvas
      context.drawImage(video, 0, 0, this.width, this.height);
      
      // Get image data from canvas
      let src = this.cv.imread(canvas);
      let dst = src.clone();
      
      // Blur the image to enhance edge detection
      let blurred = new this.cv.Mat();
      this.cv.medianBlur(src, blurred, 9);
      
      let squares: any[] = [];
      
      // Find squares in every color plane of the image
      for (let c = 0; c < 3; c++) {
        let gray0 = new this.cv.Mat();
        let gray = new this.cv.Mat();
        
        // Extract the c-th color plane
        let ch = [c, 0];
        this.cv.extractChannel(blurred, gray0, c);
        
        // Try several threshold levels
        for (let l = 0; l < 2; l++) {
          if (l === 0) {
            // Use Canny
            this.cv.Canny(gray0, gray, 10, 20, 3);
            // Dilate to remove potential holes between edge segments
            let kernel = this.cv.getStructuringElement(this.cv.MORPH_RECT, new this.cv.Size(3, 3));
            this.cv.dilate(gray, gray, kernel, new this.cv.Point(-1, -1));
            kernel.delete();
          } else {
            // Use simple thresholding
            this.cv.threshold(gray0, gray, (l + 1) * 255 / 2, 255, this.cv.THRESH_BINARY);
          }
          
          // Find contours
          let contours = new this.cv.MatVector();
          let hierarchy = new this.cv.Mat();
          this.cv.findContours(gray, contours, hierarchy, this.cv.RETR_LIST, this.cv.CHAIN_APPROX_SIMPLE);
          
          // Test each contour
          for (let i = 0; i < contours.size(); i++) {
            let contour = contours.get(i);
            let approx = new this.cv.Mat();
            let perimeter = this.cv.arcLength(contour, true);
            
            // Approximate contour with accuracy proportional to the contour perimeter
            this.cv.approxPolyDP(contour, approx, 0.02 * perimeter, true);
            
            // Square contours should have 4 vertices after approximation
            // Relatively large area (to filter out noisy contours)
            // And be convex
            let area = Math.abs(this.cv.contourArea(approx));
            
            if (approx.rows === 4 && 
                area > 1000 && 
                area < (this.width * this.height * 0.9) && // Add maximum area constraint
                this.cv.isContourConvex(approx)) {
              
              // Check if angles are approximately 90 degrees
              let maxCosine = 0;
              let points = [];
              
              // Get points from approx
              for (let j = 0; j < 4; j++) {
                points.push({
                  x: approx.data32S[j * 2],
                  y: approx.data32S[j * 2 + 1]
                });
              }
              
              // Check angles
              for (let j = 2; j < 5; j++) {
                let cosine = Math.abs(this.angle(
                  points[j % 4],
                  points[j - 2],
                  points[j - 1]
                ));
                maxCosine = Math.max(maxCosine, cosine);
              }
              
              // If all angles are approximately 90 degrees (cos < 0.3)
              if (maxCosine < 0.3) {
                // Draw the square
                this.cv.drawContours(
                  dst,
                  new this.cv.MatVector([approx]),
                  -1,
                  new this.cv.Scalar(0, 255, 0, 255),
                  3
                );
                
                // Draw corners
                points.forEach(point => {
                  this.cv.circle(
                    dst,
                    new this.cv.Point(point.x, point.y),
                    10,
                    new this.cv.Scalar(255, 0, 0, 255),
                    -1
                  );
                });
              }
            }
            approx.delete();
          }
          
          contours.delete();
          hierarchy.delete();
        }
        
        gray0.delete();
        gray.delete();
      }
      
      // Show result
      this.cv.imshow(canvas, dst);
      
      // Cleanup
      src.delete();
      dst.delete();
      blurred.delete();
      
      // Continue processing
      requestAnimationFrame(() => this.processVideo());
      
    } catch (err) {
      console.error('Error in processVideo:', err);
    }
}

// Helper function to calculate angle
angle(pt1: { x: number, y: number }, 
      pt2: { x: number, y: number }, 
      pt0: { x: number, y: number }) {
    const dx1 = pt1.x - pt0.x;
    const dy1 = pt1.y - pt0.y;
    const dx2 = pt2.x - pt0.x;
    const dy2 = pt2.y - pt0.y;
    
    return (dx1 * dx2 + dy1 * dy2) / 
           Math.sqrt((dx1 * dx1 + dy1 * dy1) * (dx2 * dx2 + dy2 * dy2) + 1e-10);
}


  // processVideo() {
  //   try {
  //     const video = this.video!.nativeElement;
  //     const canvas = this.canvas!.nativeElement;
  //     const context = canvas.getContext('2d');
      
  //     if (this.streaming) {
  //       // Draw video frame to canvas
  //       context.drawImage(video, 0, 0, this.width, this.height);
        
  //       // Get image data from canvas
  //       let imageData = context.getImageData(0, 0, this.width, this.height);
  //       let src = this.cv.matFromImageData(imageData);
  //       let dst = new this.cv.Mat();
  //       let gray = new this.cv.Mat();
  //       let edges = new this.cv.Mat();
        
  //       // Convert to grayscale
  //       this.cv.cvtColor(src, dst, this.cv.COLOR_RGBA2GRAY);
        
  //       // Apply Gaussian blur
  //       this.cv.GaussianBlur(dst, dst, new this.cv.Size(5, 5), 0);
        
  //       // Apply Canny edge detection
  //       this.cv.Canny(dst, dst, 75, 200);
        
  //       // Find contours
  //       let contours = new this.cv.MatVector();
  //       let hierarchy = new this.cv.Mat();
  //       this.cv.findContours(dst, contours, hierarchy, this.cv.RETR_EXTERNAL, this.cv.CHAIN_APPROX_SIMPLE);
        
  //       // Convert back to RGB for drawing
  //       this.cv.cvtColor(dst, dst, this.cv.COLOR_GRAY2RGBA);
        
  //       // Copy original image
  //       src.copyTo(dst);

  //       // Find the largest contour that could be a document
  //       let maxArea = 0;
  //       let maxContourIndex = -1;
  //       let documentContour = null;
        
  //       for (let i = 0; i < contours.size(); i++) {
  //         const contour = contours.get(i);
  //         const area = this.cv.contourArea(contour);
  //         const perimeter = this.cv.arcLength(contour, true);
  //         let approx = new this.cv.Mat();
  //         this.cv.approxPolyDP(contour, approx, 0.02 * perimeter, true);
          
  //         if (area > maxArea && approx.rows === 4) {
  //           maxArea = area;
  //           documentContour = approx;
  //           maxContourIndex = i;
  //         }
  //         approx.delete();
  //       }
        
  //       // Draw the largest contour if found
  //       if (maxContourIndex !== -1) {
  //         this.cv.drawContours(dst, contours, maxContourIndex, new this.cv.Scalar(0, 255, 0, 255), 2);
  //       }
        
  //       // Show result on canvas
  //       this.cv.imshow(canvas, dst);
        
  //       // Clean up
  //       src.delete();
  //       dst.delete();
  //       gray.delete();
  //       edges.delete();
  //       contours.delete();
  //       hierarchy.delete()
  //     }
      
  //     // Process next frame
  //     requestAnimationFrame(() => this.processVideo());
  //   } catch (err) {
  //     console.error('Error processing video:', err);
  //   }
  // }
  
  isValidDocument(vertices: any[]) {
    // Check aspect ratio (approximate A4 ratio 1:√2)
    const width = this.dist(vertices[0], vertices[1]);
    const height = this.dist(vertices[1], vertices[2]);
    const ratio = width > height ? width/height : height/width;
    return ratio > 1.3 && ratio < 1.5;
  }
  
  // Helper functions
  getCorners(approx: { data32S: any[]; }) {
    const corners = [];
    for (let i = 0; i < 4; i++) {
      corners.push({
        x: approx.data32S[i * 2],
        y: approx.data32S[i * 2 + 1]
      });
    }
    return corners;
  }
  
  dist(p1: { x: number; y: number; }, p2: { x: number; y: number; }) {
    return Math.sqrt((p2.x - p1.x)**2 + (p2.y - p1.y)**2);
  }

  ngOnDestroy() {
    this.streaming = false;
    if (this.video?.nativeElement.srcObject) {
      this.video.nativeElement.srcObject.getTracks().forEach((track: any) => track.stop());
    }
    if (this.openCVSubscription) {
      this.openCVSubscription.unsubscribe();
    }
  }
}