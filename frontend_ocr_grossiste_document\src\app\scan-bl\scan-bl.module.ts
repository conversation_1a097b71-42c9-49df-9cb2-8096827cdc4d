import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { ScanBLPageRoutingModule } from './scan-bl-routing.module';

import { ScanBLPage } from './scan-bl.page';
import { SharedModule } from '../shared/shared.module'; // Import SharedModule
import { ScannerService } from '../services/scanner.service';
import { ScannerModalComponent } from '../components/scanner-modal/scanner-modal.component';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    ScanBLPageRoutingModule,
    SharedModule
  ],
  declarations: [ScanBLPage, ScannerModalComponent],
  providers: [
    ScannerService
  ]
})
export class ScanBLPageModule {}
