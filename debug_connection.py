#!/usr/bin/env python3
"""
Debug script to test PostgreSQL connection and identify encoding issues.
"""

import os
import sys
from dotenv import load_dotenv
from urllib.parse import quote_plus

# Load environment variables
load_dotenv('.env.prod')

# Get database configuration
POSTGRES_HOST = os.getenv('POSTGRES_HOST', 'localhost')
POSTGRES_PORT = os.getenv('POSTGRES_PORT', '5432')
POSTGRES_DB = os.getenv('POSTGRES_DB', 'ocr_document_grossiste')
POSTGRES_USER = os.getenv('POSTGRES_USER', 'postgres')
POSTGRES_PASSWORD = os.getenv('POSTGRES_PASSWORD', 'postgres')
POSTGRES_SSL_MODE = os.getenv('POSTGRES_SSL_MODE', 'prefer')

print("=== Database Configuration Debug ===")
print(f"Host: {POSTGRES_HOST}")
print(f"Port: {POSTGRES_PORT}")
print(f"Database: {POSTGRES_DB}")
print(f"User: {POSTGRES_USER}")
print(f"Password: {POSTGRES_PASSWORD}")
print(f"SSL Mode: {POSTGRES_SSL_MODE}")
print()

# Check for encoding issues in each component
print("=== Encoding Check ===")
components = {
    'host': POSTGRES_HOST,
    'port': POSTGRES_PORT,
    'database': POSTGRES_DB,
    'user': POSTGRES_USER,
    'password': POSTGRES_PASSWORD,
    'ssl_mode': POSTGRES_SSL_MODE
}

for name, value in components.items():
    try:
        encoded = value.encode('utf-8')
        print(f"{name}: OK - {value}")
    except UnicodeEncodeError as e:
        print(f"{name}: ERROR - {e}")
        print(f"  Raw value: {repr(value)}")

print()

# Test URL encoding
print("=== URL Encoding Test ===")
try:
    encoded_password = quote_plus(POSTGRES_PASSWORD)
    print(f"Original password: {POSTGRES_PASSWORD}")
    print(f"URL-encoded password: {encoded_password}")
    
    # Build connection URL
    DATABASE_URL = f"postgresql://{POSTGRES_USER}:{encoded_password}@{POSTGRES_HOST}:{POSTGRES_PORT}/{POSTGRES_DB}?sslmode={POSTGRES_SSL_MODE}"
    print(f"Connection URL: {DATABASE_URL}")
    print()
    
    # Test connection
    print("=== Connection Test ===")
    import psycopg2
    
    conn = psycopg2.connect(
        host=POSTGRES_HOST,
        port=POSTGRES_PORT,
        database=POSTGRES_DB,
        user=POSTGRES_USER,
        password=POSTGRES_PASSWORD
    )
    
    cursor = conn.cursor()
    cursor.execute("SELECT version();")
    version = cursor.fetchone()
    print(f"✅ Connection successful!")
    print(f"PostgreSQL version: {version[0]}")
    
    cursor.close()
    conn.close()
    
except Exception as e:
    print(f"❌ Connection failed: {e}")
    print(f"Error type: {type(e).__name__}")
    
    # Check if it's a UnicodeDecodeError
    if isinstance(e, UnicodeDecodeError):
        print(f"Encoding: {e.encoding}")
        print(f"Object: {repr(e.object)}")
        print(f"Start: {e.start}")
        print(f"End: {e.end}")
        print(f"Reason: {e.reason}")
