@import url("https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap");

* {
  font-family: "Inter", sans-serif;
  font-optical-sizing: auto;
}

ion-content::part(scroll) {
  overflow-y: hidden !important;
  --overflow: hidden !important;
}

ion-header {
  height: 80px;
  --border: 0;
  display: flex;
  align-items: center;
    justify-content: center;
    gap: 1rem;

}
ion-header ion-toolbar {
  --border: 0;
  --border-width: 0;
  display: flex;
  justify-content: center;
  align-items: flex-end;
  flex-direction: row;
}

ion-header ion-toolbar ion-title {
    font-size: 22px;
    font-weight: 700;
    color: #2f4fcd;
    text-align: left;
    width: 100%;
    padding-left: 2rem;
  }
 

ion-header ion-toolbar ion-icon {
    color: #101010;
    padding-right: 1rem;
  }


  ion-toolbar {
    --background: transparent;
    --ion-color-primary: #3b82f6; // Adjust the color as needed
  }
  
  ion-button {
    --color: #3b82f6; // Adjust the color as needed
  }
  
  ion-button[slot="icon-only"] {
    --color: #3b82f6; // Adjust the color as needed
  }

 


  .profile-container {
    // padding: 16px;
  }

  .profile-avatar {
    width: 120px;
    height: 120px;
    margin: 20px auto;
  }

  .user-name {
    text-align: center;
    margin: 10px 0 5px;
    font-weight: bold;
  }

  .user-email {
    text-align: center;
    margin: 0 0 20px;
    color: var(--ion-color-medium);
  }

  ion-list {
    margin-top: 20px;
    border: 1px solid #ece7e7;
  }

  /* Container styling */
ion-list {
    background: white;
    border-radius: 8px;
    margin: 12px;
    margin-top: 50px;
    box-shadow: 
      0 1px 3px rgba(0,0,0,0.02),
      0 1px 2px rgba(0,0,0,0.04);
    padding: 4px 0;
  }
  
  /* Individual item styling */
  ion-item {
    --background: transparent;
    --padding-start: 12px;
    --padding-end: 8px;
    --min-height: 50px;
    --border-color: transparent;
    --inner-padding-end: 8px;
    
    /* Crisp transitions */
    transition: all 0.2s ease;
  }
  
  /* Tight hover effect */
  ion-item:hover {
    --background: #fafafa;
  }
  
  /* Compact icon styling */
  ion-item ion-icon {
    color: #4b5563;
    font-size: 18px;
    margin: 0;
    padding-right: 10px;
    transition: color 0.2s ease;
  }
  
  /* Icon hover animation */
  ion-item:hover ion-icon {
    color: #3880ff;
  }
  
  /* Condensed label styling */
  ion-item ion-label {
    color: #374151;
    font-weight: 500;
    font-size: 14px;
    margin: 0;
    padding: 8px 0;
  }
  
  /* Subtle detail arrow */
  ion-item::part(detail-icon) {
    color: #9ca3af;
    opacity: 0.6;
    font-size: 14px;
  }
  
  /* Active state */
  ion-item:active {
    --background: #f5f5f5;
  }
  
  /* Remove border styles */
  ion-list ion-item  {
    --border-width-bottom: 1;
    --border-color:rgb(231, 230, 230);
  }


  


  ::ng-deep .custom-alert {
    --max-width: 90%;
    --width: 100%;
  
    .alert-head, #alert-1-msg .about-content p, #alert-1-msg .about-content ul li, #alert-1-msg .about-content h4,
    .alert-2-head, #alert-2-msg .privacy-content p, #alert-2-msg .privacy-content ul li {
      text-align: left !important;
    }

    .about-content{
      text-align: left !important;
    }

    .privacy-content{
      text-align: left !important;
    }
    
    .alert-wrapper {
      max-width: 90vw;
    }
  
    .alert-message {
      max-height: 70vh;
      overflow-y: auto;
      padding: 16px;
    }
  
    .about-content, .privacy-content {
      h4 {
        color: #2f4fcd;
        margin: 16px 0 8px;
        font-size: 16px;
        font-weight: 600;
      }
  
      p {
        margin: 8px 0;
        font-size: 14px;
        line-height: 1.5;
        color: #4a4a4a;
      }
  
      ul {
        margin: 8px 0;
        padding-left: 20px;
  
        li {
          margin: 4px 0;
          font-size: 14px;
          color: #4a4a4a;
        }
      }
  
      .privacy-link {
        display: block;
        margin-top: 16px;
        color: #2f4fcd;
        text-decoration: underline;
        cursor: pointer;
        font-size: 13px;
      }
    }
  }
  