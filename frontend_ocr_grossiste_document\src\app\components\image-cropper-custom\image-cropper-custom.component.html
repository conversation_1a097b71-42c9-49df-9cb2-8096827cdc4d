<div class="cropper-container" [style.width]="width" [style.height]="height">
  <svg
    #svgElement
    [attr.viewBox]="getViewBox()"
    (touchmove)="handleTouchMove($event)"
    (touchend)="stopDragging()"
    (touchcancel)="stopDragging()"
    (mouseleave)="stopDragging()"
    class="cropper-svg"
  >
    <!-- Main image -->
    <g [attr.transform]="getRotationTransform()">
      <image
        *ngIf="img"
        [attr.href]="img?.src"
        [attr.width]="currentWidth"
        [attr.height]="currentHeight"
        preserveAspectRatio="xMidYMid meet"
        style="image-rendering: optimizeQuality"
      />
    </g>

    <!-- Semi-transparent overlay -->
    <path
      [attr.d]="getOverlayPath()"
      class="overlay"
      fill="rgba(0, 0, 0, 0.5)"
    />

    <!-- Crop area -->
    <g [attr.transform]="getCropTransform()">
      <!-- Polygon outline -->
      <polygon
        [attr.points]="getPolygonPoints()"
        class="crop-outline"
        (touchstart)="onPolygonTouchStart($event)"
      />

      <!-- Handle points with larger touch areas -->
      <g *ngFor="let point of quad?.points; let i = index">
        <!-- Invisible larger touch area -->
        <circle
          [attr.data-index]="i"
          [attr.cx]="point.x"
          [attr.cy]="point.y"
          r="160"
          class="handle-touch-area"
          [attr.pointer-events]="'all'"
          (pointerdown)="startDragging($event, i)"
        />

        <!-- Visible point -->
        <circle
          [attr.data-index]="i"
          [attr.cx]="point.x"
          [attr.cy]="point.y"
          r="60"
          [class.touch-active]="activePoint === i"
          class="handle-point"
          [attr.pointer-events]="'none'"
        />
      </g>

      <g *ngFor="let midpoint of midpoints; let j = index">
        <rect
          [attr.data-index]="'mid-' + j"
          [attr.width]="getMidpointDimensions(j).width"
          [attr.height]="getMidpointDimensions(j).height"
          [attr.x]="getMidpointX(midpoint, j)"
          [attr.y]="getMidpointY(midpoint, j)"
          [attr.rx]="getMidpointRadius(j)"
          [attr.ry]="getMidpointRadius(j)"
          class="midpoint-handle"
          [class.touch-active]="activeMidpoint === j"
          (pointerdown)="startMidpointDragging($event, j)"
        />
      </g>
    </g>

    <!-- Magnifier -->
    <g *ngIf="showMagnifier" class="magnifier">
      <!-- Background circle -->
      <circle
        [attr.cx]="magnifierPosition.x"
        [attr.cy]="magnifierPosition.y"
        [attr.r]="magnifierRadius"
        class="magnifier-background"
      />

      <!-- Clipping mask -->
      <clipPath id="magnifierClip">
        <circle
          [attr.cx]="magnifierPosition.x"
          [attr.cy]="magnifierPosition.y"
          [attr.r]="magnifierRadius"
        />
      </clipPath>

      <!-- Zoomed content -->
      <g clip-path="url(#magnifierClip)">
        <!-- Base image with rotation -->
        <g [attr.transform]="getZoomTransform()">
          <image
            *ngIf="img"
            [attr.href]="img?.src"
            [attr.width]="currentWidth"
            [attr.height]="currentHeight"
            preserveAspectRatio="xMidYMid meet"
          />

          <!-- Intersection lines -->
          <ng-container *ngIf="activePoint !== null">
            <!-- Vertical line -->
            <line
              [attr.x1]="quad.points[activePoint].x"
              [attr.y1]="quad.points[activePoint].y - 50"
              [attr.x2]="quad.points[activePoint].x"
              [attr.y2]="quad.points[activePoint].y + 50"
              class="magnifier-intersection-line"
            />

            <!-- Horizontal line -->
            <line
              [attr.x1]="quad.points[activePoint].x - 50"
              [attr.y1]="quad.points[activePoint].y"
              [attr.x2]="quad.points[activePoint].x + 50"
              [attr.y2]="quad.points[activePoint].y"
              class="magnifier-intersection-line"
            />
          </ng-container>
        </g>
      </g>

      <!-- Border -->
      <circle
        [attr.cx]="magnifierPosition.x"
        [attr.cy]="magnifierPosition.y"
        [attr.r]="magnifierRadius"
        class="magnifier-border"
      />
    </g>
  </svg>
</div>
