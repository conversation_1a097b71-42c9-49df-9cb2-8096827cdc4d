#!/usr/bin/env python3
"""
PostgreSQL Database Initialization Script

This script initializes the PostgreSQL database for the OCR Document Grossiste project.
It creates the necessary tables and sets up the database structure.

Usage:
    python scripts/init_postgresql.py

Environment Variables Required:
    POSTGRES_HOST - PostgreSQL server host
    POSTGRES_PORT - PostgreSQL server port  
    POSTGRES_DB - Database name
    POSTGRES_USER - Database user
    POSTGRES_PASSWORD - Database password
"""

import sys
import os
import logging
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.app.config import POSTGRES_URL
from src.app.database.connection import db_manager
from src.app.database.models import Base

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def check_environment():
    """Check if all required environment variables are set."""
    required_vars = [
        'POSTGRES_HOST',
        'POSTGRES_PORT', 
        'POSTGRES_DB',
        'POSTGRES_USER',
        'POSTGRES_PASSWORD'
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        logger.error(f"Missing required environment variables: {', '.join(missing_vars)}")
        logger.error("Please set these variables before running the script.")
        return False
    
    return True


def test_connection():
    """Test the PostgreSQL connection."""
    try:
        logger.info("Testing PostgreSQL connection...")
        health = db_manager.health_check()
        
        if health['postgresql']['available']:
            logger.info("✅ PostgreSQL connection successful")
            return True
        else:
            logger.error(f"❌ PostgreSQL connection failed: {health['postgresql']['error']}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Failed to test PostgreSQL connection: {e}")
        return False


def create_database_tables():
    """Create all database tables."""
    try:
        logger.info("Creating PostgreSQL database tables...")
        
        # Create tables using SQLAlchemy
        if db_manager.postgres_engine:
            Base.metadata.create_all(bind=db_manager.postgres_engine)
            logger.info("✅ PostgreSQL tables created successfully")
            
            # List created tables
            from sqlalchemy import text
            with db_manager.postgres_engine.connect() as conn:
                result = conn.execute(text("""
                    SELECT table_name
                    FROM information_schema.tables
                    WHERE table_schema = 'public'
                    ORDER BY table_name
                """))
                tables = [row[0] for row in result]
                logger.info(f"Created tables: {', '.join(tables)}")
            
            return True
        else:
            logger.error("❌ PostgreSQL engine not available")
            return False
            
    except Exception as e:
        logger.error(f"❌ Failed to create database tables: {e}")
        return False


def verify_tables():
    """Verify that all expected tables exist."""
    try:
        logger.info("Verifying database tables...")
        
        expected_tables = ['pre_bl_ocr', 'processing_logs', 'supplier_configs']
        
        from sqlalchemy import text
        with db_manager.postgres_engine.connect() as conn:
            result = conn.execute(text("""
                SELECT table_name
                FROM information_schema.tables
                WHERE table_schema = 'public'
                ORDER BY table_name
            """))
            existing_tables = [row[0] for row in result]
        
        missing_tables = set(expected_tables) - set(existing_tables)
        
        if missing_tables:
            logger.warning(f"⚠️  Missing tables: {', '.join(missing_tables)}")
        else:
            logger.info("✅ All expected tables exist")
        
        # Check main table structure
        with db_manager.postgres_engine.connect() as conn:
            result = conn.execute(text("""
                SELECT column_name, data_type, is_nullable
                FROM information_schema.columns
                WHERE table_name = 'pre_bl_ocr'
                ORDER BY ordinal_position
            """))

            columns = list(result)
            logger.info(f"pre_bl_ocr table has {len(columns)} columns")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to verify tables: {e}")
        return False


def main():
    """Main initialization function."""
    logger.info("🚀 Starting PostgreSQL database initialization...")
    
    # Check environment variables
    if not check_environment():
        sys.exit(1)
    
    logger.info(f"Database URL: {POSTGRES_URL.split('@')[0]}@***")
    
    # Test connection
    if not test_connection():
        sys.exit(1)
    
    # Create tables
    if not create_database_tables():
        sys.exit(1)
    
    # Verify tables
    if not verify_tables():
        sys.exit(1)
    
    logger.info("🎉 PostgreSQL database initialization completed successfully!")
    logger.info("You can now run the application with dual database support.")


if __name__ == "__main__":
    main()
