{"version": 3, "names": ["imageCropperCss", "ImageCropperStyle0", "ImageCropper", "this", "handlers", "polygonMouseDown", "polygonMouseDownPoint", "x", "y", "previousDistance", "undefined", "svgMouseDownPoint", "handlerMouseDownPoint", "originalPoints", "usingTouchEvent", "usingQuad", "componentDidLoad", "containerElement", "addEventListener", "e", "onContainerTouchMove", "hideMagnifier", "watchImgPropHandler", "newValue", "console", "log", "resetStates", "viewBox", "naturalWidth", "naturalHeight", "root", "inActiveStroke", "parseInt", "style", "getPropertyValue", "activeStroke", "watchRectPropHandler", "points", "getPointsFromRect", "img", "restrainPointsInBounds", "rect", "point1", "point2", "width", "point3", "height", "point4", "watchQuadPropHandler", "onCanceled", "canceled", "emit", "onConfirmed", "confirmed", "getPointsData", "pointsData", "renderFooter", "hidefooter", "h", "class", "onClick", "src", "rendenInactiveSelections", "inactiveSelections", "Fragment", "map", "selection", "index", "getPointsDataFromSelection", "getRatio", "fill", "onMouseUp", "onSelectionClicked", "onTouchStart", "selectionClicked", "renderHandlers", "getHandlerPos", "getHandlerSize", "selectedHandlerIndex", "onMouseDown", "onHandlerMouseDown", "onHandlerMouseUp", "onHandlerTouchStart", "onPointerDown", "onHandlerPointerDown", "key", "pos", "size", "ratio", "handlersize", "error", "Math", "ceil", "onSVGTouchStart", "coord", "getMousePosition", "svgElement", "touches", "length", "JSON", "parse", "stringify", "onSVGTouchEnd", "onSVGTouchMove", "stopPropagation", "preventDefault", "pinchAndZoom", "panSVG", "handleMoveEvent", "distance", "getDistanceBetweenTwoTouches", "scale", "min", "max", "touch1", "touch2", "offsetX", "clientX", "offsetY", "clientY", "onContainerMouseUp", "onSVGMouseDown", "onContainerWheel", "deltaY", "getPanAndZoomStyle", "rotation", "onSVGMouseMove", "newPoints", "point", "showMagnifier", "updateMagnifier", "pointIndex", "getPointIndexFromHandlerIndex", "selectedPoint", "imgWidth", "imgHeight", "onPolygonMouseDown", "onPolygonMouseUp", "onPolygonTouchStart", "onPolygonTouchEnd", "pointerType", "event", "svg", "CTM", "getScreenCTM", "targetTouches", "det", "a", "d", "b", "c", "invCTM", "f", "getAllSelections", "convertTo", "all", "getRectFromPoints", "push", "useQuad", "quad", "getQuad", "getRect", "getPoints", "minX", "minY", "maxX", "maxY", "floor", "getImageFromBlob", "source", "Promise", "resolve", "reject", "reader", "FileReader", "readAsDataURL", "onloadend", "dataURL", "result", "document", "createElement", "onload", "onerror", "getImageFromDataURL", "detect", "getSVGWidth", "imgRatio", "clientHeight", "parentElement", "clientWidth", "onSVGPointerMove", "onSVGPointerDown", "onSVGPointerUp", "onPolygonPointerDown", "onPolygonPointerUp", "render", "Host", "ref", "el", "onWheel", "canvasElement", "version", "xmlns", "transform", "onMouseMove", "onTouchEnd", "onTouchMove", "onPointerMove", "onPointerUp", "href", "magnifierElement", "display", "magnifierSize", "magnifierLeft", "magnifierTop", "createSVGPoint", "ctm", "transformedPoint", "matrixTransform", "left", "top", "zoomLevel", "sx", "sy", "sw", "sh", "dx", "dy", "dw", "dh", "magnifier<PERSON>anvas", "magnifierCtx", "getContext", "drawImage", "strokeStyle", "lineWidth", "beginPath", "moveTo", "i", "lineTo", "closePath", "stroke", "backgroundImage", "toDataURL"], "sources": ["src/components/image-cropper/image-cropper.css?tag=image-cropper&encapsulation=shadow", "src/components/image-cropper/image-cropper.tsx"], "sourcesContent": [":host {\r\n  /* --active-color: rgb(5, 197, 175); */\r\n    /* --inactive-color: gray; */\r\n  --active-color:orange;\r\n  --inactive-color:orange;\r\n  --active-stroke: 4;\r\n  --inactive-stroke: 4;\r\n  --main-background: transparent;\r\n  display: block;\r\n  position: relative;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n* {\r\n  user-select:none;\r\n  -webkit-user-select:none;\r\n  -moz-user-select:none;\r\n}\r\n\r\n.container {\r\n  display: flex;\r\n  justify-content: center;\r\n  background: var(--main-background);\r\n  overflow: hidden;\r\n}\r\n\r\n.absolute {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.cropper-controls {\r\n  stroke:var(--active-color);\r\n}\r\n\r\n.footer {\r\n  position: absolute;\r\n  left: 0;\r\n  bottom: 0;\r\n  height: 100px;\r\n  width: 100%;\r\n  pointer-events: none;\r\n}\r\n\r\n.items {\r\n  box-sizing: border-box;\r\n  display: flex;\r\n  width: 100%;\r\n  height: 100%;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 2.0em;\r\n}\r\n\r\n.items .item {\r\n  flex: 1;\r\n  text-align: center;\r\n}\r\n.items .item:first-child {\r\n  text-align: left;\r\n}\r\n.items .item:last-child {\r\n  text-align: right;\r\n}\r\n\r\n.accept-use img {\r\n  width: 2.5em;\r\n  height: 2.5em;\r\n  pointer-events: all;\r\n  cursor:pointer;\r\n}\r\n\r\n.accept-cancel img {\r\n  width: 2.5em;\r\n  height: 2.5em;\r\n  pointer-events: all;\r\n  cursor:pointer;\r\n}\r\n\r\n.cropper-svg {\r\n  align-self: center;\r\n  touch-action: none;\r\n  cursor:grab;\r\n}\r\n\r\n.cropper-svg polygon {\r\n  cursor:move;\r\n}\r\n\r\n.cropper-svg rect {\r\n  cursor:grab;\r\n}\r\n\r\n.hidden-canvas {\r\n  display: none;\r\n}\r\n\r\n.cropper-svg .inactive-selection {\r\n  stroke:var(--inactive-color);\r\n  cursor:pointer;\r\n}\r\n\r\n.dashed {\r\n  stroke-dasharray:10,10;\r\n}\r\n\r\n\r\n.magnifier {\r\n  position: absolute;\r\n  width: 100px;\r\n  height: 100px;\r\n  left: 0;\r\n  top: 0;\r\n  border: 1px solid #00bceb;\r\n  border-radius: 50%;\r\n  overflow: hidden;\r\n  display: none;\r\n  pointer-events: none;\r\n  background-size: 100%;\r\n  background-repeat: no-repeat;\r\n}", "import { Component, Event, EventEmitter, Fragment, Host, Method, Prop, State, Watch, h } from '@stencil/core';\r\n\r\nexport interface DetectedQuadResult{\r\n  location: Quad;\r\n  confidenceAsDocumentBoundary: number;\r\n}\r\n\r\nexport interface Quad{\r\n  points:[Point,Point,Point,Point];\r\n}\r\n\r\nexport interface Point{\r\n  x:number;\r\n  y:number;\r\n}\r\n\r\nexport interface Rect{\r\n  x:number;\r\n  y:number;\r\n  width:number;\r\n  height:number;\r\n}\r\n\r\nexport interface CropOptions {\r\n  perspectiveTransform?:boolean;\r\n  colorMode?:\"binary\"|\"gray\"|\"color\";\r\n  selection?:Quad|Rect;\r\n  source?:Blob|string|HTMLImageElement|HTMLCanvasElement;\r\n}\r\n\r\n@Component({\r\n  tag: 'image-cropper',\r\n  styleUrl: 'image-cropper.css',\r\n  shadow: true,\r\n})\r\nexport class ImageCropper {\r\n  handlers:number[] = [0,1,2,3,4,5,6,7];\r\n  polygonMouseDown:boolean = false;\r\n  polygonMouseDownPoint:Point = {x:0,y:0};\r\n  previousDistance:number|undefined = undefined;\r\n  svgMouseDownPoint:Point|undefined = undefined;\r\n  handlerMouseDownPoint:Point = {x:0,y:0};\r\n  root:HTMLElement;\r\n  containerElement:HTMLElement;\r\n  svgElement:SVGElement;\r\n  canvasElement:HTMLCanvasElement;\r\n  originalPoints:[Point,Point,Point,Point] = undefined;\r\n   usingTouchEvent:boolean = false;\r\n  usingQuad = false;\r\n  magnifierElement: HTMLElement; // Add this line\r\n\r\n  @Prop() img?: HTMLImageElement;\r\n  @Prop() rect?: Rect;\r\n  @Prop() quad?: Quad;\r\n  @Prop() license?: string;\r\n  @Prop() hidefooter?: string;\r\n  @Prop() handlersize?: string;\r\n  @Prop() inactiveSelections?: (Quad|Rect)[];\r\n  @State() viewBox:string = \"0 0 1280 720\";\r\n  @State() activeStroke:number = 2;\r\n  @Prop() rotation:number = 0;\r\n  @State() inActiveStroke:number = 4;\r\n  @State() selectedHandlerIndex:number = -1;\r\n  @State() points:[Point,Point,Point,Point] = undefined;\r\n  @State() offsetX = 0;\r\n  @State() offsetY = 0;\r\n  @State() scale = 1.0;\r\n  @Event() confirmed?: EventEmitter<void>;\r\n  @Event() canceled?: EventEmitter<void>;\r\n  @Event() selectionClicked?: EventEmitter<number>;\r\n\r\n  componentDidLoad(){\r\n    this.containerElement.addEventListener(\"touchmove\", (e:TouchEvent) => {\r\n      this.onContainerTouchMove(e);\r\n    })\r\n    this.containerElement.addEventListener(\"touchend\", () => {\r\n      this.previousDistance = undefined;\r\n      this.hideMagnifier(); // Hide magnifier on touch end\r\n    })\r\n  }\r\n\r\n  @Watch('img')\r\n  watchImgPropHandler(newValue: HTMLImageElement) {\r\n    if (newValue) {\r\n      console.log('watchImgPropHandler triggered with newValue:', newValue);\r\n      this.resetStates();\r\n      this.viewBox = `0 0 ${newValue.naturalWidth} ${newValue.naturalHeight}`;\r\n      console.log('viewBox set to:', this.viewBox);\r\n      if (this.root) {\r\n        const inActiveStroke = parseInt(this.root.style.getPropertyValue(\"--inactive-stroke\"));\r\n        const activeStroke = parseInt(this.root.style.getPropertyValue(\"--active-stroke\"));\r\n        console.log('inActiveStroke:', inActiveStroke, 'activeStroke:', activeStroke);\r\n        if (inActiveStroke) {\r\n          this.inActiveStroke = inActiveStroke;\r\n        }\r\n        if (activeStroke) {\r\n          this.activeStroke = activeStroke;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n\r\n  @Watch('rect')\r\n  watchRectPropHandler(newValue: Rect) {\r\n    if (newValue) {\r\n      this.usingQuad = false;\r\n      let points = this.getPointsFromRect(newValue);\r\n      if (this.img) {\r\n        this.restrainPointsInBounds(points,this.img.naturalWidth,this.img.naturalHeight);\r\n      }\r\n      this.points = points;\r\n    }\r\n  }\r\n\r\n  getPointsFromRect(rect:Rect):[Point,Point,Point,Point]{\r\n    const point1:Point = {x:rect.x,y:rect.y};\r\n    const point2:Point = {x:rect.x+rect.width,y:rect.y};\r\n    const point3:Point = {x:rect.x+rect.width,y:rect.y+rect.height};\r\n    const point4:Point = {x:rect.x,y:rect.y+rect.height};\r\n    return [point1,point2,point3,point4];\r\n  }\r\n\r\n  @Watch('quad')\r\n  watchQuadPropHandler(newValue: Quad) {\r\n    if (newValue) {\r\n      this.usingQuad = true;\r\n      let points = newValue.points;\r\n      if (this.img) {\r\n        this.restrainPointsInBounds(points,this.img.naturalWidth,this.img.naturalHeight);\r\n      }\r\n      this.points = newValue.points;\r\n    }\r\n  }\r\n\r\n  onCanceled(){\r\n    if (this.canceled){\r\n      this.canceled.emit();\r\n    }\r\n  }\r\n\r\n  onConfirmed(){\r\n    if (this.confirmed){\r\n      this.confirmed.emit();\r\n    }\r\n  }\r\n\r\n  getPointsData(){\r\n    if (this.points) {\r\n      let pointsData = this.points[0].x + \",\" + this.points[0].y + \" \";\r\n      pointsData = pointsData + this.points[1].x + \",\" + this.points[1].y +\" \";\r\n      pointsData = pointsData + this.points[2].x + \",\" + this.points[2].y +\" \";\r\n      pointsData = pointsData + this.points[3].x + \",\" + this.points[3].y;\r\n      return pointsData;\r\n    }\r\n    return \"\";\r\n  }\r\n\r\n  renderFooter(){\r\n    if (this.hidefooter === \"\") {\r\n      return \"\";\r\n    }\r\n    return (\r\n      <div class=\"footer\">\r\n        <section class=\"items\">\r\n          <div class=\"item accept-cancel\" onClick={() => this.onCanceled()}>\r\n            <img src=\"data:image/svg+xml,%3Csvg version='1.1' id='Layer_1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' x='0px' y='0px' viewBox='0 0 512 512' enable-background='new 0 0 512 512' xml:space='preserve'%3E%3Ccircle fill='%23727A87' cx='256' cy='256' r='256'/%3E%3Cg id='Icon_5_'%3E%3Cg%3E%3Cpath fill='%23FFFFFF' d='M394.2,142L370,117.8c-1.6-1.6-4.1-1.6-5.7,0L258.8,223.4c-1.6,1.6-4.1,1.6-5.7,0L147.6,117.8 c-1.6-1.6-4.1-1.6-5.7,0L117.8,142c-1.6,1.6-1.6,4.1,0,5.7l105.5,105.5c1.6,1.6,1.6,4.1,0,5.7L117.8,364.4c-1.6,1.6-1.6,4.1,0,5.7 l24.1,24.1c1.6,1.6,4.1,1.6,5.7,0l105.5-105.5c1.6-1.6,4.1-1.6,5.7,0l105.5,105.5c1.6,1.6,4.1,1.6,5.7,0l24.1-24.1 c1.6-1.6,1.6-4.1,0-5.7L288.6,258.8c-1.6-1.6-1.6-4.1,0-5.7l105.5-105.5C395.7,146.1,395.7,143.5,394.2,142z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\" />\r\n          </div>\r\n          <div class=\"item accept-use\" onClick={() => this.onConfirmed()}>\r\n            <img src=\"data:image/svg+xml,%3Csvg version='1.1' id='Layer_1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' x='0px' y='0px' viewBox='0 0 512 512' enable-background='new 0 0 512 512' xml:space='preserve'%3E%3Ccircle fill='%232CD865' cx='256' cy='256' r='256'/%3E%3Cg id='Icon_1_'%3E%3Cg%3E%3Cg%3E%3Cpath fill='%23FFFFFF' d='M208,301.4l-55.4-55.5c-1.5-1.5-4-1.6-5.6-0.1l-23.4,22.3c-1.6,1.6-1.7,4.1-0.1,5.7l81.6,81.4 c3.1,3.1,8.2,3.1,11.3,0l171.8-171.7c1.6-1.6,1.6-4.2-0.1-5.7l-23.4-22.3c-1.6-1.5-4.1-1.5-5.6,0.1L213.7,301.4 C212.1,303,209.6,303,208,301.4z'/%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/svg%3E\" />\r\n          </div>\r\n        </section>\r\n      </div>\r\n    )\r\n  }\r\n\r\n  rendenInactiveSelections(){\r\n    if (!this.inactiveSelections) {\r\n      return \"\";\r\n    }\r\n    return (\r\n      <Fragment>\r\n        {this.inactiveSelections.map((selection,index) => (\r\n          <polygon\r\n            points={this.getPointsDataFromSelection(selection)}\r\n            class=\"inactive-selection dashed\"\r\n            stroke-width={this.inActiveStroke * this.getRatio()}\r\n            fill=\"transparent\"\r\n            onMouseUp={()=>this.onSelectionClicked(index)}\r\n            onTouchStart={()=>this.onSelectionClicked(index)}\r\n          >\r\n         </polygon>\r\n        ))}\r\n      </Fragment>\r\n    );\r\n  }\r\n\r\n  onSelectionClicked(index:number) {\r\n    if (this.selectionClicked) {\r\n      this.selectionClicked.emit(index);\r\n    }\r\n  }\r\n\r\n  getPointsDataFromSelection(selection:Quad|Rect){\r\n    let points:Point[] = [];\r\n    if (\"width\" in selection) { //is Rect\r\n      points = this.getPointsFromRect(selection);\r\n    }else{\r\n      points = selection.points;\r\n    }\r\n    let pointsData = points[0].x + \",\" + points[0].y + \" \";\r\n    pointsData = pointsData + points[1].x + \",\" + points[1].y +\" \";\r\n    pointsData = pointsData + points[2].x + \",\" + points[2].y +\" \";\r\n    pointsData = pointsData + points[3].x + \",\" + points[3].y;\r\n    return pointsData;\r\n  }\r\n\r\n  renderHandlers(){\r\n    if (!this.points) {\r\n      return (<div></div>)\r\n    }\r\n    return (\r\n      <Fragment>\r\n        {this.handlers.map(index => (\r\n          <rect\r\n            x={this.getHandlerPos(index,\"x\")}\r\n            y={this.getHandlerPos(index,\"y\")}\r\n            width={this.getHandlerSize()}\r\n            height={this.getHandlerSize()}\r\n            class=\"cropper-controls\"\r\n            stroke-width={index === this.selectedHandlerIndex ? this.activeStroke * 2 * this.getRatio() : this.activeStroke * this.getRatio()}\r\n            fill=\"transparent\"\r\n            onMouseDown={(e:MouseEvent)=>this.onHandlerMouseDown(e,index)}\r\n            onMouseUp={(e:MouseEvent)=>this.onHandlerMouseUp(e)}\r\n            onTouchStart={(e:TouchEvent)=>this.onHandlerTouchStart(e,index)}\r\n            onPointerDown={(e:PointerEvent)=>this.onHandlerPointerDown(e,index)}\r\n          />\r\n        ))}\r\n      </Fragment>\r\n    )\r\n  }\r\n\r\n  getHandlerPos(index:number,key:string) {\r\n    let pos = 0;\r\n    let size = this.getHandlerSize();\r\n    if (index === 0){\r\n      pos = this.points[0][key];\r\n    }else if (index === 1) {\r\n      pos = this.points[0][key] + (this.points[1][key] - this.points[0][key])/2;\r\n    }else if (index === 2) {\r\n      pos = this.points[1][key];\r\n    }else if (index === 3) {\r\n      pos = this.points[1][key] + (this.points[2][key] - this.points[1][key])/2;\r\n    }else if (index === 4) {\r\n      pos = this.points[2][key];\r\n    }else if (index === 5) {\r\n      pos = this.points[3][key] + (this.points[2][key] - this.points[3][key])/2;\r\n    }else if (index === 6) {\r\n      pos = this.points[3][key];\r\n    }else if (index === 7) {\r\n      pos = this.points[0][key] + (this.points[3][key] - this.points[0][key])/2;\r\n    }\r\n    pos = pos - size/2;\r\n    return pos;\r\n  }\r\n\r\n  getHandlerSize() {\r\n    let ratio = this.getRatio();\r\n    let size:number = 20;\r\n    if (this.handlersize) {\r\n      try {\r\n        size = parseInt(this.handlersize);\r\n      } catch (error) {\r\n        console.log(error);\r\n      }\r\n    }\r\n    return Math.ceil(size*ratio);\r\n  }\r\n\r\n  onSVGTouchStart(e:TouchEvent) {\r\n    this.usingTouchEvent = true;\r\n    this.svgMouseDownPoint = undefined;\r\n    this.previousDistance = undefined;\r\n    let coord = this.getMousePosition(e,this.svgElement);\r\n    if (e.touches.length > 1) {\r\n      this.selectedHandlerIndex = -1;\r\n    }else{\r\n      if (this.selectedHandlerIndex != -1) {\r\n        this.originalPoints = JSON.parse(JSON.stringify(this.points));  //We need this info so that whether we start dragging the rectangular in the center or in the corner will not affect the result.\r\n        this.handlerMouseDownPoint.x = coord.x;\r\n        this.handlerMouseDownPoint.y = coord.y;\r\n      }else{\r\n        this.svgMouseDownPoint = {x:coord.x,y:coord.y};\r\n        this.polygonMouseDown = true; // Add this line to enable dragging immediately\r\n        this.polygonMouseDownPoint = { x: coord.x, y: coord.y }; // Add this line to store the initial touch point\r\n        this.originalPoints = JSON.parse(JSON.stringify(this.points)); // Add this line to store the original points\r\n      }\r\n    }\r\n  }\r\n\r\n  onSVGTouchEnd() {\r\n    this.svgMouseDownPoint = undefined;\r\n  }\r\n\r\n  onSVGTouchMove(e:TouchEvent) {\r\n    e.stopPropagation();\r\n    e.preventDefault();\r\n    if (e.touches.length === 2) {\r\n      this.pinchAndZoom(e);\r\n    }else{\r\n      if (this.svgMouseDownPoint) {\r\n        this.panSVG(e);\r\n      } else if (this.polygonMouseDown) { // Add this condition to handle dragging\r\n        this.handleMoveEvent(e);\r\n      }else{\r\n        this.handleMoveEvent(e);\r\n      }\r\n    }\r\n  }\r\n\r\n  //handle pinch and zoom\r\n  pinchAndZoom(e:TouchEvent){\r\n    const distance = this.getDistanceBetweenTwoTouches(e.touches[0],e.touches[1]);\r\n    if (this.previousDistance) {\r\n      if ((distance - this.previousDistance)>0) { //zoom\r\n        this.scale = Math.min(10, this.scale + 0.02);\r\n      }else{\r\n        this.scale = Math.max(0.1,this.scale - 0.02);\r\n      }\r\n      this.previousDistance = distance;\r\n    }else{\r\n      this.previousDistance = distance;\r\n    }\r\n  }\r\n\r\n  getDistanceBetweenTwoTouches(touch1:Touch,touch2:Touch){\r\n    const offsetX = touch1.clientX - touch2.clientX;\r\n    const offsetY = touch1.clientY - touch2.clientY;\r\n    const distance = offsetX * offsetX + offsetY + offsetY;\r\n    return distance;\r\n  }\r\n\r\n  onContainerMouseUp(){\r\n    this.svgMouseDownPoint = undefined;\r\n    if (!this.usingTouchEvent) {\r\n      this.selectedHandlerIndex = -1;\r\n      this.polygonMouseDown = false;\r\n      this.hideMagnifier(); // Hide the magnifier\r\n    }\r\n  }\r\n\r\n  onSVGMouseDown(e:MouseEvent) {\r\n    if (!this.usingTouchEvent) {\r\n      let coord = this.getMousePosition(e,this.svgElement);\r\n      this.svgMouseDownPoint = {x:coord.x,y:coord.y};\r\n    }\r\n  }\r\n\r\n  onContainerWheel(e:WheelEvent) {\r\n    if (e.deltaY<0) {\r\n      this.scale = this.scale + 0.1;\r\n    }else{\r\n      this.scale = Math.max(0.1, this.scale - 0.1);\r\n    }\r\n    e.preventDefault();\r\n  }\r\n\r\n  onContainerTouchMove(e:TouchEvent) {\r\n    e.preventDefault();\r\n    if (e.touches.length === 2) {\r\n      this.pinchAndZoom(e);\r\n    }\r\n  }\r\n\r\n  getPanAndZoomStyle(){\r\n    if (this.img) {\r\n      // const percentX = this.offsetX / this.img.naturalWidth * 100;\r\n      // const percentY = this.offsetY / this.img.naturalHeight * 100;\r\n      return `scale(1.0)  rotate(${this.rotation}deg)`;\r\n      // return \"scale(\"+this.scale+\") translateX(\"+percentX+\"%)translateY(\"+percentY+\"%)\";\r\n    }else{\r\n      return \"scale(1.0)\";\r\n    }\r\n  }\r\n\r\n  onSVGMouseMove(e:MouseEvent){\r\n    if (this.svgMouseDownPoint) {\r\n      this.panSVG(e);\r\n    }else{\r\n      this.handleMoveEvent(e);\r\n    }\r\n  }\r\n\r\n  panSVG(e:TouchEvent|MouseEvent){\r\n    let coord = this.getMousePosition(e,this.svgElement);\r\n    let offsetX = coord.x - this.svgMouseDownPoint.x;\r\n    let offsetY = coord.y - this.svgMouseDownPoint.y;\r\n    //console.log(\"coord\");\r\n    //console.log(coord);\r\n    //console.log(\"svgMouseDownPoint\");\r\n    //console.log(this.svgMouseDownPoint);\r\n\r\n    //console.log(offsetX)\r\n    //console.log(offsetY)\r\n    //e.g img width: 100, offsetX: -10, translateX: -10%\r\n    this.offsetX = this.offsetX + offsetX;\r\n    this.offsetY = this.offsetY + offsetY;\r\n  }\r\n\r\n  handleMoveEvent(e:MouseEvent|TouchEvent){\r\n    if (this.polygonMouseDown) {\r\n      let coord = this.getMousePosition(e,this.svgElement);\r\n      let offsetX = coord.x - this.polygonMouseDownPoint.x;\r\n      let offsetY = coord.y - this.polygonMouseDownPoint.y;\r\n      let newPoints = JSON.parse(JSON.stringify(this.originalPoints));\r\n      for (const point of newPoints) {\r\n        point.x = point.x + offsetX;\r\n        point.y = point.y + offsetY;\r\n        if (point.x < 0 || point.y < 0 || point.x > this.img.naturalWidth || point.y > this.img.naturalHeight){\r\n          console.log(\"reach bounds\");\r\n          return;\r\n        }\r\n      }\r\n      this.points = newPoints;\r\n      this.showMagnifier(); // Show the magnifier when the rect is moved\r\n      this.updateMagnifier(coord); // Update the magnifier position and content\r\n    }\r\n    if (this.selectedHandlerIndex >= 0) {\r\n      let pointIndex = this.getPointIndexFromHandlerIndex(this.selectedHandlerIndex);\r\n      let coord = this.getMousePosition(e,this.svgElement);\r\n      let offsetX = coord.x - this.handlerMouseDownPoint.x;\r\n      let offsetY = coord.y - this.handlerMouseDownPoint.y;\r\n      let newPoints = JSON.parse(JSON.stringify(this.originalPoints));\r\n      if (pointIndex != -1) {\r\n        let selectedPoint = newPoints[pointIndex];\r\n        selectedPoint.x = this.originalPoints[pointIndex].x + offsetX;\r\n        selectedPoint.y = this.originalPoints[pointIndex].y + offsetY;\r\n        if (this.usingQuad === false) { //rect mode\r\n          if (pointIndex === 0) {\r\n            newPoints[1].y = selectedPoint.y;\r\n            newPoints[3].x = selectedPoint.x;\r\n          }else if (pointIndex === 1) {\r\n            newPoints[0].y = selectedPoint.y;\r\n            newPoints[2].x = selectedPoint.x;\r\n          }else if (pointIndex === 2) {\r\n            newPoints[1].x = selectedPoint.x;\r\n            newPoints[3].y = selectedPoint.y;\r\n          }else if (pointIndex === 3) {\r\n            newPoints[0].x = selectedPoint.x;\r\n            newPoints[2].y = selectedPoint.y;\r\n          }\r\n        }\r\n      }else{ //mid-point handlers\r\n        if (this.selectedHandlerIndex === 1) {\r\n          newPoints[0].y = this.originalPoints[0].y + offsetY;\r\n          newPoints[1].y = this.originalPoints[1].y + offsetY;\r\n        }else if (this.selectedHandlerIndex === 3) {\r\n          newPoints[1].x = this.originalPoints[1].x + offsetX;\r\n          newPoints[2].x = this.originalPoints[2].x + offsetX;\r\n        }else if (this.selectedHandlerIndex === 5) {\r\n          newPoints[2].y = this.originalPoints[2].y + offsetY;\r\n          newPoints[3].y = this.originalPoints[3].y + offsetY;\r\n        }else if (this.selectedHandlerIndex === 7) {\r\n          newPoints[0].x = this.originalPoints[0].x + offsetX;\r\n          newPoints[3].x = this.originalPoints[3].x + offsetX;\r\n        }\r\n      }\r\n      if (this.img) {\r\n        this.restrainPointsInBounds(newPoints,this.img.naturalWidth,this.img.naturalHeight);\r\n      }\r\n      this.points = newPoints;\r\n      this.showMagnifier(); // Show the magnifier when the rect is moved\r\n      this.updateMagnifier(coord); // Update the magnifier position and content\r\n    }\r\n  }\r\n\r\n  restrainPointsInBounds(points:[Point,Point,Point,Point],imgWidth:number,imgHeight:number){\r\n    for (let index = 0; index < points.length; index++) {\r\n      const point = points[index];\r\n      point.x = Math.max(0,point.x);\r\n      point.x = Math.min(point.x,imgWidth);\r\n      point.y = Math.max(0,point.y);\r\n      point.y = Math.min(point.y,imgHeight);\r\n    }\r\n  }\r\n\r\n  onPolygonMouseDown(e:MouseEvent){\r\n    e.stopPropagation();\r\n    this.originalPoints = JSON.parse(JSON.stringify(this.points));\r\n    this.polygonMouseDown = true;\r\n    let coord = this.getMousePosition(e,this.svgElement);\r\n    this.polygonMouseDownPoint.x = coord.x;\r\n    this.polygonMouseDownPoint.y = coord.y;\r\n    this.showMagnifier(); // Show the magnifier when the rect starts being moved\r\n  }\r\n\r\n  onPolygonMouseUp(e:MouseEvent){\r\n    e.stopPropagation();\r\n    if (!this.usingTouchEvent) {\r\n      this.selectedHandlerIndex = -1;\r\n      this.polygonMouseDown = false;\r\n      this.hideMagnifier(); // Hide the magnifier when the rect stops being moved\r\n    }\r\n  }\r\n\r\n  onPolygonTouchStart(e:TouchEvent) {\r\n    this.usingTouchEvent = true;\r\n    e.stopPropagation();\r\n    this.selectedHandlerIndex = -1;\r\n    // this.polygonMouseDown = false;\r\n    this.polygonMouseDown = true;\r\n    this.originalPoints = JSON.parse(JSON.stringify(this.points));\r\n    // this.polygonMouseDown = true;\r\n    let coord = this.getMousePosition(e,this.svgElement);\r\n    // this.polygonMouseDownPoint.x = coord.x;\r\n    // this.polygonMouseDownPoint.y = coord.y;\r\n    this.polygonMouseDownPoint = { x: coord.x, y: coord.y }; // Store the initial touch point\r\n    this.showMagnifier(); // Show the magnifier when the rect starts being moved\r\n\r\n  }\r\n\r\n  onPolygonTouchEnd(e:TouchEvent) {\r\n    e.stopPropagation();\r\n    this.selectedHandlerIndex = -1;\r\n    this.polygonMouseDown = false;\r\n    this.hideMagnifier(); // Hide the magnifier when the rect stops being moved\r\n  }\r\n\r\n  onHandlerMouseDown(e:MouseEvent,index:number){\r\n    e.stopPropagation();\r\n    let coord = this.getMousePosition(e,this.svgElement);\r\n    this.originalPoints = JSON.parse(JSON.stringify(this.points));\r\n    this.handlerMouseDownPoint.x = coord.x;\r\n    this.handlerMouseDownPoint.y = coord.y;\r\n    this.selectedHandlerIndex = index;\r\n  }\r\n\r\n  onHandlerMouseUp(e:MouseEvent){\r\n    e.stopPropagation();\r\n    if (!this.usingTouchEvent) {\r\n      this.selectedHandlerIndex = -1;\r\n      this.hideMagnifier(); // Hide the magnifier\r\n    }\r\n  }\r\n\r\n  onHandlerTouchStart(e:TouchEvent,index:number) {\r\n    this.usingTouchEvent = true; //Touch events are triggered before mouse events. We can use this to prevent executing mouse events.\r\n    e.stopPropagation();\r\n    this.polygonMouseDown = false;\r\n    let coord = this.getMousePosition(e,this.svgElement);\r\n    this.originalPoints = JSON.parse(JSON.stringify(this.points));\r\n    this.handlerMouseDownPoint.x = coord.x;\r\n    this.handlerMouseDownPoint.y = coord.y;\r\n    this.selectedHandlerIndex = index;\r\n  }\r\n\r\n  onHandlerPointerDown(e:PointerEvent,index:number) {\r\n    if (e.pointerType != \"mouse\" && !this.usingTouchEvent) {\r\n      this.onHandlerMouseDown(e,index);\r\n      e.preventDefault();\r\n    }\r\n  }\r\n\r\n  getPointIndexFromHandlerIndex(index:number){\r\n    if (index === 0) {\r\n      return 0;\r\n    }else if (index === 2) {\r\n      return 1;\r\n    }else if (index === 4) {\r\n      return 2;\r\n    }else if (index === 6) {\r\n      return 3;\r\n    }\r\n    return -1;\r\n  }\r\n\r\n  //Convert the screen coordinates to the SVG's coordinates from https://www.petercollingridge.co.uk/tutorials/svg/interactive/dragging/\r\n  getMousePosition(event: any, svg: any) {\r\n    let CTM = svg.getScreenCTM();\r\n    if (!CTM) {\r\n      return { x: 0, y: 0 };\r\n    }\r\n\r\n    let x, y;\r\n    if (event.targetTouches) { // if it is a touch event\r\n      x = event.targetTouches[0].clientX;\r\n      y = event.targetTouches[0].clientY;\r\n    } else {\r\n      x = event.clientX;\r\n      y = event.clientY;\r\n    }\r\n\r\n    // Invert the transformation matrix\r\n    let det = CTM.a * CTM.d - CTM.b * CTM.c;\r\n    if (det === 0) {\r\n      // Handle the case where the matrix is singular\r\n      return { x: 0, y: 0 };\r\n    }\r\n\r\n    let invCTM = {\r\n      a: CTM.d / det,\r\n      b: -CTM.b / det,\r\n      c: -CTM.c / det,\r\n      d: CTM.a / det,\r\n      e: (CTM.c * CTM.f - CTM.d * CTM.e) / det,\r\n      f: (CTM.b * CTM.e - CTM.a * CTM.f) / det\r\n    };\r\n\r\n    return {\r\n      x: (x - CTM.e) * invCTM.a + (y - CTM.f) * invCTM.c,\r\n      y: (x - CTM.e) * invCTM.b + (y - CTM.f) * invCTM.d\r\n    };\r\n  }\r\n\r\n\r\n  getRatio(){\r\n    if (this.img) {\r\n      return this.img.naturalWidth/750;\r\n    }else{\r\n      return 1;\r\n    }\r\n  }\r\n\r\n  @Method()\r\n  async resetStates():Promise<void>\r\n  {\r\n    this.scale = 1.0;\r\n    this.offsetX = 0;\r\n    this.offsetY = 0;\r\n  }\r\n\r\n  @Method()\r\n  async getAllSelections(convertTo?:\"rect\"|\"quad\"):Promise<(Quad|Rect)[]>\r\n  {\r\n    let all = [];\r\n    for (let index = 0; index < this.inactiveSelections.length; index++) {\r\n      let selection = this.inactiveSelections[index];\r\n      if (convertTo) {\r\n        if (\"width\" in selection && convertTo === \"quad\") {\r\n          selection = {points:this.getPointsFromRect(selection)};\r\n        }else if (!(\"width\" in selection) && convertTo === \"rect\"){\r\n          selection = this.getRectFromPoints(selection.points);\r\n        }\r\n      }\r\n      all.push(selection);\r\n    }\r\n    let useQuad = true;\r\n    if (convertTo) {\r\n      if (convertTo === \"rect\") {\r\n        useQuad = false;\r\n      }\r\n    }else{\r\n      if (!this.usingQuad) {\r\n        useQuad = false;\r\n      }\r\n    }\r\n    if (useQuad) {\r\n      const quad = await this.getQuad();\r\n      all.push(quad);\r\n    }else{\r\n      const rect = await this.getRect();\r\n      all.push(rect);\r\n    }\r\n    return all;\r\n  }\r\n\r\n  @Method()\r\n  async getPoints():Promise<[Point,Point,Point,Point]>\r\n  {\r\n    return this.points;\r\n  }\r\n\r\n  @Method()\r\n  async getQuad():Promise<Quad>\r\n  {\r\n    return {points:this.points};\r\n  }\r\n\r\n  @Method()\r\n  async getRect():Promise<Rect>\r\n  {\r\n    return this.getRectFromPoints(this.points);\r\n  }\r\n\r\n  getRectFromPoints(points:Point[]):Rect{\r\n    let minX:number;\r\n    let minY:number;\r\n    let maxX:number;\r\n    let maxY:number;\r\n    for (const point of points) {\r\n      if (!minX) {\r\n        minX = point.x;\r\n        maxX = point.x;\r\n        minY = point.y;\r\n        maxY = point.y;\r\n      }else{\r\n        minX = Math.min(point.x,minX);\r\n        minY = Math.min(point.y,minY);\r\n        maxX = Math.max(point.x,maxX);\r\n        maxY = Math.max(point.y,maxY);\r\n      }\r\n    }\r\n    minX = Math.floor(minX);\r\n    maxX = Math.floor(maxX);\r\n    minY = Math.floor(minY);\r\n    maxY = Math.floor(maxY);\r\n    return {x:minX,y:minY,width:maxX - minX,height:maxY - minY};\r\n  }\r\n\r\n\r\n\r\n  async getImageFromBlob(source:Blob){\r\n    return new Promise<HTMLImageElement>((resolve, reject) => {\r\n      let reader = new FileReader();\r\n      reader.readAsDataURL(source);\r\n      reader.onloadend = function () {\r\n        let dataURL:string = reader.result as string;\r\n        let img = document.createElement(\"img\");\r\n        img.onload = function(){\r\n          resolve(img);\r\n        };\r\n        img.onerror = function(){\r\n          reject();\r\n        }\r\n        img.src = dataURL;\r\n      }\r\n    })\r\n  }\r\n\r\n  async getImageFromDataURL(source:string){\r\n    return new Promise<HTMLImageElement>((resolve, reject) => {\r\n      let img = document.createElement(\"img\");\r\n      img.onload = function(){\r\n        resolve(img);\r\n      };\r\n      img.onerror = function(){\r\n        reject();\r\n      }\r\n      img.src = source;\r\n    })\r\n  }\r\n\r\n @Method()\r\n async detect(){}\r\n\r\n\r\n\r\n  getSVGWidth(){\r\n    if (this.img && this.svgElement) {\r\n      this.svgElement.style.height = \"100%\";\r\n      let imgRatio = this.img.naturalWidth/this.img.naturalHeight;\r\n      let width = this.svgElement.clientHeight * imgRatio;\r\n      if (width>this.svgElement.parentElement.clientWidth) {\r\n        width = this.svgElement.parentElement.clientWidth;\r\n        this.svgElement.style.height = width / imgRatio + \"px\";\r\n      }\r\n      return width;\r\n    }\r\n    return \"100%\";\r\n  }\r\n\r\n  onSVGPointerMove(e:PointerEvent){\r\n    if (e.pointerType != \"mouse\" && !this.usingTouchEvent) {\r\n      e.stopPropagation();\r\n      e.preventDefault();\r\n      this.onSVGMouseMove(e);\r\n    }\r\n  }\r\n\r\n  onSVGPointerDown(e:PointerEvent){\r\n    if (e.pointerType != \"mouse\" && !this.usingTouchEvent) {\r\n      this.onSVGMouseDown(e);\r\n    }\r\n  }\r\n\r\n  onSVGPointerUp(e:PointerEvent) {\r\n    if (e.pointerType != \"mouse\" && !this.usingTouchEvent) {\r\n      this.svgMouseDownPoint = undefined;\r\n      this.selectedHandlerIndex = -1;\r\n    }\r\n  }\r\n\r\n  onPolygonPointerDown(e:PointerEvent){\r\n    if (e.pointerType != \"mouse\" && !this.usingTouchEvent) {\r\n      this.onPolygonMouseDown(e);\r\n    }\r\n  }\r\n\r\n  onPolygonPointerUp(e:PointerEvent){\r\n    e.stopPropagation();\r\n    this.selectedHandlerIndex = -1;\r\n    this.polygonMouseDown = false;\r\n  }\r\n\r\n  render() {\r\n    return (\r\n      <Host ref={(el) => this.root = el}>\r\n        <div class=\"container absolute\"\r\n          ref={(el) => this.containerElement = el}\r\n          onWheel={(e:WheelEvent)=>this.onContainerWheel(e)}\r\n          onMouseUp={()=>this.onContainerMouseUp()}\r\n        >\r\n          <canvas\r\n            ref={(el) => this.canvasElement = el as HTMLCanvasElement}\r\n            class=\"hidden-canvas\"\r\n          ></canvas>\r\n          <svg\r\n            version=\"1.1\"\r\n            ref={(el) => this.svgElement = el as SVGElement}\r\n            class=\"cropper-svg\"\r\n            xmlns=\"http://www.w3.org/2000/svg\"\r\n            viewBox={this.viewBox}\r\n            width={this.getSVGWidth()}\r\n            style={{transform:this.getPanAndZoomStyle()}}\r\n            onMouseMove={(e:MouseEvent)=>this.onSVGMouseMove(e)}\r\n            onMouseDown={(e:MouseEvent)=>this.onSVGMouseDown(e)}\r\n            onTouchStart={(e:TouchEvent)=>this.onSVGTouchStart(e)}\r\n            onTouchEnd={()=>this.onSVGTouchEnd()}\r\n            onTouchMove={(e:TouchEvent)=>this.onSVGTouchMove(e)}\r\n            onPointerMove={(e:PointerEvent)=>this.onSVGPointerMove(e)}\r\n            onPointerDown={(e:PointerEvent)=>this.onSVGPointerDown(e)}\r\n            onPointerUp={(e:PointerEvent)=>this.onSVGPointerUp(e)}\r\n          >\r\n            <image href={this.img ? this.img.src : \"\"}></image>\r\n            {this.rendenInactiveSelections()}\r\n            <polygon\r\n              points={this.getPointsData()}\r\n              class=\"cropper-controls dashed\"\r\n              stroke-width={this.activeStroke * this.getRatio()}\r\n              fill=\"transparent\"\r\n              onMouseDown={(e:MouseEvent)=>this.onPolygonMouseDown(e)}\r\n              onMouseUp={(e:MouseEvent)=>this.onPolygonMouseUp(e)}\r\n              onTouchStart={(e:TouchEvent)=>this.onPolygonTouchStart(e)}\r\n              onTouchEnd={(e:TouchEvent)=>this.onPolygonTouchEnd(e)}\r\n              onPointerDown={(e:PointerEvent)=>this.onPolygonPointerDown(e)}\r\n              onPointerUp={(e:PointerEvent)=>this.onPolygonPointerUp(e)}\r\n            >\r\n            </polygon>\r\n            {this.renderHandlers()}\r\n          </svg>\r\n          {this.renderFooter()}\r\n          <div class=\"magnifier\" ref={(el) => this.magnifierElement = el as HTMLElement}></div>\r\n          <slot></slot>\r\n        </div>\r\n      </Host>\r\n    );\r\n  }\r\n\r\n  showMagnifier() {\r\n    if (this.magnifierElement) {\r\n      this.magnifierElement.style.display = 'block';\r\n    }\r\n  }\r\n\r\n  hideMagnifier() {\r\n    if (this.magnifierElement) {\r\n      this.magnifierElement.style.display = 'none';\r\n    }\r\n  }\r\n\r\n  updateMagnifier(coord: Point) {\r\n    if (!this.magnifierElement || !this.img) return;\r\n\r\n    // Get the coordinates and dimensions of the rect\r\n    const rect = this.getRectFromPoints(this.points);\r\n\r\n    // Calculate the position of the magnifier\r\n    const magnifierSize = 100; // Example size\r\n    // const magnifierLeft = (coord.x - 300) - magnifierSize / 2 ;\r\n    // const magnifierTop = (coord.y - 200) - magnifierSize / 2;\r\n    const magnifierLeft = coord.x - magnifierSize ;\r\n    const magnifierTop = coord.y - magnifierSize;\r\n\r\n    // Cast svgElement to SVGSVGElement to use createSVGPoint\r\n    const svgElement = this.svgElement as unknown as SVGSVGElement;\r\n\r\n    // Check if getScreenCTM and createSVGPoint methods are available\r\n    if (svgElement.getScreenCTM && svgElement.createSVGPoint) {\r\n      const ctm = svgElement.getScreenCTM();\r\n      const point = svgElement.createSVGPoint();\r\n      point.x = magnifierLeft;\r\n      point.y = magnifierTop;\r\n      const transformedPoint = point.matrixTransform(ctm);\r\n\r\n      // Set the magnifier's position\r\n      this.magnifierElement.style.left = `${transformedPoint.x - 40}px`;\r\n      this.magnifierElement.style.top = `${transformedPoint.y - 210}px`;\r\n    } else {\r\n      // Fallback if methods are not available\r\n      this.magnifierElement.style.left = `${magnifierLeft}px`;\r\n      this.magnifierElement.style.top = `${magnifierTop}px`;\r\n    }\r\n\r\n    // Set the magnifier's content (e.g., magnified image)\r\n    const zoomLevel = 0.5; // Example zoom level\r\n    const sx = Math.max(0, rect.x + (coord.x - rect.x) / this.scale - magnifierSize / zoomLevel / 2);\r\n    const sy = Math.max(0, rect.y + (coord.y - rect.y) / this.scale - magnifierSize / zoomLevel / 2);\r\n    const sw = magnifierSize / zoomLevel;\r\n    const sh = magnifierSize / zoomLevel;\r\n    const dx = 0;\r\n    const dy = 0;\r\n    const dw = magnifierSize;\r\n    const dh = magnifierSize;\r\n\r\n    const magnifierCanvas = document.createElement(\"canvas\");\r\n    magnifierCanvas.width = magnifierSize;\r\n    magnifierCanvas.height = magnifierSize;\r\n    const magnifierCtx = magnifierCanvas.getContext(\"2d\");\r\n\r\n    magnifierCtx.drawImage(this.img, sx, sy, sw, sh, dx, dy, dw, dh);\r\n\r\n    // Draw the polygon on the magnifier canvas\r\n    magnifierCtx.scale(zoomLevel, zoomLevel);\r\n    magnifierCtx.strokeStyle = 'orange'; // Set the style as needed\r\n    magnifierCtx.lineWidth = this.activeStroke / zoomLevel; // Adjust the line width\r\n    magnifierCtx.beginPath();\r\n    magnifierCtx.moveTo((this.points[0].x - sx), (this.points[0].y - sy));\r\n    for (let i = 1; i < this.points.length; i++) {\r\n      magnifierCtx.lineTo((this.points[i].x - sx), (this.points[i].y - sy));\r\n    }\r\n    magnifierCtx.closePath();\r\n    magnifierCtx.stroke();\r\n\r\n    this.magnifierElement.style.backgroundImage = `url(${magnifierCanvas.toDataURL()})`;\r\n  }\r\n\r\n\r\n\r\n\r\n}\r\n"], "mappings": "gEAAA,MAAMA,EAAkB,m4CACxB,MAAAC,EAAeD,E,MCkCFE,EAAY,M,oJACvBC,KAAAC,SAAoB,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GACnCD,KAAAE,iBAA2B,MAC3BF,KAAAG,sBAA8B,CAACC,EAAE,EAAEC,EAAE,GACrCL,KAAAM,iBAAoCC,UACpCP,KAAAQ,kBAAoCD,UACpCP,KAAAS,sBAA8B,CAACL,EAAE,EAAEC,EAAE,GAKrCL,KAAAU,eAA2CH,UAC1CP,KAAAW,gBAA0B,MAC3BX,KAAAY,UAAY,M,sLAUc,e,kBACK,E,cACL,E,oBACO,E,2BACO,E,YACIL,U,aACzB,E,aACA,E,WACF,C,CAKjB,gBAAAM,GACEb,KAAKc,iBAAiBC,iBAAiB,aAAcC,IACnDhB,KAAKiB,qBAAqBD,EAAE,IAE9BhB,KAAKc,iBAAiBC,iBAAiB,YAAY,KACjDf,KAAKM,iBAAmBC,UACxBP,KAAKkB,eAAe,G,CAKxB,mBAAAC,CAAoBC,GAClB,GAAIA,EAAU,CACZC,QAAQC,IAAI,+CAAgDF,GAC5DpB,KAAKuB,cACLvB,KAAKwB,QAAU,OAAOJ,EAASK,gBAAgBL,EAASM,gBACxDL,QAAQC,IAAI,kBAAmBtB,KAAKwB,SACpC,GAAIxB,KAAK2B,KAAM,CACb,MAAMC,EAAiBC,SAAS7B,KAAK2B,KAAKG,MAAMC,iBAAiB,sBACjE,MAAMC,EAAeH,SAAS7B,KAAK2B,KAAKG,MAAMC,iBAAiB,oBAC/DV,QAAQC,IAAI,kBAAmBM,EAAgB,gBAAiBI,GAChE,GAAIJ,EAAgB,CAClB5B,KAAK4B,eAAiBA,C,CAExB,GAAII,EAAc,CAChBhC,KAAKgC,aAAeA,C,IAQ5B,oBAAAC,CAAqBb,GACnB,GAAIA,EAAU,CACZpB,KAAKY,UAAY,MACjB,IAAIsB,EAASlC,KAAKmC,kBAAkBf,GACpC,GAAIpB,KAAKoC,IAAK,CACZpC,KAAKqC,uBAAuBH,EAAOlC,KAAKoC,IAAIX,aAAazB,KAAKoC,IAAIV,c,CAEpE1B,KAAKkC,OAASA,C,EAIlB,iBAAAC,CAAkBG,GAChB,MAAMC,EAAe,CAACnC,EAAEkC,EAAKlC,EAAEC,EAAEiC,EAAKjC,GACtC,MAAMmC,EAAe,CAACpC,EAAEkC,EAAKlC,EAAEkC,EAAKG,MAAMpC,EAAEiC,EAAKjC,GACjD,MAAMqC,EAAe,CAACtC,EAAEkC,EAAKlC,EAAEkC,EAAKG,MAAMpC,EAAEiC,EAAKjC,EAAEiC,EAAKK,QACxD,MAAMC,EAAe,CAACxC,EAAEkC,EAAKlC,EAAEC,EAAEiC,EAAKjC,EAAEiC,EAAKK,QAC7C,MAAO,CAACJ,EAAOC,EAAOE,EAAOE,E,CAI/B,oBAAAC,CAAqBzB,GACnB,GAAIA,EAAU,CACZpB,KAAKY,UAAY,KACjB,IAAIsB,EAASd,EAASc,OACtB,GAAIlC,KAAKoC,IAAK,CACZpC,KAAKqC,uBAAuBH,EAAOlC,KAAKoC,IAAIX,aAAazB,KAAKoC,IAAIV,c,CAEpE1B,KAAKkC,OAASd,EAASc,M,EAI3B,UAAAY,GACE,GAAI9C,KAAK+C,SAAS,CAChB/C,KAAK+C,SAASC,M,EAIlB,WAAAC,GACE,GAAIjD,KAAKkD,UAAU,CACjBlD,KAAKkD,UAAUF,M,EAInB,aAAAG,GACE,GAAInD,KAAKkC,OAAQ,CACf,IAAIkB,EAAapD,KAAKkC,OAAO,GAAG9B,EAAI,IAAMJ,KAAKkC,OAAO,GAAG7B,EAAI,IAC7D+C,EAAaA,EAAapD,KAAKkC,OAAO,GAAG9B,EAAI,IAAMJ,KAAKkC,OAAO,GAAG7B,EAAG,IACrE+C,EAAaA,EAAapD,KAAKkC,OAAO,GAAG9B,EAAI,IAAMJ,KAAKkC,OAAO,GAAG7B,EAAG,IACrE+C,EAAaA,EAAapD,KAAKkC,OAAO,GAAG9B,EAAI,IAAMJ,KAAKkC,OAAO,GAAG7B,EAClE,OAAO+C,C,CAET,MAAO,E,CAGT,YAAAC,GACE,GAAIrD,KAAKsD,aAAe,GAAI,CAC1B,MAAO,E,CAET,OACEC,EAAA,OAAKC,MAAM,UACTD,EAAA,WAASC,MAAM,SACbD,EAAA,OAAKC,MAAM,qBAAqBC,QAAS,IAAMzD,KAAK8C,cAClDS,EAAA,OAAKG,IAAI,wyBAEXH,EAAA,OAAKC,MAAM,kBAAkBC,QAAS,IAAMzD,KAAKiD,eAC/CM,EAAA,OAAKG,IAAI,8mB,CAOnB,wBAAAC,GACE,IAAK3D,KAAK4D,mBAAoB,CAC5B,MAAO,E,CAET,OACEL,EAACM,EAAQ,KACN7D,KAAK4D,mBAAmBE,KAAI,CAACC,EAAUC,IACtCT,EAAA,WACErB,OAAQlC,KAAKiE,2BAA2BF,GACxCP,MAAM,4BAA2B,eACnBxD,KAAK4B,eAAiB5B,KAAKkE,WACzCC,KAAK,cACLC,UAAW,IAAIpE,KAAKqE,mBAAmBL,GACvCM,aAAc,IAAItE,KAAKqE,mBAAmBL,O,CAQpD,kBAAAK,CAAmBL,GACjB,GAAIhE,KAAKuE,iBAAkB,CACzBvE,KAAKuE,iBAAiBvB,KAAKgB,E,EAI/B,0BAAAC,CAA2BF,GACzB,IAAI7B,EAAiB,GACrB,GAAI,UAAW6B,EAAW,CACxB7B,EAASlC,KAAKmC,kBAAkB4B,E,KAC7B,CACH7B,EAAS6B,EAAU7B,M,CAErB,IAAIkB,EAAalB,EAAO,GAAG9B,EAAI,IAAM8B,EAAO,GAAG7B,EAAI,IACnD+C,EAAaA,EAAalB,EAAO,GAAG9B,EAAI,IAAM8B,EAAO,GAAG7B,EAAG,IAC3D+C,EAAaA,EAAalB,EAAO,GAAG9B,EAAI,IAAM8B,EAAO,GAAG7B,EAAG,IAC3D+C,EAAaA,EAAalB,EAAO,GAAG9B,EAAI,IAAM8B,EAAO,GAAG7B,EACxD,OAAO+C,C,CAGT,cAAAoB,GACE,IAAKxE,KAAKkC,OAAQ,CAChB,OAAQqB,EAAA,W,CAEV,OACEA,EAACM,EAAQ,KACN7D,KAAKC,SAAS6D,KAAIE,GACjBT,EAAA,QACEnD,EAAGJ,KAAKyE,cAAcT,EAAM,KAC5B3D,EAAGL,KAAKyE,cAAcT,EAAM,KAC5BvB,MAAOzC,KAAK0E,iBACZ/B,OAAQ3C,KAAK0E,iBACblB,MAAM,mBAAkB,eACVQ,IAAUhE,KAAK2E,qBAAuB3E,KAAKgC,aAAe,EAAIhC,KAAKkE,WAAalE,KAAKgC,aAAehC,KAAKkE,WACvHC,KAAK,cACLS,YAAc5D,GAAehB,KAAK6E,mBAAmB7D,EAAEgD,GACvDI,UAAYpD,GAAehB,KAAK8E,iBAAiB9D,GACjDsD,aAAetD,GAAehB,KAAK+E,oBAAoB/D,EAAEgD,GACzDgB,cAAgBhE,GAAiBhB,KAAKiF,qBAAqBjE,EAAEgD,O,CAOvE,aAAAS,CAAcT,EAAakB,GACzB,IAAIC,EAAM,EACV,IAAIC,EAAOpF,KAAK0E,iBAChB,GAAIV,IAAU,EAAE,CACdmB,EAAMnF,KAAKkC,OAAO,GAAGgD,E,MACjB,GAAIlB,IAAU,EAAG,CACrBmB,EAAMnF,KAAKkC,OAAO,GAAGgD,IAAQlF,KAAKkC,OAAO,GAAGgD,GAAOlF,KAAKkC,OAAO,GAAGgD,IAAM,C,MACpE,GAAIlB,IAAU,EAAG,CACrBmB,EAAMnF,KAAKkC,OAAO,GAAGgD,E,MACjB,GAAIlB,IAAU,EAAG,CACrBmB,EAAMnF,KAAKkC,OAAO,GAAGgD,IAAQlF,KAAKkC,OAAO,GAAGgD,GAAOlF,KAAKkC,OAAO,GAAGgD,IAAM,C,MACpE,GAAIlB,IAAU,EAAG,CACrBmB,EAAMnF,KAAKkC,OAAO,GAAGgD,E,MACjB,GAAIlB,IAAU,EAAG,CACrBmB,EAAMnF,KAAKkC,OAAO,GAAGgD,IAAQlF,KAAKkC,OAAO,GAAGgD,GAAOlF,KAAKkC,OAAO,GAAGgD,IAAM,C,MACpE,GAAIlB,IAAU,EAAG,CACrBmB,EAAMnF,KAAKkC,OAAO,GAAGgD,E,MACjB,GAAIlB,IAAU,EAAG,CACrBmB,EAAMnF,KAAKkC,OAAO,GAAGgD,IAAQlF,KAAKkC,OAAO,GAAGgD,GAAOlF,KAAKkC,OAAO,GAAGgD,IAAM,C,CAE1EC,EAAMA,EAAMC,EAAK,EACjB,OAAOD,C,CAGT,cAAAT,GACE,IAAIW,EAAQrF,KAAKkE,WACjB,IAAIkB,EAAc,GAClB,GAAIpF,KAAKsF,YAAa,CACpB,IACEF,EAAOvD,SAAS7B,KAAKsF,Y,CACrB,MAAOC,GACPlE,QAAQC,IAAIiE,E,EAGhB,OAAOC,KAAKC,KAAKL,EAAKC,E,CAGxB,eAAAK,CAAgB1E,GACdhB,KAAKW,gBAAkB,KACvBX,KAAKQ,kBAAoBD,UACzBP,KAAKM,iBAAmBC,UACxB,IAAIoF,EAAQ3F,KAAK4F,iBAAiB5E,EAAEhB,KAAK6F,YACzC,GAAI7E,EAAE8E,QAAQC,OAAS,EAAG,CACxB/F,KAAK2E,sBAAwB,C,KAC1B,CACH,GAAI3E,KAAK2E,uBAAyB,EAAG,CACnC3E,KAAKU,eAAiBsF,KAAKC,MAAMD,KAAKE,UAAUlG,KAAKkC,SACrDlC,KAAKS,sBAAsBL,EAAIuF,EAAMvF,EACrCJ,KAAKS,sBAAsBJ,EAAIsF,EAAMtF,C,KAClC,CACHL,KAAKQ,kBAAoB,CAACJ,EAAEuF,EAAMvF,EAAEC,EAAEsF,EAAMtF,GAC5CL,KAAKE,iBAAmB,KACxBF,KAAKG,sBAAwB,CAAEC,EAAGuF,EAAMvF,EAAGC,EAAGsF,EAAMtF,GACpDL,KAAKU,eAAiBsF,KAAKC,MAAMD,KAAKE,UAAUlG,KAAKkC,Q,GAK3D,aAAAiE,GACEnG,KAAKQ,kBAAoBD,S,CAG3B,cAAA6F,CAAepF,GACbA,EAAEqF,kBACFrF,EAAEsF,iBACF,GAAItF,EAAE8E,QAAQC,SAAW,EAAG,CAC1B/F,KAAKuG,aAAavF,E,KACf,CACH,GAAIhB,KAAKQ,kBAAmB,CAC1BR,KAAKwG,OAAOxF,E,MACP,GAAIhB,KAAKE,iBAAkB,CAChCF,KAAKyG,gBAAgBzF,E,KAClB,CACHhB,KAAKyG,gBAAgBzF,E,GAM3B,YAAAuF,CAAavF,GACX,MAAM0F,EAAW1G,KAAK2G,6BAA6B3F,EAAE8E,QAAQ,GAAG9E,EAAE8E,QAAQ,IAC1E,GAAI9F,KAAKM,iBAAkB,CACzB,GAAKoG,EAAW1G,KAAKM,iBAAkB,EAAG,CACxCN,KAAK4G,MAAQpB,KAAKqB,IAAI,GAAI7G,KAAK4G,MAAQ,I,KACpC,CACH5G,KAAK4G,MAAQpB,KAAKsB,IAAI,GAAI9G,KAAK4G,MAAQ,I,CAEzC5G,KAAKM,iBAAmBoG,C,KACrB,CACH1G,KAAKM,iBAAmBoG,C,EAI5B,4BAAAC,CAA6BI,EAAaC,GACxC,MAAMC,EAAUF,EAAOG,QAAUF,EAAOE,QACxC,MAAMC,EAAUJ,EAAOK,QAAUJ,EAAOI,QACxC,MAAMV,EAAWO,EAAUA,EAAUE,EAAUA,EAC/C,OAAOT,C,CAGT,kBAAAW,GACErH,KAAKQ,kBAAoBD,UACzB,IAAKP,KAAKW,gBAAiB,CACzBX,KAAK2E,sBAAwB,EAC7B3E,KAAKE,iBAAmB,MACxBF,KAAKkB,e,EAIT,cAAAoG,CAAetG,GACb,IAAKhB,KAAKW,gBAAiB,CACzB,IAAIgF,EAAQ3F,KAAK4F,iBAAiB5E,EAAEhB,KAAK6F,YACzC7F,KAAKQ,kBAAoB,CAACJ,EAAEuF,EAAMvF,EAAEC,EAAEsF,EAAMtF,E,EAIhD,gBAAAkH,CAAiBvG,GACf,GAAIA,EAAEwG,OAAO,EAAG,CACdxH,KAAK4G,MAAQ5G,KAAK4G,MAAQ,E,KACvB,CACH5G,KAAK4G,MAAQpB,KAAKsB,IAAI,GAAK9G,KAAK4G,MAAQ,G,CAE1C5F,EAAEsF,gB,CAGJ,oBAAArF,CAAqBD,GACnBA,EAAEsF,iBACF,GAAItF,EAAE8E,QAAQC,SAAW,EAAG,CAC1B/F,KAAKuG,aAAavF,E,EAItB,kBAAAyG,GACE,GAAIzH,KAAKoC,IAAK,CAGZ,MAAO,sBAAsBpC,KAAK0H,c,KAE/B,CACH,MAAO,Y,EAIX,cAAAC,CAAe3G,GACb,GAAIhB,KAAKQ,kBAAmB,CAC1BR,KAAKwG,OAAOxF,E,KACT,CACHhB,KAAKyG,gBAAgBzF,E,EAIzB,MAAAwF,CAAOxF,GACL,IAAI2E,EAAQ3F,KAAK4F,iBAAiB5E,EAAEhB,KAAK6F,YACzC,IAAIoB,EAAUtB,EAAMvF,EAAIJ,KAAKQ,kBAAkBJ,EAC/C,IAAI+G,EAAUxB,EAAMtF,EAAIL,KAAKQ,kBAAkBH,EAS/CL,KAAKiH,QAAUjH,KAAKiH,QAAUA,EAC9BjH,KAAKmH,QAAUnH,KAAKmH,QAAUA,C,CAGhC,eAAAV,CAAgBzF,GACd,GAAIhB,KAAKE,iBAAkB,CACzB,IAAIyF,EAAQ3F,KAAK4F,iBAAiB5E,EAAEhB,KAAK6F,YACzC,IAAIoB,EAAUtB,EAAMvF,EAAIJ,KAAKG,sBAAsBC,EACnD,IAAI+G,EAAUxB,EAAMtF,EAAIL,KAAKG,sBAAsBE,EACnD,IAAIuH,EAAY5B,KAAKC,MAAMD,KAAKE,UAAUlG,KAAKU,iBAC/C,IAAK,MAAMmH,KAASD,EAAW,CAC7BC,EAAMzH,EAAIyH,EAAMzH,EAAI6G,EACpBY,EAAMxH,EAAIwH,EAAMxH,EAAI8G,EACpB,GAAIU,EAAMzH,EAAI,GAAKyH,EAAMxH,EAAI,GAAKwH,EAAMzH,EAAIJ,KAAKoC,IAAIX,cAAgBoG,EAAMxH,EAAIL,KAAKoC,IAAIV,cAAc,CACpGL,QAAQC,IAAI,gBACZ,M,EAGJtB,KAAKkC,OAAS0F,EACd5H,KAAK8H,gBACL9H,KAAK+H,gBAAgBpC,E,CAEvB,GAAI3F,KAAK2E,sBAAwB,EAAG,CAClC,IAAIqD,EAAahI,KAAKiI,8BAA8BjI,KAAK2E,sBACzD,IAAIgB,EAAQ3F,KAAK4F,iBAAiB5E,EAAEhB,KAAK6F,YACzC,IAAIoB,EAAUtB,EAAMvF,EAAIJ,KAAKS,sBAAsBL,EACnD,IAAI+G,EAAUxB,EAAMtF,EAAIL,KAAKS,sBAAsBJ,EACnD,IAAIuH,EAAY5B,KAAKC,MAAMD,KAAKE,UAAUlG,KAAKU,iBAC/C,GAAIsH,IAAe,EAAG,CACpB,IAAIE,EAAgBN,EAAUI,GAC9BE,EAAc9H,EAAIJ,KAAKU,eAAesH,GAAY5H,EAAI6G,EACtDiB,EAAc7H,EAAIL,KAAKU,eAAesH,GAAY3H,EAAI8G,EACtD,GAAInH,KAAKY,YAAc,MAAO,CAC5B,GAAIoH,IAAe,EAAG,CACpBJ,EAAU,GAAGvH,EAAI6H,EAAc7H,EAC/BuH,EAAU,GAAGxH,EAAI8H,EAAc9H,C,MAC3B,GAAI4H,IAAe,EAAG,CAC1BJ,EAAU,GAAGvH,EAAI6H,EAAc7H,EAC/BuH,EAAU,GAAGxH,EAAI8H,EAAc9H,C,MAC3B,GAAI4H,IAAe,EAAG,CAC1BJ,EAAU,GAAGxH,EAAI8H,EAAc9H,EAC/BwH,EAAU,GAAGvH,EAAI6H,EAAc7H,C,MAC3B,GAAI2H,IAAe,EAAG,CAC1BJ,EAAU,GAAGxH,EAAI8H,EAAc9H,EAC/BwH,EAAU,GAAGvH,EAAI6H,EAAc7H,C,OAGhC,CACH,GAAIL,KAAK2E,uBAAyB,EAAG,CACnCiD,EAAU,GAAGvH,EAAIL,KAAKU,eAAe,GAAGL,EAAI8G,EAC5CS,EAAU,GAAGvH,EAAIL,KAAKU,eAAe,GAAGL,EAAI8G,C,MACxC,GAAInH,KAAK2E,uBAAyB,EAAG,CACzCiD,EAAU,GAAGxH,EAAIJ,KAAKU,eAAe,GAAGN,EAAI6G,EAC5CW,EAAU,GAAGxH,EAAIJ,KAAKU,eAAe,GAAGN,EAAI6G,C,MACxC,GAAIjH,KAAK2E,uBAAyB,EAAG,CACzCiD,EAAU,GAAGvH,EAAIL,KAAKU,eAAe,GAAGL,EAAI8G,EAC5CS,EAAU,GAAGvH,EAAIL,KAAKU,eAAe,GAAGL,EAAI8G,C,MACxC,GAAInH,KAAK2E,uBAAyB,EAAG,CACzCiD,EAAU,GAAGxH,EAAIJ,KAAKU,eAAe,GAAGN,EAAI6G,EAC5CW,EAAU,GAAGxH,EAAIJ,KAAKU,eAAe,GAAGN,EAAI6G,C,EAGhD,GAAIjH,KAAKoC,IAAK,CACZpC,KAAKqC,uBAAuBuF,EAAU5H,KAAKoC,IAAIX,aAAazB,KAAKoC,IAAIV,c,CAEvE1B,KAAKkC,OAAS0F,EACd5H,KAAK8H,gBACL9H,KAAK+H,gBAAgBpC,E,EAIzB,sBAAAtD,CAAuBH,EAAiCiG,EAAgBC,GACtE,IAAK,IAAIpE,EAAQ,EAAGA,EAAQ9B,EAAO6D,OAAQ/B,IAAS,CAClD,MAAM6D,EAAQ3F,EAAO8B,GACrB6D,EAAMzH,EAAIoF,KAAKsB,IAAI,EAAEe,EAAMzH,GAC3ByH,EAAMzH,EAAIoF,KAAKqB,IAAIgB,EAAMzH,EAAE+H,GAC3BN,EAAMxH,EAAImF,KAAKsB,IAAI,EAAEe,EAAMxH,GAC3BwH,EAAMxH,EAAImF,KAAKqB,IAAIgB,EAAMxH,EAAE+H,E,EAI/B,kBAAAC,CAAmBrH,GACjBA,EAAEqF,kBACFrG,KAAKU,eAAiBsF,KAAKC,MAAMD,KAAKE,UAAUlG,KAAKkC,SACrDlC,KAAKE,iBAAmB,KACxB,IAAIyF,EAAQ3F,KAAK4F,iBAAiB5E,EAAEhB,KAAK6F,YACzC7F,KAAKG,sBAAsBC,EAAIuF,EAAMvF,EACrCJ,KAAKG,sBAAsBE,EAAIsF,EAAMtF,EACrCL,KAAK8H,e,CAGP,gBAAAQ,CAAiBtH,GACfA,EAAEqF,kBACF,IAAKrG,KAAKW,gBAAiB,CACzBX,KAAK2E,sBAAwB,EAC7B3E,KAAKE,iBAAmB,MACxBF,KAAKkB,e,EAIT,mBAAAqH,CAAoBvH,GAClBhB,KAAKW,gBAAkB,KACvBK,EAAEqF,kBACFrG,KAAK2E,sBAAwB,EAE7B3E,KAAKE,iBAAmB,KACxBF,KAAKU,eAAiBsF,KAAKC,MAAMD,KAAKE,UAAUlG,KAAKkC,SAErD,IAAIyD,EAAQ3F,KAAK4F,iBAAiB5E,EAAEhB,KAAK6F,YAGzC7F,KAAKG,sBAAwB,CAAEC,EAAGuF,EAAMvF,EAAGC,EAAGsF,EAAMtF,GACpDL,KAAK8H,e,CAIP,iBAAAU,CAAkBxH,GAChBA,EAAEqF,kBACFrG,KAAK2E,sBAAwB,EAC7B3E,KAAKE,iBAAmB,MACxBF,KAAKkB,e,CAGP,kBAAA2D,CAAmB7D,EAAagD,GAC9BhD,EAAEqF,kBACF,IAAIV,EAAQ3F,KAAK4F,iBAAiB5E,EAAEhB,KAAK6F,YACzC7F,KAAKU,eAAiBsF,KAAKC,MAAMD,KAAKE,UAAUlG,KAAKkC,SACrDlC,KAAKS,sBAAsBL,EAAIuF,EAAMvF,EACrCJ,KAAKS,sBAAsBJ,EAAIsF,EAAMtF,EACrCL,KAAK2E,qBAAuBX,C,CAG9B,gBAAAc,CAAiB9D,GACfA,EAAEqF,kBACF,IAAKrG,KAAKW,gBAAiB,CACzBX,KAAK2E,sBAAwB,EAC7B3E,KAAKkB,e,EAIT,mBAAA6D,CAAoB/D,EAAagD,GAC/BhE,KAAKW,gBAAkB,KACvBK,EAAEqF,kBACFrG,KAAKE,iBAAmB,MACxB,IAAIyF,EAAQ3F,KAAK4F,iBAAiB5E,EAAEhB,KAAK6F,YACzC7F,KAAKU,eAAiBsF,KAAKC,MAAMD,KAAKE,UAAUlG,KAAKkC,SACrDlC,KAAKS,sBAAsBL,EAAIuF,EAAMvF,EACrCJ,KAAKS,sBAAsBJ,EAAIsF,EAAMtF,EACrCL,KAAK2E,qBAAuBX,C,CAG9B,oBAAAiB,CAAqBjE,EAAegD,GAClC,GAAIhD,EAAEyH,aAAe,UAAYzI,KAAKW,gBAAiB,CACrDX,KAAK6E,mBAAmB7D,EAAEgD,GAC1BhD,EAAEsF,gB,EAIN,6BAAA2B,CAA8BjE,GAC5B,GAAIA,IAAU,EAAG,CACf,OAAO,C,MACH,GAAIA,IAAU,EAAG,CACrB,OAAO,C,MACH,GAAIA,IAAU,EAAG,CACrB,OAAO,C,MACH,GAAIA,IAAU,EAAG,CACrB,OAAO,C,CAET,OAAQ,C,CAIV,gBAAA4B,CAAiB8C,EAAYC,GAC3B,IAAIC,EAAMD,EAAIE,eACd,IAAKD,EAAK,CACR,MAAO,CAAExI,EAAG,EAAGC,EAAG,E,CAGpB,IAAID,EAAGC,EACP,GAAIqI,EAAMI,cAAe,CACvB1I,EAAIsI,EAAMI,cAAc,GAAG5B,QAC3B7G,EAAIqI,EAAMI,cAAc,GAAG1B,O,KACtB,CACLhH,EAAIsI,EAAMxB,QACV7G,EAAIqI,EAAMtB,O,CAIZ,IAAI2B,EAAMH,EAAII,EAAIJ,EAAIK,EAAIL,EAAIM,EAAIN,EAAIO,EACtC,GAAIJ,IAAQ,EAAG,CAEb,MAAO,CAAE3I,EAAG,EAAGC,EAAG,E,CAGpB,IAAI+I,EAAS,CACXJ,EAAGJ,EAAIK,EAAIF,EACXG,GAAIN,EAAIM,EAAIH,EACZI,GAAIP,EAAIO,EAAIJ,EACZE,EAAGL,EAAII,EAAID,EACX/H,GAAI4H,EAAIO,EAAIP,EAAIS,EAAIT,EAAIK,EAAIL,EAAI5H,GAAK+H,EACrCM,GAAIT,EAAIM,EAAIN,EAAI5H,EAAI4H,EAAII,EAAIJ,EAAIS,GAAKN,GAGvC,MAAO,CACL3I,GAAIA,EAAIwI,EAAI5H,GAAKoI,EAAOJ,GAAK3I,EAAIuI,EAAIS,GAAKD,EAAOD,EACjD9I,GAAID,EAAIwI,EAAI5H,GAAKoI,EAAOF,GAAK7I,EAAIuI,EAAIS,GAAKD,EAAOH,E,CAKrD,QAAA/E,GACE,GAAIlE,KAAKoC,IAAK,CACZ,OAAOpC,KAAKoC,IAAIX,aAAa,G,KAC1B,CACH,OAAO,C,EAKX,iBAAMF,GAEJvB,KAAK4G,MAAQ,EACb5G,KAAKiH,QAAU,EACfjH,KAAKmH,QAAU,C,CAIjB,sBAAMmC,CAAiBC,GAErB,IAAIC,EAAM,GACV,IAAK,IAAIxF,EAAQ,EAAGA,EAAQhE,KAAK4D,mBAAmBmC,OAAQ/B,IAAS,CACnE,IAAID,EAAY/D,KAAK4D,mBAAmBI,GACxC,GAAIuF,EAAW,CACb,GAAI,UAAWxF,GAAawF,IAAc,OAAQ,CAChDxF,EAAY,CAAC7B,OAAOlC,KAAKmC,kBAAkB4B,G,MACvC,KAAM,UAAWA,IAAcwF,IAAc,OAAO,CACxDxF,EAAY/D,KAAKyJ,kBAAkB1F,EAAU7B,O,EAGjDsH,EAAIE,KAAK3F,E,CAEX,IAAI4F,EAAU,KACd,GAAIJ,EAAW,CACb,GAAIA,IAAc,OAAQ,CACxBI,EAAU,K,MAET,CACH,IAAK3J,KAAKY,UAAW,CACnB+I,EAAU,K,EAGd,GAAIA,EAAS,CACX,MAAMC,QAAa5J,KAAK6J,UACxBL,EAAIE,KAAKE,E,KACN,CACH,MAAMtH,QAAatC,KAAK8J,UACxBN,EAAIE,KAAKpH,E,CAEX,OAAOkH,C,CAIT,eAAMO,GAEJ,OAAO/J,KAAKkC,M,CAId,aAAM2H,GAEJ,MAAO,CAAC3H,OAAOlC,KAAKkC,O,CAItB,aAAM4H,GAEJ,OAAO9J,KAAKyJ,kBAAkBzJ,KAAKkC,O,CAGrC,iBAAAuH,CAAkBvH,GAChB,IAAI8H,EACJ,IAAIC,EACJ,IAAIC,EACJ,IAAIC,EACJ,IAAK,MAAMtC,KAAS3F,EAAQ,CAC1B,IAAK8H,EAAM,CACTA,EAAOnC,EAAMzH,EACb8J,EAAOrC,EAAMzH,EACb6J,EAAOpC,EAAMxH,EACb8J,EAAOtC,EAAMxH,C,KACV,CACH2J,EAAOxE,KAAKqB,IAAIgB,EAAMzH,EAAE4J,GACxBC,EAAOzE,KAAKqB,IAAIgB,EAAMxH,EAAE4J,GACxBC,EAAO1E,KAAKsB,IAAIe,EAAMzH,EAAE8J,GACxBC,EAAO3E,KAAKsB,IAAIe,EAAMxH,EAAE8J,E,EAG5BH,EAAOxE,KAAK4E,MAAMJ,GAClBE,EAAO1E,KAAK4E,MAAMF,GAClBD,EAAOzE,KAAK4E,MAAMH,GAClBE,EAAO3E,KAAK4E,MAAMD,GAClB,MAAO,CAAC/J,EAAE4J,EAAK3J,EAAE4J,EAAKxH,MAAMyH,EAAOF,EAAKrH,OAAOwH,EAAOF,E,CAKxD,sBAAMI,CAAiBC,GACrB,OAAO,IAAIC,SAA0B,CAACC,EAASC,KAC7C,IAAIC,EAAS,IAAIC,WACjBD,EAAOE,cAAcN,GACrBI,EAAOG,UAAY,WACjB,IAAIC,EAAiBJ,EAAOK,OAC5B,IAAI3I,EAAM4I,SAASC,cAAc,OACjC7I,EAAI8I,OAAS,WACXV,EAAQpI,E,EAEVA,EAAI+I,QAAU,WACZV,G,EAEFrI,EAAIsB,IAAMoH,C,CACX,G,CAIL,yBAAMM,CAAoBd,GACxB,OAAO,IAAIC,SAA0B,CAACC,EAASC,KAC7C,IAAIrI,EAAM4I,SAASC,cAAc,OACjC7I,EAAI8I,OAAS,WACXV,EAAQpI,E,EAEVA,EAAI+I,QAAU,WACZV,G,EAEFrI,EAAIsB,IAAM4G,CAAM,G,CAKrB,YAAMe,GAAM,CAIX,WAAAC,GACE,GAAItL,KAAKoC,KAAOpC,KAAK6F,WAAY,CAC/B7F,KAAK6F,WAAW/D,MAAMa,OAAS,OAC/B,IAAI4I,EAAWvL,KAAKoC,IAAIX,aAAazB,KAAKoC,IAAIV,cAC9C,IAAIe,EAAQzC,KAAK6F,WAAW2F,aAAeD,EAC3C,GAAI9I,EAAMzC,KAAK6F,WAAW4F,cAAcC,YAAa,CACnDjJ,EAAQzC,KAAK6F,WAAW4F,cAAcC,YACtC1L,KAAK6F,WAAW/D,MAAMa,OAASF,EAAQ8I,EAAW,I,CAEpD,OAAO9I,C,CAET,MAAO,M,CAGT,gBAAAkJ,CAAiB3K,GACf,GAAIA,EAAEyH,aAAe,UAAYzI,KAAKW,gBAAiB,CACrDK,EAAEqF,kBACFrF,EAAEsF,iBACFtG,KAAK2H,eAAe3G,E,EAIxB,gBAAA4K,CAAiB5K,GACf,GAAIA,EAAEyH,aAAe,UAAYzI,KAAKW,gBAAiB,CACrDX,KAAKsH,eAAetG,E,EAIxB,cAAA6K,CAAe7K,GACb,GAAIA,EAAEyH,aAAe,UAAYzI,KAAKW,gBAAiB,CACrDX,KAAKQ,kBAAoBD,UACzBP,KAAK2E,sBAAwB,C,EAIjC,oBAAAmH,CAAqB9K,GACnB,GAAIA,EAAEyH,aAAe,UAAYzI,KAAKW,gBAAiB,CACrDX,KAAKqI,mBAAmBrH,E,EAI5B,kBAAA+K,CAAmB/K,GACjBA,EAAEqF,kBACFrG,KAAK2E,sBAAwB,EAC7B3E,KAAKE,iBAAmB,K,CAG1B,MAAA8L,GACE,OACEzI,EAAC0I,EAAI,CAAA/G,IAAA,2CAACgH,IAAMC,GAAOnM,KAAK2B,KAAOwK,GAC7B5I,EAAA,OAAA2B,IAAA,2CAAK1B,MAAM,qBACT0I,IAAMC,GAAOnM,KAAKc,iBAAmBqL,EACrCC,QAAUpL,GAAehB,KAAKuH,iBAAiBvG,GAC/CoD,UAAW,IAAIpE,KAAKqH,sBAEpB9D,EAAA,UAAA2B,IAAA,2CACEgH,IAAMC,GAAOnM,KAAKqM,cAAgBF,EAClC3I,MAAM,kBAERD,EAAA,OAAA2B,IAAA,2CACEoH,QAAQ,MACRJ,IAAMC,GAAOnM,KAAK6F,WAAasG,EAC/B3I,MAAM,cACN+I,MAAM,6BACN/K,QAASxB,KAAKwB,QACdiB,MAAOzC,KAAKsL,cACZxJ,MAAO,CAAC0K,UAAUxM,KAAKyH,sBACvBgF,YAAczL,GAAehB,KAAK2H,eAAe3G,GACjD4D,YAAc5D,GAAehB,KAAKsH,eAAetG,GACjDsD,aAAetD,GAAehB,KAAK0F,gBAAgB1E,GACnD0L,WAAY,IAAI1M,KAAKmG,gBACrBwG,YAAc3L,GAAehB,KAAKoG,eAAepF,GACjD4L,cAAgB5L,GAAiBhB,KAAK2L,iBAAiB3K,GACvDgE,cAAgBhE,GAAiBhB,KAAK4L,iBAAiB5K,GACvD6L,YAAc7L,GAAiBhB,KAAK6L,eAAe7K,IAEnDuC,EAAA,SAAA2B,IAAA,2CAAO4H,KAAM9M,KAAKoC,IAAMpC,KAAKoC,IAAIsB,IAAM,KACtC1D,KAAK2D,2BACNJ,EAAA,WAAA2B,IAAA,2CACEhD,OAAQlC,KAAKmD,gBACbK,MAAM,0BAAyB,eACjBxD,KAAKgC,aAAehC,KAAKkE,WACvCC,KAAK,cACLS,YAAc5D,GAAehB,KAAKqI,mBAAmBrH,GACrDoD,UAAYpD,GAAehB,KAAKsI,iBAAiBtH,GACjDsD,aAAetD,GAAehB,KAAKuI,oBAAoBvH,GACvD0L,WAAa1L,GAAehB,KAAKwI,kBAAkBxH,GACnDgE,cAAgBhE,GAAiBhB,KAAK8L,qBAAqB9K,GAC3D6L,YAAc7L,GAAiBhB,KAAK+L,mBAAmB/K,KAGxDhB,KAAKwE,kBAEPxE,KAAKqD,eACNE,EAAA,OAAA2B,IAAA,2CAAK1B,MAAM,YAAY0I,IAAMC,GAAOnM,KAAK+M,iBAAmBZ,IAC5D5I,EAAA,QAAA2B,IAAA,8C,CAMR,aAAA4C,GACE,GAAI9H,KAAK+M,iBAAkB,CACzB/M,KAAK+M,iBAAiBjL,MAAMkL,QAAU,O,EAI1C,aAAA9L,GACE,GAAIlB,KAAK+M,iBAAkB,CACzB/M,KAAK+M,iBAAiBjL,MAAMkL,QAAU,M,EAI1C,eAAAjF,CAAgBpC,GACd,IAAK3F,KAAK+M,mBAAqB/M,KAAKoC,IAAK,OAGzC,MAAME,EAAOtC,KAAKyJ,kBAAkBzJ,KAAKkC,QAGzC,MAAM+K,EAAgB,IAGtB,MAAMC,EAAgBvH,EAAMvF,EAAI6M,EAChC,MAAME,EAAexH,EAAMtF,EAAI4M,EAG/B,MAAMpH,EAAa7F,KAAK6F,WAGxB,GAAIA,EAAWgD,cAAgBhD,EAAWuH,eAAgB,CACxD,MAAMC,EAAMxH,EAAWgD,eACvB,MAAMhB,EAAQhC,EAAWuH,iBACzBvF,EAAMzH,EAAI8M,EACVrF,EAAMxH,EAAI8M,EACV,MAAMG,EAAmBzF,EAAM0F,gBAAgBF,GAG/CrN,KAAK+M,iBAAiBjL,MAAM0L,KAAO,GAAGF,EAAiBlN,EAAI,OAC3DJ,KAAK+M,iBAAiBjL,MAAM2L,IAAM,GAAGH,EAAiBjN,EAAI,O,KACrD,CAELL,KAAK+M,iBAAiBjL,MAAM0L,KAAO,GAAGN,MACtClN,KAAK+M,iBAAiBjL,MAAM2L,IAAM,GAAGN,K,CAIvC,MAAMO,EAAY,GAClB,MAAMC,EAAKnI,KAAKsB,IAAI,EAAGxE,EAAKlC,GAAKuF,EAAMvF,EAAIkC,EAAKlC,GAAKJ,KAAK4G,MAAQqG,EAAgBS,EAAY,GAC9F,MAAME,EAAKpI,KAAKsB,IAAI,EAAGxE,EAAKjC,GAAKsF,EAAMtF,EAAIiC,EAAKjC,GAAKL,KAAK4G,MAAQqG,EAAgBS,EAAY,GAC9F,MAAMG,EAAKZ,EAAgBS,EAC3B,MAAMI,EAAKb,EAAgBS,EAC3B,MAAMK,EAAK,EACX,MAAMC,EAAK,EACX,MAAMC,EAAKhB,EACX,MAAMiB,EAAKjB,EAEX,MAAMkB,EAAkBnD,SAASC,cAAc,UAC/CkD,EAAgB1L,MAAQwK,EACxBkB,EAAgBxL,OAASsK,EACzB,MAAMmB,EAAeD,EAAgBE,WAAW,MAEhDD,EAAaE,UAAUtO,KAAKoC,IAAKuL,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,GAG7DE,EAAaxH,MAAM8G,EAAWA,GAC9BU,EAAaG,YAAc,SAC3BH,EAAaI,UAAYxO,KAAKgC,aAAe0L,EAC7CU,EAAaK,YACbL,EAAaM,OAAQ1O,KAAKkC,OAAO,GAAG9B,EAAIuN,EAAM3N,KAAKkC,OAAO,GAAG7B,EAAIuN,GACjE,IAAK,IAAIe,EAAI,EAAGA,EAAI3O,KAAKkC,OAAO6D,OAAQ4I,IAAK,CAC3CP,EAAaQ,OAAQ5O,KAAKkC,OAAOyM,GAAGvO,EAAIuN,EAAM3N,KAAKkC,OAAOyM,GAAGtO,EAAIuN,E,CAEnEQ,EAAaS,YACbT,EAAaU,SAEb9O,KAAK+M,iBAAiBjL,MAAMiN,gBAAkB,OAAOZ,EAAgBa,c"}