# Prod environment variables
# Application Environment
ENVIRONMENT=prod
DEBUG=True
SYS_ARGV=api

# Tesseract OCR Configuration
TESSERACT_PATH=/usr/bin/tesseract

# API Configuration
API_URL=https://windoc-api.sophatel.com
TAP_URL=https://tap.sophatel.com

# WinPlus ERP Integration
WINPLUS_AUTH_USER=https://winpharmplus.ma/api/user/auth
WINPLUS_AUTH_TENANT=https://winpharmplus.ma/api/user/auth-tenant
WINPLUS_URL=https://winpharmplus.ma

# Pharmalien Integration
PHARMALIEN_AUTH_URL=https://pharmalien.ma/api/user/auth

# JWT Authentication
SECRET_KEY=paython-ocr-insecure-#8!7z!_
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=180 # 3 hours

# PostgreSQL Database Configuration (VPS)
POSTGRES_HOST=pgsql.prod
POSTGRES_PORT=5432
POSTGRES_DB=ocr_document_grossiste
POSTGRES_USER=postgres 
POSTGRES_PASSWORD=Uyj33uNV%23-4%28Jo%295
POSTGRES_SSL_MODE=prefer
