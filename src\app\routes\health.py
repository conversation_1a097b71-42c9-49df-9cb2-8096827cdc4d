"""
Health check endpoints for monitoring database and system status.
"""

import logging
from fastapi import APIRouter, HTTPException
from typing import Dict, Any
from datetime import datetime

from src.app.utils.db_operations import get_database_health

router = APIRouter()
logger = logging.getLogger(__name__)


@router.get("/health")
async def health_check() -> Dict[str, Any]:
    """
    Basic health check endpoint.
    Returns overall system health status.
    """
    try:
        db_health = get_database_health()

        # Determine overall health
        db_healthy = db_health.get('available', False)

        return {
            "status": "healthy" if db_healthy else "unhealthy",
            "timestamp": datetime.now().isoformat(),
            "version": "1.0.0",
            "database": {
                "type": "postgresql",
                "status": "healthy" if db_healthy else "unhealthy"
            }
        }

    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=503, detail="Service unavailable")


@router.get("/health/database")
async def database_health() -> Dict[str, Any]:
    """
    Detailed database health check endpoint.
    Returns status of the PostgreSQL database.
    """
    try:
        db_health = get_database_health()

        return {
            "timestamp": datetime.now().isoformat(),
            "configuration": {
                "database_type": "postgresql",
                "single_database": True
            },
            "database": db_health,
            "summary": {
                "database_healthy": db_health.get('available', False),
                "error": db_health.get('error')
            }
        }

    except Exception as e:
        logger.error(f"Database health check failed: {e}")
        raise HTTPException(status_code=503, detail="Database health check failed")


@router.get("/health/detailed")
async def detailed_health() -> Dict[str, Any]:
    """
    Comprehensive health check with detailed system information.
    """
    try:
        db_health = get_database_health()

        # Calculate database status
        db_healthy = db_health.get('available', False)

        # Determine system status
        system_status = "optimal" if db_healthy else "critical"

        return {
            "timestamp": datetime.now().isoformat(),
            "system": {
                "status": system_status,
                "version": "1.0.0",
                "environment": "production"  # This could be read from config
            },
            "database": {
                "configuration": {
                    "type": "postgresql",
                    "single_database": True
                },
                "health": db_health,
                "status": {
                    "available": db_healthy,
                    "error": db_health.get('error')
                }
            },
            "features": {
                "ocr_processing": True,
                "multi_supplier": True,
                "real_time_progress": True,
                "postgresql_database": True,
                "api_authentication": True
            },
            "recommendations": _get_health_recommendations(db_health, db_healthy)
        }

    except Exception as e:
        logger.error(f"Detailed health check failed: {e}")
        raise HTTPException(status_code=503, detail="Detailed health check failed")


def _get_health_recommendations(db_health: Dict, db_healthy: bool) -> list:
    """Generate health recommendations based on current status."""
    recommendations = []

    if not db_healthy:
        recommendations.append({
            "level": "critical",
            "message": "PostgreSQL database is not available",
            "action": "Check database connection and credentials"
        })

    # Check for specific database errors
    if not db_healthy and db_health.get('error'):
        recommendations.append({
            "level": "error",
            "message": f"PostgreSQL database error: {db_health['error']}",
            "action": "Investigate PostgreSQL database connectivity"
        })

    if not recommendations:
        recommendations.append({
            "level": "success",
            "message": "All systems are operating normally",
            "action": "No action required"
        })

    return recommendations
