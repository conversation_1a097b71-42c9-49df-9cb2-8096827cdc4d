@import url("https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap");

* {
  font-family: "Inter", sans-serif;
  font-optical-sizing: auto;
}

ion-content::part(scroll) {
  overflow-y: hidden !important;
  --overflow: hidden !important;
}
ion-content {
  --offset-top : 0px !important;
  display: flex;
  justify-content: center;
  align-items: center;
}

ion-footer {
  background-color: #e5e7eb; // Adjust the background color as needed
}

.camera-button {
  --background: #3b82f6; // Adjust the color as needed
  --background-activated: #2563eb; // Adjust the color as needed
  border-radius: 50%;
  width: 60px;
  height: 60px;
  margin-top: -30px; // Position the button above the toolbar
}

ion-toolbar {
  --background: transparent;
  --ion-color-primary: #3b82f6; // Adjust the color as needed
}

ion-button {
  --color: #3b82f6; // Adjust the color as needed
}

ion-button[slot="icon-only"] {
  --color: #3b82f6; // Adjust the color as needed
}

// ion-icon {
//   font-size: 24px;
// }

ion-footer {
  position: relative;
  background-color: #dddbff;
  height: 110px;
  // border-top: 1px solid #2563eb;
  width: 100%;

  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: row;
}

ion-footer ion-toolbar {
  --border-width: 0;
}

ion-footer ion-toolbar ion-buttons,
ion-footer ion-toolbar ion-buttons ion-button {
  display: flex;
  justify-content: space-around;
  align-items: center;
  flex-direction: row;
}

::ng-deep ion-button.menu-button app-custom-icon img {
  width: 30px !important;
  height: 30px !important;
  color: #000;
}
::ng-deep .menu-button.active app-custom-icon img {
  color: #2f4fcd;
}

::ng-deep .menu-button-middle {
  background-color: #2f4fcd;
  padding: 2px 12px;
  border-radius: 14px;
  width: 85px;
  height: 60px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 15px;
  app-custom-icon img {
    width: 45px !important;
    height: 45px !important;
    color: #fff;
  }
}

.videoCamera {
  width: 100%;
  height: 100%;
  background-color: #000;
  display: flex !important;
  justify-content: center !important;
  align-items: flex-start !important;
}

.videoCanva{
  position: relative;
  width: 100%;
  height: auto;
  padding: 0;
  margin: 0;
}
.videoCanva canvas{
  width: 100%;
  height: 100%;
  padding: 0;
  margin: 0;
}




// realtime-contours.page.scss
.camera-container {
  position: relative;
  width: 100%;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #000;
  
  canvas {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
}

:host {
  display: block;
  width: 100%;
  height: 100%;
}