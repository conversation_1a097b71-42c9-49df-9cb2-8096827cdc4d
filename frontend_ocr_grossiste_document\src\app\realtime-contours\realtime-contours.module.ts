import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { RealtimeContoursPageRoutingModule } from './realtime-contours-routing.module';

import { RealtimeContoursPage } from './realtime-contours.page';
import { SharedModule } from '../shared/shared.module'; // Import SharedModule

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    RealtimeContoursPageRoutingModule,
    SharedModule
  ],
  declarations: [RealtimeContoursPage]
})
export class RealtimeContoursPageModule {}
