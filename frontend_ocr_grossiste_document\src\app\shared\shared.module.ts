import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CustomIconComponent } from '../custom-icon/custom-icon.component';
import { CheckNetworkComponent } from '../check-network/check-network.component';
import { IonicModule } from '@ionic/angular';
import { CustomLoadingComponent } from '../custom-loading/custom-loading.component';
import { CustomAlertComponent } from '../custom-alert/custom-alert.component';
import { ImageModalComponent } from '../image-modal/image-modal.component';

@NgModule({
  declarations: [CustomIconComponent, CheckNetworkComponent, CustomLoadingComponent, CustomAlertComponent, ImageModalComponent ],
  imports: [CommonModule, IonicModule],
  exports: [CustomIconComponent, CheckNetworkComponent, CustomLoadingComponent, CustomAlertComponent, ImageModalComponent] // Export it so it can be used in other modules
})
export class SharedModule {}