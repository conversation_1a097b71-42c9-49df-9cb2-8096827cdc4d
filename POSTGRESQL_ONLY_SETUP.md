# PostgreSQL-Only Database Setup Guide

## 🎯 Overview

The OCR Document Grossiste project now uses a **simplified PostgreSQL-only architecture**:

- **PostgreSQL**: Single, reliable database for all operations
- **Removed**: SQLite and dual database complexity
- **Benefits**: Simpler architecture, easier maintenance, better performance

## 📋 Prerequisites

1. **PostgreSQL Server**: Cloud-hosted, VPS-hosted, or local
2. **Python Dependencies**: PostgreSQL drivers and SQLAlchemy
3. **Environment Configuration**: Database connection settings

## 🚀 Quick Setup

### 1. Install PostgreSQL Dependencies

```bash
# Install PostgreSQL-specific dependencies
pip install -r requirements-postgresql.txt

# Or install individually:
pip install psycopg2-binary==2.9.9 SQLAlchemy==2.0.29
```

### 2. Configure Environment Variables

Copy the environment template and configure your database settings:

```bash
cp .env.template .env.local  # For local development
# or
cp .env.template .env.prod   # For production
```

Edit your environment file with your PostgreSQL credentials:

```env
# PostgreSQL Configuration
POSTGRES_HOST=your-postgres-host.com
POSTGRES_PORT=5432
POSTGRES_DB=ocr_document_grossiste
POSTGRES_USER=your_username
POSTGRES_PASSWORD=your_password
POSTGRES_SSL_MODE=require
```

### 3. Initialize PostgreSQL Database

Run the initialization script to create tables:

```bash
python scripts/init_postgresql_simple.py
```

### 4. Test Database Connection

Verify everything is working:

```bash
python scripts/test_connection.py
```

### 5. Start Application

```bash
# Development
uvicorn src.api:app --host 0.0.0.0 --port 8088 --log-config=logging.yaml --env-file .env.local

# Production
uvicorn src.api:app --host 0.0.0.0 --port 8089 --log-config=logging.yaml --env-file .env.prod
```

## 🏗️ Database Architecture

### Single PostgreSQL Database

```sql
CREATE TABLE pre_bl_ocr (
    ID_BL SERIAL PRIMARY KEY,
    Content JSONB,
    ID_USER VARCHAR(255),
    status VARCHAR(50) DEFAULT 'EN_ATTENTE',
    ID_TENANT VARCHAR(255),
    CODE_TENANT VARCHAR(255),
    date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    id_BL_origine VARCHAR(255),
    date_BL_origine VARCHAR(255),
    supplier_name VARCHAR(255),
    supplier_id VARCHAR(255),
    random_id VARCHAR(255)
);

-- Additional tables
CREATE TABLE processing_logs (...);
CREATE TABLE supplier_configs (...);
```

### Simplified Operations

All database operations now use PostgreSQL directly:

```python
from src.app.utils.db_operations import (
    save_response_to_db,           # Saves to PostgreSQL
    get_all_pre_bl_ocr,           # Reads from PostgreSQL
    get_pre_bl_ocr_by_id,         # Reads from PostgreSQL
    update_bl_status,             # Updates PostgreSQL
    get_database_health           # Checks PostgreSQL connection
)
```

## 🌐 Cloud Deployment Options

### AWS RDS PostgreSQL
```env
POSTGRES_HOST=your-db.cluster-xyz.us-east-1.rds.amazonaws.com
POSTGRES_PORT=5432
POSTGRES_SSL_MODE=require
```

### Google Cloud SQL
```env
POSTGRES_HOST=your-project:region:instance-name
POSTGRES_PORT=5432
POSTGRES_SSL_MODE=require
```

### DigitalOcean Managed Database
```env
POSTGRES_HOST=your-db-do-user-123456-0.b.db.ondigitalocean.com
POSTGRES_PORT=25060
POSTGRES_SSL_MODE=require
```

### VPS Self-Hosted
```env
POSTGRES_HOST=your-vps-ip-address
POSTGRES_PORT=5432
POSTGRES_SSL_MODE=prefer
```

### Local Development with Docker
```bash
# Start PostgreSQL with Docker Compose
docker-compose -f docker-compose.dev.yml up -d

# Use these credentials in .env.local:
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=ocr_document_grossiste
POSTGRES_USER=ocr_user
POSTGRES_PASSWORD=ocr_password_dev
POSTGRES_SSL_MODE=disable
```

## 🔍 Health Monitoring

### Health Check Endpoints

- `GET /health` - Basic system health
- `GET /health/database` - Database-specific health
- `GET /health/detailed` - Comprehensive system status

### Example Health Response
```json
{
  "status": "healthy",
  "timestamp": "2025-06-20T11:30:00Z",
  "database": {
    "type": "postgresql",
    "status": "healthy"
  }
}
```

### Database Health Response
```json
{
  "configuration": {
    "database_type": "postgresql",
    "single_database": true
  },
  "database": {
    "available": true,
    "error": null
  },
  "summary": {
    "database_healthy": true,
    "error": null
  }
}
```

## 🛠️ Troubleshooting

### Connection Issues

1. **Test connection**:
   ```bash
   python scripts/test_connection.py
   ```

2. **Check environment variables**:
   ```bash
   python -c "from src.app.config import DATABASE_URL; print(DATABASE_URL)"
   ```

3. **Verify PostgreSQL is running**:
   ```bash
   # For Docker
   docker-compose -f docker-compose.dev.yml ps
   
   # For system service
   sudo systemctl status postgresql
   ```

### Common Solutions

1. **Connection refused**: Check if PostgreSQL is running and accessible
2. **Authentication failed**: Verify username and password
3. **Database not found**: Create the database or check the database name
4. **SSL issues**: Adjust `POSTGRES_SSL_MODE` setting

## 📊 Performance Benefits

### Simplified Architecture
- ✅ **Single database**: No dual-database complexity
- ✅ **Better performance**: Direct PostgreSQL operations
- ✅ **Easier maintenance**: One database to manage
- ✅ **Cleaner code**: Simplified database operations

### PostgreSQL Advantages
- ✅ **JSONB support**: Efficient JSON storage and querying
- ✅ **ACID compliance**: Data integrity and consistency
- ✅ **Scalability**: Handles large datasets efficiently
- ✅ **Advanced features**: Full-text search, indexing, etc.

## 🔄 Migration from Dual Database

If you previously had the dual database system:

1. **Data is preserved**: Your PostgreSQL data remains intact
2. **SQLite removed**: No more SQLite dependencies or operations
3. **Simplified config**: Remove dual database environment variables
4. **Same API**: All existing API endpoints work the same way

## 📞 Support

### Quick Diagnostics
```bash
# Test database health
python -c "from src.app.utils.db_operations import get_database_health; print(get_database_health())"

# Test connection
python scripts/test_connection.py

# Initialize database
python scripts/init_postgresql_simple.py
```

### Application Commands
```bash
# Development
uvicorn src.api:app --host 0.0.0.0 --port 8088 --log-config=logging.yaml --env-file .env.local

# Production
uvicorn src.api:app --host 0.0.0.0 --port 8089 --log-config=logging.yaml --env-file .env.prod
```

## 🎉 Benefits Achieved

1. **Simplified Architecture**: Single PostgreSQL database
2. **Better Performance**: Direct database operations
3. **Easier Maintenance**: No dual database complexity
4. **Cloud Ready**: Works with any PostgreSQL provider
5. **Reliable**: ACID compliance and data integrity
6. **Scalable**: PostgreSQL handles growth efficiently

The PostgreSQL-only implementation provides enterprise-grade reliability with a much simpler and more maintainable architecture!
