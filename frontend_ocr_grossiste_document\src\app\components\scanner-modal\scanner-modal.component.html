<!-- scanner-modal.component.html -->
<ion-header>
  <ion-toolbar>
    <ion-title>Sélectionnez l'image que vous souhaitez</ion-title>
    <ion-buttons slot="end">
      <ion-button (click)="dismiss()">
        <ion-icon name="close-outline"></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content>
  <ion-grid>
    <ion-row>
      <ion-col size="6" size-md="4" *ngFor="let scan of scanImages">
        <ion-card 
          [class.selected]="scan.selected"
          (click)="toggleSelection(scan, $event)">
          <img [src]="'data:image/jpeg;base64,' + scan.data" [alt]="'Scanned image ' + scan.id"/>
          <ion-card-content>
            <ion-item lines="none">
              <ion-checkbox 
                [(ngModel)]="scan.selected"
                (ionChange)="toggleSelection(scan)"
                [checked]="scan.selected">
              </ion-checkbox>
              <ion-label>Page {{scan.id + 1}}</ion-label>
            </ion-item>
          </ion-card-content>
        </ion-card>
      </ion-col>
    </ion-row>
  </ion-grid>
</ion-content>

<ion-footer>
  <ion-toolbar>
    <ion-text slot="start" class="ion-padding">
      {{getSelectedImages().length}} image selected
    </ion-text>
    <ion-buttons slot="end">
      <ion-button 
        (click)="confirm()" 
        [disabled]="getSelectedImages().length === 0"
        color="primary">
        Confirm
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-footer>
