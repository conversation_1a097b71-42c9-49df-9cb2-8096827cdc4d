// Scanner.service.ts
import { Injectable } from "@angular/core";
import { Subject, Subscription, throwError, Observable } from "rxjs";
import { webSocket, WebSocketSubject } from "rxjs/webSocket";
import { catchError, timeout } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class ScannerService {
  private socket$: WebSocketSubject<any> | null = null;
  private scannerSubscription!: Subscription;
  public resultScan$: Subject<string[]> = new Subject();
  private readonly WS_ENDPOINT = "ws://localhost:8889";

  async checkServerConnection(): Promise<boolean> {
    return new Promise((resolve) => {
      const ws = new WebSocket(this.WS_ENDPOINT);
      
      const timeoutId = setTimeout(() => {
        ws.close();
        resolve(false);
      }, 3000); // 3 seconds timeout

      ws.onopen = () => {
        clearTimeout(timeoutId);
        ws.close();
        resolve(true);
      };

      ws.onerror = () => {
        clearTimeout(timeoutId);
        resolve(false);
      };
    });
  }

  private connect(): WebSocketSubject<any> {
    if (!this.socket$ || this.socket$.closed) {
      this.socket$ = webSocket({ 
        "url": this.WS_ENDPOINT, 
        deserializer: e => e.data 
      });

      if (this.scannerSubscription) {
        this.scannerSubscription.unsubscribe();
      }

      this.scannerSubscription = this.socket$.asObservable().subscribe(
        response => {
          response = JSON.parse(response);
          if (Array.isArray(response)) {
            this.resultScan$.next(response);
          }
        },
        err => {
          console.error('Scanner WebSocket error:', err);
          this.socket$ = null;
        },
        () => {
          console.log('Scanner WebSocket closed');
          this.socket$ = null;
        }
      );
    }
    return this.socket$;
  }

  public async launchScan(): Promise<boolean> {
    try {
      const isServerRunning = await this.checkServerConnection();
      if (!isServerRunning) {
        throw new Error("Scanner server not running");
      }
      this.connect().next(null);
      return true;
    } catch (error) {
      console.error('Failed to launch scan:', error);
      return false;
    }
  }

  public close() {
    if (this.socket$) {
      this.connect().complete();
      if (this.scannerSubscription) {
        this.scannerSubscription.unsubscribe();
      }
    }
  }
}
