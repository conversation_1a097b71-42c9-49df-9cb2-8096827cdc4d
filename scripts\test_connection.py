#!/usr/bin/env python3
"""
Simple PostgreSQL connection test script.
This script tests the basic connection to PostgreSQL without the full application setup.
"""

import sys
import os
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import psycopg2
from sqlalchemy import create_engine, text
from src.app.config import (
    POSTGRES_HOST, POSTGRES_PORT, POSTGRES_DB,
    POSTGRES_USER, POSTGRES_PASSWORD, DATABASE_URL
)

def test_psycopg2_connection():
    """Test direct psycopg2 connection."""
    print("🔍 Testing direct psycopg2 connection...")
    try:
        conn = psycopg2.connect(
            host=POSTGRES_HOST,
            port=POSTGRES_PORT,
            database=POSTGRES_DB,
            user=POSTGRES_USER,
            password=POSTGRES_PASSWORD
        )
        cursor = conn.cursor()
        cursor.execute("SELECT version();")
        version = cursor.fetchone()
        print(f"✅ psycopg2 connection successful!")
        print(f"   PostgreSQL version: {version[0]}")
        cursor.close()
        conn.close()
        return True
    except Exception as e:
        print(f"❌ psycopg2 connection failed: {e}")
        return False

def test_sqlalchemy_connection():
    """Test SQLAlchemy connection."""
    print("\n🔍 Testing SQLAlchemy connection...")
    try:
        engine = create_engine(DATABASE_URL)
        with engine.connect() as conn:
            result = conn.execute(text("SELECT version();"))
            version = result.fetchone()
            print(f"✅ SQLAlchemy connection successful!")
            print(f"   PostgreSQL version: {version[0]}")
        return True
    except Exception as e:
        print(f"❌ SQLAlchemy connection failed: {e}")
        return False

def show_configuration():
    """Show current configuration."""
    print("📋 Current Configuration:")
    print(f"   Host: {POSTGRES_HOST}")
    print(f"   Port: {POSTGRES_PORT}")
    print(f"   Database: {POSTGRES_DB}")
    print(f"   User: {POSTGRES_USER}")
    print(f"   Password: {'*' * len(POSTGRES_PASSWORD) if POSTGRES_PASSWORD else 'NOT SET'}")
    print(f"   URL: {DATABASE_URL.split('@')[0]}@***")

def main():
    print("🚀 PostgreSQL Connection Test")
    print("=" * 50)
    
    show_configuration()
    print()
    
    # Test connections
    psycopg2_ok = test_psycopg2_connection()
    sqlalchemy_ok = test_sqlalchemy_connection()
    
    print("\n" + "=" * 50)
    if psycopg2_ok and sqlalchemy_ok:
        print("🎉 All connection tests passed!")
        print("You can now run: python scripts/init_postgresql.py")
    else:
        print("❌ Connection tests failed!")
        print("\n💡 Troubleshooting tips:")
        print("1. Make sure PostgreSQL is running")
        print("2. Check your environment variables in .env.local")
        print("3. Verify database credentials")
        print("4. Check firewall/network connectivity")
        print("5. For local development, try: docker-compose -f docker-compose.dev.yml up -d")

if __name__ == "__main__":
    main()
