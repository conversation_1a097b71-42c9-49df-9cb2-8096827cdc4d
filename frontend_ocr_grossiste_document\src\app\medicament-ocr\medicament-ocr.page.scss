@import url("https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap");

:host {
  height: 100dvh;
}

* {
  font-family: "Inter", sans-serif;
  font-optical-sizing: auto;
}

ion-content {
  --offset-top: 0px !important;
}

ion-header {
  height: 70px;
  --border: 0;
  display: flex;
  align-items: center;
}

ion-header ion-toolbar {
  --border: 0;
  --border-width: 0;
  display: flex;
  justify-content: center;
  align-items: flex-end;
  flex-direction: row;
  --background: #fff !important;
}

ion-header ion-toolbar ion-title {
  font-size: 26px;
  font-weight: 700;
  color: #2f4fcd;
  text-align: left;
  width: 100%;
  padding-left: 3.5rem;
  padding-right: 0 !important;
}

ion-header ion-toolbar ion-icon {
  color: #101010;
  padding-right: 1rem;
}

.scan-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 2rem;
}

::ng-deep .scan-wrapper .medicine-scan-icon img {
  width: 150px !important;
  height: 150px !important;
}

::ng-deep .scan-wrapper .arrow-bottom-icon img {
  width: 100px !important;
  height: 100px !important;
}

.scan-wrapper .content {
  padding: 10px 50px 0 50px;
}

.scan-wrapper .content h2 {
  color: #9a9a9a;
  font-size: 22px;
  font-weight: 700;
}

.scan-wrapper .content p {
  color: #9a9a9a;
  font-size: 12px;
  text-align: center;
  padding: 5px 10px;
}

.medicament-ocr-content {
  --background: #ffffff;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
}

.div-content {
  background: url("/assets/bg-scan-bl.png") no-repeat center center fixed;
  background-size: cover;
  min-height: 100%;
  padding-bottom: 80px;
}

.instructions {
  margin-top: 20px;
  text-align: center;
}

.instruction-text {
  font-size: 18px;
  color: #433f3f;
  margin-bottom: 10px;
  font-weight: 600;
  padding: 10px;
}

.section-title {
  margin-top: 20px;
  margin-bottom: 10px;
  color: #4b4b4b;
  font-size: 18px;
  margin-left: 16px;
  font-weight: 600;
}

// Scanned image styles
.scanned-image-container {
  padding: 0 16px;
}

.image-preview {
  width: 100%;
  display: flex;
  justify-content: center;
  margin-bottom: 16px;
  
  img {
    max-width: 100%;
    max-height: 250px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

.rescan-button {
  --background: #2f4fcd;
  margin: 0 auto 20px;
  max-width: 300px;
}

// Suggestions styles
.suggestions-container {
  padding: 0 16px;
}

.suggestion-list {
  margin-bottom: 20px;
}

.suggestion-card {
  margin-bottom: 16px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  border: 1px solid #e5ebfd;
}

ion-card-header {
  padding-bottom: 8px;
}

ion-card-title {
  font-size: 18px;
  font-weight: 600;
  color: #2f4fcd;
}

ion-card-subtitle {
  font-size: 14px;
  color: #666;
}

.suggestion-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  padding: 4px 0;
  border-bottom: 1px solid #f0f0f0;
  
  &:last-child {
    border-bottom: none;
  }
  
  .label {
    font-weight: 500;
    color: #555;
  }
  
  .value {
    color: #333;
  }
}

// No results found
.no-results {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30px 20px;
  
  ion-icon {
    font-size: 64px;
    color: #ccc;
    margin-bottom: 16px;
  }
  
  h3 {
    font-size: 20px;
    color: #666;
    margin-bottom: 8px;
  }
  
  p {
    color: #888;
    text-align: center;
  }
}

// FAB button styles
ion-fab {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}

::ng-deep ion-fab-button.menu-button-middle::part(native) {
  background: none;
  border: 0;
  box-shadow: none;
  width: 100% !important;
  color: #fff;
}

::ng-deep .menu-button-middle {
  background-color: #2f4fcd;
  padding: 2px 12px;
  border-radius: 14px;
  width: 85px;
  height: 60px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 15px;
  app-custom-icon img {
    width: 45px !important;
    height: 45px !important;
    color: #fff;
  }
}

::ng-deep ion-fab ion-fab-list {
  display: flex;
  flex-direction: row !important;
  justify-content: space-around;
  align-items: flex-end;
  width: auto !important;
  padding: 10px 20px;
  margin-bottom: 100px;
  height: 100vh;
  --transition-duration: 300ms;
  
  &.fab-active {
    visibility: visible;
    opacity: 1;
    transform: translateY(0);
    transition: all var(--transition-duration) ease-in-out;
    pointer-events: all;
    
    ion-fab-button {
      transform: translateY(0);
      opacity: 1;
    }
  }

  &.fab-hidden {
    visibility: hidden;
    opacity: 0;
    transform: translateY(20px);
    transition: all var(--transition-duration) ease-in-out;
    pointer-events: none;
    
    ion-fab-button {
      transform: translateY(40px);
      opacity: 0;
    }
  }

  ion-fab-button {
    transition: all var(--transition-duration) ease-in-out;
  }
}

.content-fab-buttom {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}

::ng-deep .content-fab-buttom app-custom-icon img {
  width: 50px !important;
  height: 50px !important;
}

::ng-deep .content-fab-buttom ion-label {
  color: #2f4fcd;
  font-size: 20px;
}

::ng-deep ion-fab ion-fab-list ion-fab-button {
  padding: 10px;
  width: 190px;
  height: 135px;
  transform: translateY(40px);
  transition: all 0.6s ease-in-out;
  
  &.fab-active {
    transform: translateY(0);
  }
}

::ng-deep ion-fab ion-fab-list ion-fab-button::part(native) {
  border-radius: 16px !important;
  --background: #f4f5f8 !important;
}

// Loading styles
::ng-deep .loading:not(.alert-progress) {
  opacity: 0.5;
  pointer-events: none;
  --background: rgba(0, 0, 0, 0.1);
}

::ng-deep .alert-progress {
  position: absolute;
  width: 100%;
  top: 40%;
  justify-content: center;
  align-items: center;
  display: none;
}

::ng-deep .alert-progress.loading {
  display: flex;
}

::ng-deep .alert-progress app-custom-alert {
  width: 90%;
}


.scan-buttons {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-top: 20px;
    padding: 0 20px;
    width: 100%;
  }
  
  .camera-button, .gallery-button {
    --border-radius: 8px;
    height: 48px;
    font-weight: 500;
  }
  
  .camera-button {
    --background: #4a90e2;
    --background-activated: #3a80d2;
  }
  
  .gallery-button {
    --background: #5c6bc0;
    --background-activated: #4c5bb0;
  }

  ion-card{
    --background: #fff !important;
  }

@media (prefers-color-scheme: dark) {
  .bg-hide {
    --background: rgba(255, 255, 255, 0.9);
  }
}

@media (prefers-color-scheme: light) {
  .bg-hide {
    --background: rgba(0, 0, 0, 0.1);
  }
}
