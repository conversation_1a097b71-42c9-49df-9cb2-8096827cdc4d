import { Component, ViewChild, AfterViewInit, ElementRef,OnInit, inject } from '@angular/core';
import { ImageData } from 'src/models/ImageData';
import Swiper from 'swiper';
import { NavController, LoadingController, AlertController  } from '@ionic/angular';
import { SignalService } from '../services/signal.service';
import { TransformedDocData } from 'src/models/TransformedDocData';

@Component({
  selector: 'app-data-bl',
  templateUrl: './data-bl.page.html',
  styleUrls: ['./data-bl.page.scss'],
})
export class DataBLPage implements AfterViewInit, OnInit {
  @ViewChild('topSwiper', { static: true }) topSwiper: ElementRef | undefined;
  @ViewChild('bottomSwiper', { static: true }) bottomSwiper: ElementRef | undefined;

  currentSlideProducts: any[] = [];
  currentProductIndex: number = 1;
  currentPageIndex: number = 1;

  slidesData: TransformedDocData[] = [];
  navCtrl = inject(NavController);
  signalService = inject(SignalService);
  loadingController = inject(LoadingController);
  alertController = inject(AlertController);

  // slidesData = [
  //   {
  //     image: 'assets/doc1.jpg',
  //     title: 'Scan 01:11:2023 01:57:06',
  //     date: 'Aujourd\'hui',
  //     page_index: '1',
  //     products: [
  //       { designation: 'REPADINA 10OVUL OV', quantity: 2, expiryDate: '2023-12-01', ppv: 99.00, pph: 66.50, total: 133.00 },
  //       { designation: 'APIXOL SPR AD 30M AE', quantity: 3, expiryDate: '2024-01-01', ppv: 153.40, pph: 101.00, total: 303.00 },
  //       { designation: 'MAXICLAV 1G/125MG 24SACH SA', quantity: 4, expiryDate: '2024-04-01', ppv: 36.70, pph: 24.00, total: 96.00 },
  //     ]
  //   },
  //   {
  //     image: 'assets/doc2.png',
  //     title: 'Scan 01:11:2023 02:57:06',
  //     date: 'Aujourd\'hui',
  //     page_index: '2',
  //     products: [
  //       { designation: 'BIOMARTIAL 30 GELULE', quantity: 6, expiryDate: '2023-05-01', ppv: 123.00, pph: 86.10, total: 516.60 },
  //       { designation: 'ACFOL 5MG BT 28CP', quantity: 10, expiryDate: '2024-02-01', ppv: 26.20, pph: 17.34, total: 173.39 },
  //       { designation: 'BIOMARTIAL PLUS 30CP', quantity: 2, expiryDate: '2025-01-01', ppv: 133.00, pph: 93.10, total: 186.20 },
  //       { designation: 'MENOPHYT BT/30CPS', quantity: 6, expiryDate: '2023-05-01', ppv: 123.00, pph: 86.10, total: 516.60 },
  //     ]
  //   },
  //   {
  //     image: 'assets/doc3.png',
  //     title: 'Scan 01:11:2023 03:57:06',
  //     date: 'Aujourd\'hui',
  //     page_index: '3',
  //     products: [
  //       { designation: 'CARBOFLORE BT/30GLLES', quantity: 2, expiryDate: '2023-12-01', ppv: 99.00, pph: 66.50, total: 133.00 },
  //       { designation: 'DIGESTAL BT/30CPS', quantity: 3, expiryDate: '2024-01-02', ppv: 153.40, pph: 101.00, total: 303.00 },
  //       { designation: 'FORTIVISION BT/30DG', quantity: 4, expiryDate: '2024-04-24', ppv: 36.70, pph: 24.00, total: 96.00 },
  //     ]
  //   },
  //   {
  //     image: 'assets/doc2.png',
  //     title: 'Scan 01:11:2023 02:57:06',
  //     date: 'Aujourd\'hui',
  //     page_index: '2',
  //     products: [
  //       { designation: 'LEVUPHTA 0.05% COLLYRE', quantity: 2, expiryDate: '2023-12-01', ppv: 99.00, pph: 66.50, total: 133.00 },
  //       { designation: 'CETIRAL 10MG BT/15 CP', quantity: 3, expiryDate: '2024-01-01', ppv: 153.40, pph: 101.00, total: 303.00 },
  //       { designation: 'PURCARB BTE/30 GELULES', quantity: 4, expiryDate: '2024-04-01', ppv: 36.70, pph: 24.00, total: 96.00 },
  //     ]
  //   },
  //   {
  //     image: 'assets/doc3.png',
  //     title: 'Scan 01:11:2023 03:57:06',
  //     date: 'Aujourd\'hui',
  //     page_index: '3',
  //     products: [
  //       { designation: 'CATAFLAM 50MG BT/10 CP', quantity: 6, expiryDate: '2023-05-01', ppv: 123.00, pph: 86.10, total: 516.60 },
  //       { designation: 'ANAPRED 20MG BTE/20 CPS', quantity: 10, expiryDate: '2024-02-01', ppv: 26.20, pph: 17.34, total: 173.39 },
  //       { designation: 'NEOFORTAN 160MG BT/10 CP EF', quantity: 2, expiryDate: '2025-01-01', ppv: 133.00, pph: 93.10, total: 186.20 },
  //       { designation: 'CARDIX 6.25 MG BT/28 CP', quantity: 6, expiryDate: '2023-05-01', ppv: 123.00, pph: 86.10, total: 516.60 },
  //     ]
  //   },
  // ];

  constructor() {}

  ngOnInit() {
    this.slidesData = this.signalService.getTransformedData();
    if(this.slidesData.length === 0) {
      this.navCtrl.navigateBack('/scan-bl');
    }
    this.topSwiper?.nativeElement.swiper.update();
    this.bottomSwiper?.nativeElement.swiper.update();
    console.log('Received data:', this.slidesData);
  }

  ngAfterViewInit() {
    this.initSwipers();
  }

  onSlideChange(){
    console.log("changedslidechange");
    this.initSwipers()
  }
  initSwipers() {
    if (this.topSwiper && this.topSwiper.nativeElement && this.bottomSwiper && this.bottomSwiper.nativeElement) {
      const topSwiperInstance = this.topSwiper.nativeElement.swiper;
      const bottomSwiperInstance = this.bottomSwiper.nativeElement.swiper;
      this.updateBottomSwiper();
    }
  }

  updateBottomSwiper() {

    if (this.topSwiper && this.topSwiper.nativeElement) {
      const topSwiperInstance = this.topSwiper.nativeElement.swiper;
      const activeIndex = topSwiperInstance.realIndex;
      this.currentPageIndex = activeIndex + 1; // Update the current page index
      this.currentSlideProducts = this.slidesData[activeIndex]?.products || [];
      // this.currentSlideProducts = this.slidesData[activeIndex].products;
      this.updateProductIndex();
    }
  }

  updateProductIndex() {
    if (this.bottomSwiper && this.bottomSwiper.nativeElement) {
      const bottomSwiperInstance = this.bottomSwiper.nativeElement.swiper;
      const activeProductIndex = bottomSwiperInstance.realIndex;
      this.currentProductIndex = activeProductIndex + 1;
    }
  }

  slideNext() {
    if (this.bottomSwiper && this.bottomSwiper.nativeElement) {
      const bottomSwiperInstance = this.bottomSwiper.nativeElement.swiper;
      bottomSwiperInstance.slideNext();
      this.updateProductIndex();
    }
  }

  slidePrev() {
    if (this.bottomSwiper && this.bottomSwiper.nativeElement) {
      const bottomSwiperInstance = this.bottomSwiper.nativeElement.swiper;
      bottomSwiperInstance.slidePrev();
      this.updateProductIndex();
    }
  }


  async NewBL() {
    const alert = await this.alertController.create({
      header: 'Voulez-vous créer un nouveau document ?',
      buttons: [
        {
          text: 'Annuler',
          role: 'cancel',
          cssClass: 'custom-alert-button-rename-doc cancel',
          handler: () => {
            console.log('Confirm Cancel');
          },
        },
        {
          text: 'Nouveau BL',
          cssClass: 'custom-alert-button-rename-doc rename',
          handler: () => {
            this.signalService.removeAllData();
            localStorage.removeItem('selectedSupplier');
            this.navCtrl.navigateForward('/scan-bl');
          },
        },
      ],
    });

    await alert.present();
  }


  async EditCurrentBL(){
    const alert = await this.alertController.create({
      header: 'Voulez-vous vraiment modifier les pages de ce document ?',
      buttons: [
        {
          text: 'Annuler',
          role: 'cancel',
          cssClass: 'custom-alert-button-rename-doc cancel',
          handler: () => {
            console.log('Confirm Cancel');
          },
        },
        {
          text: 'Modifier',
          cssClass: 'custom-alert-button-rename-doc rename',
          handler: () => {
            this.navCtrl.navigateRoot('/process-doc');
          },
        },
      ],
    });

    await alert.present();
  }
}
