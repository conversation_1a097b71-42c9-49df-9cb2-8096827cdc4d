# PostgreSQL Database Setup Guide

This guide explains how to set up the dual database system (PostgreSQL + SQLite) for the OCR Document Grossiste project.

## 🎯 Overview

The project now supports **dual database architecture**:
- **PostgreSQL**: Primary cloud database for production data
- **SQLite**: Local backup database for redundancy and offline access

## 📋 Prerequisites

1. **PostgreSQL Server**: Either cloud-hosted or VPS-hosted
2. **Python Dependencies**: PostgreSQL drivers and SQLAlchemy
3. **Environment Configuration**: Database connection settings

## 🚀 Quick Setup

### 1. Install PostgreSQL Dependencies

```bash
# Install PostgreSQL-specific dependencies
pip install -r requirements-postgresql.txt

# Or install individually:
pip install psycopg2-binary==2.9.9 SQLAlchemy==2.0.29
```

### 2. Configure Environment Variables

Copy the environment template and configure your database settings:

```bash
cp .env.template .env.local  # For local development
# or
cp .env.template .env.prod   # For production
```

Edit your environment file with your PostgreSQL credentials:

```env
# PostgreSQL Configuration
POSTGRES_HOST=your-postgres-host.com
POSTGRES_PORT=5432
POSTGRES_DB=ocr_document_grossiste
POSTGRES_USER=your_username
POSTGRES_PASSWORD=your_password
POSTGRES_SSL_MODE=require

# Database Strategy
USE_DUAL_DATABASE=True
PRIMARY_DATABASE=postgresql
```

### 3. Initialize PostgreSQL Database

Run the initialization script to create tables:

```bash
python scripts/init_postgresql.py
```

### 4. Migrate Existing Data (Optional)

If you have existing SQLite data, migrate it to PostgreSQL:

```bash
# Dry run to see what would be migrated
python scripts/migrate_sqlite_to_postgresql.py --dry-run

# Perform actual migration
python scripts/migrate_sqlite_to_postgresql.py
```

## 🏗️ Database Architecture

### Table Structure

The PostgreSQL database mirrors the existing SQLite structure:

```sql
CREATE TABLE pre_bl_ocr (
    ID_BL SERIAL PRIMARY KEY,
    Content JSONB,
    ID_USER VARCHAR(255),
    status VARCHAR(50) DEFAULT 'EN_ATTENTE',
    ID_TENANT VARCHAR(255),
    CODE_TENANT VARCHAR(255),
    date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    id_BL_origine VARCHAR(255),
    date_BL_origine VARCHAR(255),
    supplier_name VARCHAR(255),
    supplier_id VARCHAR(255),
    random_id VARCHAR(255)
);
```

### Dual Database Operations

All database operations automatically:
1. **Write to primary database** (PostgreSQL)
2. **Write to backup database** (SQLite) 
3. **Fallback to backup** if primary fails
4. **Read from primary** with fallback to backup

## 🔧 Configuration Options

### Database Strategy Settings

```env
# Use both databases (recommended)
USE_DUAL_DATABASE=True
PRIMARY_DATABASE=postgresql

# Use only PostgreSQL
USE_DUAL_DATABASE=False
PRIMARY_DATABASE=postgresql

# Use only SQLite (fallback mode)
USE_DUAL_DATABASE=False
PRIMARY_DATABASE=sqlite
```

### SSL Configuration

```env
# For cloud databases (recommended)
POSTGRES_SSL_MODE=require

# For local/VPS databases
POSTGRES_SSL_MODE=prefer

# For development only
POSTGRES_SSL_MODE=disable
```

## 🌐 Cloud Provider Examples

### AWS RDS PostgreSQL

```env
POSTGRES_HOST=your-db.cluster-xyz.us-east-1.rds.amazonaws.com
POSTGRES_PORT=5432
POSTGRES_SSL_MODE=require
```

### Google Cloud SQL

```env
POSTGRES_HOST=your-project:region:instance-name
POSTGRES_PORT=5432
POSTGRES_SSL_MODE=require
```

### DigitalOcean Managed Database

```env
POSTGRES_HOST=your-db-do-user-123456-0.b.db.ondigitalocean.com
POSTGRES_PORT=25060
POSTGRES_SSL_MODE=require
```

### VPS Self-Hosted

```env
POSTGRES_HOST=your-vps-ip-address
POSTGRES_PORT=5432
POSTGRES_SSL_MODE=prefer
```

## 🔍 Health Monitoring

Check database health status:

```python
from src.app.utils.db_operations import get_database_health

health = get_database_health()
print(health)
# Output:
# {
#     'postgresql': {'available': True, 'error': None},
#     'sqlite': {'available': True, 'error': None}
# }
```

## 🛠️ Troubleshooting

### Connection Issues

1. **Check environment variables**:
   ```bash
   python -c "from src.app.config import POSTGRES_URL; print(POSTGRES_URL)"
   ```

2. **Test connection manually**:
   ```bash
   python scripts/init_postgresql.py
   ```

3. **Check firewall/security groups** for cloud databases

### Migration Issues

1. **Verify SQLite data**:
   ```bash
   sqlite3 src/database/ocr_document_grossiste.db "SELECT COUNT(*) FROM pre_bl_ocr;"
   ```

2. **Check PostgreSQL tables**:
   ```sql
   SELECT COUNT(*) FROM pre_bl_ocr;
   ```

### Performance Optimization

1. **Connection pooling** is automatically configured
2. **Indexes** are created on frequently queried columns
3. **Batch operations** are used for large data migrations

## 📊 Monitoring and Maintenance

### Regular Backups

The dual database system provides automatic redundancy, but consider:
- Regular PostgreSQL backups via your cloud provider
- Periodic SQLite file backups
- Database health monitoring

### Performance Monitoring

Monitor database performance through:
- Application logs
- Database provider dashboards
- Custom health check endpoints

## 🔄 Migration Path

1. **Phase 1**: Install dependencies and configure environment
2. **Phase 2**: Initialize PostgreSQL database
3. **Phase 3**: Migrate existing data
4. **Phase 4**: Enable dual database mode
5. **Phase 5**: Monitor and optimize

## 📞 Support

For issues with the dual database setup:
1. Check the application logs
2. Verify environment configuration
3. Test database connections individually
4. Review the troubleshooting section above
