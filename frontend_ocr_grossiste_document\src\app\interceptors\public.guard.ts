// public.guard.ts
import { Injectable } from '@angular/core';
import { CanActivate, Router } from '@angular/router';
import { jwtDecode } from 'jwt-decode';
import * as moment from 'moment';

@Injectable({
  providedIn: 'root'
})
export class PublicGuard implements CanActivate {

  constructor(private router: Router) {}

  canActivate(): boolean {
    const tokenUser = localStorage.getItem('tokenUser');
    const tokenTenant = localStorage.getItem('tokenTenant');
    const tokenLocal = localStorage.getItem('token');

    if (tokenUser && tokenTenant && tokenLocal) {
      try {
        // Check if tokens are valid
        const isTokenUserValid = this.checkTokenExpiration(tokenUser);
        const isTokenTenantValid = this.checkTokenExpiration(tokenTenant);
        const isTokenLocalValid = this.checkTokenExpiration(tokenLocal);

        if (isTokenUserValid && isTokenTenantValid && isTokenLocalValid) {
          // If user is already authenticated, redirect to scan-bl
          this.router.navigate(['/scan-bl']);
          return false;
        }
      } catch (error) {
        console.error('Token validation error:', error);
      }
    }

    // Allow access to public route if not authenticated
    return true;
  }

  private checkTokenExpiration(token: string): boolean {
    const decodedToken: any = jwtDecode(token);
    const expiration = moment(decodedToken?.exp * 1000);
    return moment(new Date()) < expiration;
  }
}
