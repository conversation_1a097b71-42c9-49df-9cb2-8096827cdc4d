import { Component, OnInit, inject } from '@angular/core';
import { NetworkService } from '../services/network.service';
import { Platform } from '@ionic/angular';
import { Network } from '@capacitor/network';

@Component({
  selector: 'app-check-network',
  templateUrl: './check-network.component.html',
  styleUrls: ['./check-network.component.scss'],
})
export class CheckNetworkComponent  implements OnInit {

  isConnected = false; // Track network status


  constructor(private platform: Platform, private networkService: NetworkService) { }

  ngOnInit() {

    


    this.platform.ready().then(() => {
      this.networkService.getNetworkStatus().subscribe((connected: boolean) => {
        this.isConnected = connected;
        console.log('Network status:', this.isConnected);
      });

      this.networkService.isOnline$.subscribe(result => {
        this.isConnected = result;
        console.log(result ? 'User is online' : 'User is offline');
      });
    });
  }

}
