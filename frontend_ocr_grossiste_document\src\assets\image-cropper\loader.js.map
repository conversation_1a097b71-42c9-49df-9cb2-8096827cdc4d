{"file": "loader.js", "mappings": ";;;AAGY,MAAC,oBAAoB,GAAG,CAAC,GAAG,EAAE,OAAO,KAAK;AACtD,EAAE,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE,OAAO,SAAS,CAAC;AAEtD,EAAE,OAAO,aAAa,CAAC,4BAA4B,EAAE,OAAO,CAAC,CAAC;AAC9D;;;;", "names": [], "sources": ["@lazy-external-entrypoint?app-data=conditional"], "sourcesContent": ["export { setNonce } from '@stencil/core';\nimport { bootstrapLazy } from '@stencil/core';\nimport { globalScripts } from '@stencil/core/internal/app-globals';\nexport const defineCustomElements = (win, options) => {\n  if (typeof window === 'undefined') return undefined;\n  globalScripts();\n  return bootstrapLazy([/*!__STENCIL_LAZY_DATA__*/], options);\n};\n"], "version": 3}