#!/usr/bin/env python3
"""
Database Migration Script: Add src_app Column
This script adds the src_app column to existing PostgreSQL database.

Usage:
    python scripts/add_src_app_column.py [--env-file .env.local] [--dry-run]

Options:
    --env-file   Environment file to load (default: .env.local)
    --dry-run    Show what would be executed without actually doing it
"""

import sys
import os
import argparse
import logging
from pathlib import Path
from dotenv import load_dotenv

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.app.database.connection import db_manager
from sqlalchemy import text

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def check_column_exists():
    """Check if src_app column already exists."""
    try:
        with db_manager.engine.connect() as conn:
            result = conn.execute(text("""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name = 'pre_bl_ocr' 
                AND column_name = 'src_app'
            """))
            return result.fetchone() is not None
    except Exception as e:
        logger.error(f"Error checking column existence: {e}")
        return False


def add_src_app_column(dry_run=False):
    """Add src_app column to pre_bl_ocr table."""
    try:
        # Check if column already exists
        if check_column_exists():
            logger.info("✅ src_app column already exists")
            return True
        
        # SQL to add the column
        sql_command = """
        ALTER TABLE pre_bl_ocr 
        ADD COLUMN src_app VARCHAR(50) NOT NULL DEFAULT 'winpluspharma';
        """
        
        if dry_run:
            logger.info("🔍 DRY RUN - Would execute:")
            logger.info(sql_command)
            return True
        
        # Execute the migration
        logger.info("🔄 Adding src_app column to pre_bl_ocr table...")
        with db_manager.engine.connect() as conn:
            conn.execute(text(sql_command))
            conn.commit()
        
        logger.info("✅ Successfully added src_app column")
        
        # Verify the column was added
        if check_column_exists():
            logger.info("✅ Column verification successful")
            return True
        else:
            logger.error("❌ Column verification failed")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error adding src_app column: {e}")
        return False


def update_existing_records(dry_run=False):
    """Update existing records to have default src_app value."""
    try:
        # In dry-run mode, if column doesn't exist yet, we can't check for records
        if dry_run:
            # Check if column exists first
            if not check_column_exists():
                logger.info("🔍 DRY RUN - Column doesn't exist yet, would update existing records after column creation")
                sql_command = """
        UPDATE pre_bl_ocr
        SET src_app = 'winpluspharma'
        WHERE src_app IS NULL OR src_app = '';
        """
                logger.info("🔍 DRY RUN - Would execute after column creation:")
                logger.info(sql_command)
                return True

        # Count records that need updating
        with db_manager.engine.connect() as conn:
            result = conn.execute(text("""
                SELECT COUNT(*)
                FROM pre_bl_ocr
                WHERE src_app IS NULL OR src_app = ''
            """))
            count = result.fetchone()[0]

        if count == 0:
            logger.info("✅ All records already have src_app values")
            return True

        sql_command = """
        UPDATE pre_bl_ocr
        SET src_app = 'winpluspharma'
        WHERE src_app IS NULL OR src_app = '';
        """

        if dry_run:
            logger.info(f"🔍 DRY RUN - Would update {count} records:")
            logger.info(sql_command)
            return True

        logger.info(f"🔄 Updating {count} existing records with default src_app value...")
        with db_manager.engine.connect() as conn:
            result = conn.execute(text(sql_command))
            conn.commit()
            updated_count = result.rowcount

        logger.info(f"✅ Successfully updated {updated_count} records")
        return True

    except Exception as e:
        logger.error(f"❌ Error updating existing records: {e}")
        return False


def verify_migration():
    """Verify the migration was successful."""
    try:
        with db_manager.engine.connect() as conn:
            # Check column exists
            result = conn.execute(text("""
                SELECT column_name, data_type, column_default
                FROM information_schema.columns 
                WHERE table_name = 'pre_bl_ocr' 
                AND column_name = 'src_app'
            """))
            column_info = result.fetchone()
            
            if not column_info:
                logger.error("❌ src_app column not found")
                return False
            
            logger.info(f"✅ Column info: {column_info}")
            
            # Check record counts
            result = conn.execute(text("""
                SELECT 
                    COUNT(*) as total,
                    COUNT(CASE WHEN src_app = 'winpluspharma' THEN 1 END) as winplus_count,
                    COUNT(CASE WHEN src_app = 'pharmalien' THEN 1 END) as pharmalien_count
                FROM pre_bl_ocr
            """))
            counts = result.fetchone()
            
            logger.info(f"✅ Record counts - Total: {counts[0]}, WinPlus: {counts[1]}, Pharmalien: {counts[2]}")
            return True
            
    except Exception as e:
        logger.error(f"❌ Error verifying migration: {e}")
        return False


def main():
    """Main migration function."""
    parser = argparse.ArgumentParser(description='Add src_app column to PostgreSQL database')
    parser.add_argument('--env-file', default='.env.local', help='Environment file to load (default: .env.local)')
    parser.add_argument('--dry-run', action='store_true', help='Show what would be done without executing')
    args = parser.parse_args()

    # Load environment file
    env_file_path = project_root / args.env_file
    if env_file_path.exists():
        load_dotenv(env_file_path)
        logger.info(f"✅ Loaded environment from {args.env_file}")
    else:
        logger.error(f"❌ Environment file not found: {args.env_file}")
        sys.exit(1)

    logger.info("🚀 Starting src_app column migration...")

    if args.dry_run:
        logger.info("🔍 DRY RUN MODE - No changes will be made")
    
    # Check database connection
    try:
        health = db_manager.health_check()
        if not health['available']:
            logger.error(f"❌ PostgreSQL not available: {health['error']}")
            sys.exit(1)
        logger.info("✅ PostgreSQL connection successful")
    except Exception as e:
        logger.error(f"❌ Database connection failed: {e}")
        sys.exit(1)
    
    # Add the column
    if not add_src_app_column(args.dry_run):
        sys.exit(1)
    
    # Update existing records
    if not update_existing_records(args.dry_run):
        sys.exit(1)
    
    # Verify migration (only if not dry run)
    if not args.dry_run:
        if not verify_migration():
            sys.exit(1)
    
    logger.info("🎉 Migration completed successfully!")
    if args.dry_run:
        logger.info("💡 Run without --dry-run to execute the migration")


if __name__ == "__main__":
    main()
