import { Injectable } from '@angular/core';
import { Observable, Subject } from 'rxjs';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root'
})
export class WebSocketService {
  private socket?: WebSocket;
  private subjects: { [jobId: string]: Subject<any> } = {};

  constructor() {
  }

  public connect(url: string, jobId: string): void {
    this.socket = new WebSocket(url);

    this.socket.onopen = (event) => {
      console.log('WebSocket connection established:', event);
    };

    this.socket.onmessage = (event) => {
      const data = JSON.parse(event.data);
      if (data.job_id && this.subjects[data.job_id]) {
        this.subjects[data.job_id].next(data);
      } else if (this.subjects[jobId]) {
        this.subjects[jobId].next(data);
      }
    };

    this.socket.onerror = (event) => {
      console.error('WebSocket error observed:', event);
      this.reconnect(url, jobId);
    };

    this.socket.onclose = (event) => {
      console.log('WebSocket connection closed:', event);
      this.reconnect(url, jobId);
    };
  }

  private reconnect(url: string, jobId: string): void {
    setTimeout(() => {
      this.connect(url, jobId);
    }, 1000); // Retry connection after 1 second
  }

  public send(data: any): void {
    if (this.socket?.readyState === WebSocket.OPEN) {
      this.socket?.send(JSON.stringify(data));
    } else {
      console.error('WebSocket connection is not open.');
    }
  }

  public onMessage(jobId: string): Observable<any> {
    if (!this.subjects[jobId]) {
      this.subjects[jobId] = new Subject<any>();
    }
    return this.subjects[jobId].asObservable();
  }

  public close(jobId: string): void {
    if (this.socket) {
      this.socket.close();
      delete this.subjects[jobId];
    }
  }

}
