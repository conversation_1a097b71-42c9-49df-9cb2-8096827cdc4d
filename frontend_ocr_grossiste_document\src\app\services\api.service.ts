import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpErrorResponse } from '@angular/common/http';
import { Observable, throwError  } from 'rxjs';
import { environment } from '../../environments/environment';
import { Coordinates } from '../../models/coordinates';
import { ProcessDocData } from 'src/models/ProcessDocData';
import { ProcessImageSuppRequest } from 'src/models/ProcessImageSuppRequest';
import { ImageData } from 'src/models/ImageData';
import { catchError, tap, switchMap } from 'rxjs/operators';
import { LoginRequest, LoginResponse, TenantLoginRequest, TenantLoginResponse, PharmalienLoginRequest, PharmalienLoginResponse } from '../../models/login';
import { AlertController } from '@ionic/angular';
import Swal from 'sweetalert2';
import { jwtDecode } from 'jwt-decode';

enum OcrMode {
  STANDARD = 'STANDARD',
  MINDEE_ADVANCED = 'MINDEE_ADVANCED'
}

@Injectable({
  providedIn: 'root'
})
export class ApiService {
  private baseUrl = environment.apiUrl;  // Ensure your environment file has the correct base URL


  constructor(private http: HttpClient, private alertController: AlertController,) { }

  // Set common HTTP options
  private httpOptions = {
    headers: new HttpHeaders({
      'Content-Type': 'application/json'
    })
  };

  // Generate a unique job ID
  generateJobId(): string {
    return 'job_' + Math.random().toString(36).substr(2, 9);
  }

  private logRequest(url: string, method: string, body?: any) {
    console.log(`Request: ${method} ${url}`, body);
  }

  private logResponse(response: any) {
    console.log('Response:', response);
  }

  private handleError(error: HttpErrorResponse) {
    console.error('API Error:', error);
    return throwError(error);
  }

  // Fetch root endpoint
  getRoot(): Observable<any> {
    return this.http.get(`${this.baseUrl}/`);
  }

   // Tenant Login
  tenantLogin(request: TenantLoginRequest): Observable<TenantLoginResponse> {
    const url = `${this.baseUrl}/tenant_login`;
    this.logRequest(url, 'POST', request);
    return this.http.post<TenantLoginResponse>(url, request, this.httpOptions).pipe(
      tap(this.logResponse),
      catchError(this.handleError)
    );
  }

  // User Login
  userLogin(request: LoginRequest, tenantToken: string): Observable<LoginResponse> {
    const url = `${this.baseUrl}/login`;
    const headers = new HttpHeaders({
      'Content-Type': 'application/json',
      'AuthorizationTenant': `BearerTenant ${tenantToken}`
    });
    const bodyWithToken = { ...request, tenant_token: tenantToken };
    this.logRequest(url, 'POST', bodyWithToken);
    return this.http.post<LoginResponse>(url, bodyWithToken, { headers }).pipe(
      tap(this.logResponse),
      catchError(this.handleError)
    );
  }

  // Pharmalien Login
  pharmalienLogin(request: PharmalienLoginRequest): Observable<PharmalienLoginResponse> {
    const url = `${this.baseUrl}/pharmalien_login`;
    this.logRequest(url, 'POST', request);
    return this.http.post<PharmalienLoginResponse>(url, request, this.httpOptions).pipe(
      tap(this.logResponse),
      catchError(this.handleError)
    );
  }

  // Logout function
  async logout(): Promise<void> {
    return new Promise(async (resolve) => {
      const alert = await this.alertController.create({
        header: 'Déconnexion',
        message: `Confirmer la déconnexion ?`,
        buttons: [
          {
            text: 'Annuler',
            role: 'cancel',
            cssClass: 'custom-alert-button cancel',
            handler: () => {
              console.log('Confirm Cancel');
              resolve(); // Resolve even if canceled
            },
          },
          {
            text: 'Oui',
            cssClass: 'custom-alert-button danger',
            handler: () => {
              localStorage.removeItem('tokenUser');
              localStorage.removeItem('tokenTenant');
              localStorage.removeItem('token');
              localStorage.removeItem('ocrMode');
              localStorage.removeItem('forceSupplierGlobal');
              localStorage.removeItem('selectedSupplier');
              resolve(); // Resolve after logout
            },
          },
        ],
      });
  
      await alert.present();
    });
  }

  // Smart Crop API
  smartCrop(image: File, jobId: string, isScanner: boolean): Observable<any> {
    console.log('API Base URL:', this.baseUrl);

    const formData: FormData = new FormData();
    formData.append('image', image);
    formData.append('job_id', jobId);  // Include the job ID
    formData.append('isScanner', isScanner.toString());  // Include the isScanner flag
    const url = `${this.baseUrl}/smart_crop/`;
    this.logRequest(url, 'POST', formData);
    return this.http.post<any>(url, formData).pipe(
      tap(this.logResponse),
      catchError(this.handleError)
    );
  }

  // Magic Pro Filter API
  magicProFilter(image: File, modelName: string, randomId?: string, jobId?: string): Observable<any> {
    const formData: FormData = new FormData();
    formData.append('image', image);
    formData.append('model_name', modelName);
    formData.append('job_id', jobId ?? "");  // Include the job ID
    if (randomId) {
      formData.append('random_id', randomId);
    }

    return this.http.post<any>(`${this.baseUrl}/magic_pro_filter/`, formData);
  }

  // Identify Supplier API
  identifySupplier(image: File, modelName: string, randomId?: string, jobId?: string): Observable<any> {
    const formData: FormData = new FormData();
    formData.append('image', image);
    formData.append('model_name', modelName);
    formData.append('job_id', jobId ?? "");  // Include the job ID
    if (randomId) {
      formData.append('random_id', randomId);
    }

    return this.http.post<any>(`${this.baseUrl}/identify_supplier/`, formData);
  }

  // Process Image Supp API
  processImageSupp(request: ProcessImageSuppRequest, jobId: string): Observable<any> {
    // const jobId = this.generateJobId();  // Get the job ID
    const modifiedRequest = { ...request, job_id: jobId };  // Include the job ID in the request
    return this.http.post<any>(`${this.baseUrl}/process_image_supp/`, modifiedRequest, this.httpOptions);
  }

  // Process OCR Multi API
  processOcrMulti(images: ImageData[], jobId: string, random_id: string, ocrMode: OcrMode = OcrMode.STANDARD): Observable<any> {
    const modifiedImages = images.map(image => ({ ...image, job_id: jobId }));

    // Get src_app from localStorage, default to 'winpluspharma'
    const srcApp = localStorage.getItem('src_app') || 'winpluspharma';

    // Create the request body with images, ocrMode, and src_app
    const requestBody = {
      images: modifiedImages,
      ocr_mode: ocrMode.toLowerCase(), // Convert to lowercase to match backend expectation
      src_app: srcApp // Include platform source
    };

    return this.http.post(`${this.baseUrl}/process_ocr_multi/`, requestBody, this.httpOptions);
  }

  // Update BL Status API to EN_COURS
  updateBLStatus(blId: string, status: string, id_BL_origine: string, date_BL_origine: string, supplier_name: string): Observable<any> {
    return this.http.put(`${this.baseUrl}/winplus/updateStatus/${blId}`, { status, id_BL_origine, date_BL_origine, supplier_name });
  }

  // Get All Suppliers list
  getAllSuppliers(): Observable<any> {
    return this.http.get(`${this.baseUrl}/suppliers`);
  }

  showErrorAlert(message: string) {
    const messageDisplayed = message !== "" ? message : "Il y a eu une erreur de compréhension de la requête. Veuillez réessayer plus tard.";
  
    Swal.fire({
      icon: 'error',
      title: 'Format de l\'image incorrecte !',
      html: messageDisplayed,
      footer: '<a href="/guide">Comment capturer une image de qualité ?</a>',
      showConfirmButton: false,  // Remove the confirm button
      showCloseButton: true,     // Add a close button
      customClass: {
        closeButton: 'custom-close-button',  // Custom class for the close button
        popup: 'custom-popup',               // Custom class for the popup for additional styling if needed
        footer: 'custom-footer'              // Custom class for the footer
      }
    });

    // localStorage.removeItem('ocrMode');
    // localStorage.removeItem('forceSupplierGlobal');
    localStorage.removeItem('selectedSupplier');


  }

  isLoggedIn(): boolean {
    const token = localStorage.getItem('token');
    const tokenUser = localStorage.getItem('tokenUser');
    const tokenTenant = localStorage.getItem('tokenTenant');
    const platform = localStorage.getItem('src_app');

    // Check required tokens based on platform
    if (platform === 'pharmalien') {
      // Pharmalien only needs token and tokenUser
      if (!token || !tokenUser) {
        return false;
      }
    } else {
      // WinPlus needs all three tokens
      if (!token || !tokenUser || !tokenTenant) {
        return false;
      }
    }

    try {
      const decodedToken = this.decodeToken(token);
      if (!this.isTokenValid(decodedToken)) {
        return false;
      }
    } catch (e) {
      console.error('Token decoding failed', e);
      return false;
    }

    return true;
  }

  private decodeToken(token: string): any {
    return jwtDecode(token);
  }

  private isTokenValid(decodedToken: any): boolean {
    // Check if token has expired
    const currentTime = Math.floor(Date.now() / 1000);
    if (decodedToken.exp < currentTime) {
      return false;
    }

    return true;
  }



  // ## -- Medicament API -- ##

  /**
   * Get medicament information and suggestions from OCR
   * @param image Image file to analyze
   * @param jobId Job ID for tracking progress
   * @returns Promise with medicament suggestions
   */
  getMedicamentInfo(image: File, jobId: string): Observable<any> {
    console.log('API Base URL:', this.baseUrl);

    const formData: FormData = new FormData();
    formData.append('image', image);
    formData.append('job_id', jobId);  // Include the job ID
    
    const url = `${this.baseUrl}/medicament_ocr_tap/`;
    console.log('Making request to:', url);
    
    return this.http.post<any>(url, formData);
  }
}
