@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap');

* {
  font-family: 'Inter', sans-serif;
  font-optical-sizing: auto;
}

.footer-md, .header-md {
  -webkit-box-shadow: none;
  box-shadow: none;
}
.fix-wrapper{
  --ion-background-color: transparent;
  display: flex;
  flex-direction: column;
  background: url('/assets/onboarding_images/bg_onboarding.png') no-repeat;
  background-size: cover;
  height: 100dvh;
}
// ion-content::part(scroll) {
//   overflow-y: hidden !important;
//   --overflow: hidden !important;
// }
 
.onboarding-wrapper {
  // background: url('/assets/onboarding_images/bg_onboarding.png') no-repeat center center fixed;
  // background-size: cover;
  height: 100%;
}
ion-footer

.slide-content {
  text-align: center;
  padding: 20px;
}

.slide-image {
  width: 350px;
  height: 350px;
  margin: 0 auto 20px;
}

ion-footer {
  position: relative;
  // bottom: -30px;
  width: 100%;
  background: transparent;
  --background: transparent;
  --ion-background-color: transparent;
}

ion-toolbar {
  --background: transparent;
  --ion-color-primary: #2f4fcd;
}

ion-button {
  margin: 10px;
}

ion-button.next-button {
  --background: #2f4fcd;
  --background-activated: #1e3aa8;
  --border-radius: 8px;
  --color: #fff;
  width: 80%;
}

ion-button.skip-button {
  --color: #2f4fcd;
  width: 20%;
}

.pagination {
  display: flex;
  justify-content: center;
  margin: 20px 0;
  gap: 5px;
}

.pagination span {
  display: block;
  width: 10px;
  height: 10px;
  background-color: #ccc;
  border-radius: 50%;
}

.pagination .active {
  background-color: #2f4fcd;
}

.buttons_nav {
  display: flex;
  justify-content: space-between;
  gap: 5px;
  background: transparent;
  // padding: 0 10px;
}

/* Swiper Container */
swiper-container {
  height: 100%;
  --swiper-pagination-bullet-inactive-color: var(--ion-color-step-200, #cccccc);
  --swiper-pagination-color: var(--ion-color-primary, #2f4fcd);
  --swiper-pagination-progressbar-bg-color: rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.25);
  --swiper-scrollbar-bg-color: rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.1);
  --swiper-scrollbar-drag-bg-color: rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.5);
}

swiper-slide {
  display: flex;
  position: relative;
  flex-direction: column;
  flex-shrink: 0;
  align-items: center;
  justify-content: center;
  width: 100%;
  font-size: 18px;
  text-align: center;
  box-sizing: border-box;
  height: calc(100vh - 90px);
  overflow-y: auto;
}

swiper-slide ion-row {
  height: 100%;
}

::ng-deep swiper-slide ion-row ion-col {
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}

::ng-deep swiper-slide ion-col {
  display: flex !important;
  flex-direction: column;
  justify-content: space-evenly;
  align-items: center;
}

::ng-deep swiper-slide img {
  width: auto;
  max-width: 100%;
  height: auto;
  max-height: 100%;
  margin-bottom: 10%;
}

::ng-deep swiper-slide .content-slide {
  color: #000;
  text-align: left;
  padding: 0 15px;
  margin-bottom: 100px;
}

::ng-deep swiper-slide .content-slide h2 {
  font-weight: bold;
}

::ng-deep swiper-slide .content-slide p {
  // text-align: justify;
  letter-spacing: 0.9px;
  font-size: 16px;
  padding-right: 20px;
}

.next-button {
  display: flex;
  justify-content: space-between;
  flex-direction: row;
  align-items: center;
}

.skip-button {
  font-size: 16px !important;
  font-weight: bold;
  opacity: 0.7;
}

swiper-container::part(bullet-active) {
  width: 70px !important;
  height: 5px !important;
  border-radius: 8px !important;
}
ion-footer::before {
  /* position: relative; */
  /* width: 100%; */
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  // background: url('/assets/onboarding_images/bg_onboarding.png') no-repeat center center fixed;
  background-size: cover;
  transform: rotate(180deg);
  z-index: -1;
}