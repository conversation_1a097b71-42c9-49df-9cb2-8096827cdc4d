# Date Column Migration Deployment Guide

This guide explains how to deploy the date format migration to production and preproduction environments.

## What This Migration Does

Changes the `date` column in the `pre_bl_ocr` table from:
- **Before**: `TIMESTAMP WITH TIME ZONE` → stores dates like `2025-07-02 17:30:56+00`
- **After**: `TIMESTAMP WITHOUT TIME ZONE` → stores dates like `2025-07-02 17:30:56`

## Prerequisites

1. Ensure you have access to the target environment databases
2. Have the virtual environment activated: `env_ocr\Scripts\activate`
3. Backup the database before running on production (recommended)

## Deployment Steps

### 1. Test on Preprod First (Recommended)

```bash
# Activate virtual environment
env_ocr\Scripts\activate

# Run migration on preprod
py.exe scripts/migrate_date_column.py --env preprod
```

**Expected Output:**
```
2025-07-02 17:33:14,002 - INFO - Running migration on preprod environment
2025-07-02 17:33:14,002 - INFO - Starting migration of date column for preprod environment...
2025-07-02 17:33:14,006 - INFO - Current column info: ('date', 'timestamp with time zone', 'NO')
2025-07-02 17:33:14,006 - INFO - Executing column type change...
2025-07-02 17:33:14,012 - INFO - Updating default value...
2025-07-02 17:33:14,014 - INFO - Updated column info: ('date', 'timestamp without time zone', 'now()')
2025-07-02 17:33:14,015 - INFO - Sample data after migration:
2025-07-02 17:33:14,015 - INFO -   ID_BL: 45, date: 2025-07-02 17:30:56, status: EN_COURS
2025-07-02 17:33:14,029 - INFO - ✅ Migration completed successfully!
```

### 2. Verify Preprod Migration

Test creating new records in preprod to ensure dates are stored without timezone:

```bash
# Test the API endpoint or create a test record
# Verify the date format is: yyyy-MM-dd HH:mm:ss (no +00)
```

### 3. Deploy to Production

**⚠️ IMPORTANT: Backup production database first!**

```bash
# Activate virtual environment
env_ocr\Scripts\activate

# Run migration on production
py.exe scripts/migrate_date_column.py --env prod
```

### 4. Verify Production Migration

1. Check that the migration completed successfully
2. Test creating new records
3. Verify API responses show clean date format

## Rollback Instructions

If something goes wrong, you can rollback the migration:

### Rollback Preprod:
```bash
py.exe scripts/migrate_date_column.py --env preprod --rollback
```

### Rollback Production:
```bash
py.exe scripts/migrate_date_column.py --env prod --rollback
```

## Environment Configuration

The migration script automatically uses the correct environment file:

- **Local**: `.env.local` (default)
- **Preprod**: `.env.preprod` 
- **Production**: `.env.prod`

## What Gets Changed

### Database Schema:
```sql
-- Before
date TIMESTAMP WITH TIME ZONE DEFAULT NOW()

-- After  
date TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW()
```

### Application Code (Already Updated):
- `src/app/database/models.py`: Column definition and date handling
- `src/app/database/operations.py`: Date creation in operations

## Verification Checklist

After running the migration on each environment:

- [ ] Migration script completed successfully (exit code 0)
- [ ] Column type changed to `timestamp without time zone`
- [ ] Sample data shows dates without `+00` timezone
- [ ] New records created through API have clean date format
- [ ] Existing functionality still works correctly

## Troubleshooting

### Common Issues:

1. **Connection Error**: Verify environment file exists and database credentials are correct
2. **Permission Error**: Ensure database user has ALTER TABLE permissions
3. **Column Not Found**: Check that table `pre_bl_ocr` exists in target database

### Getting Help:

If migration fails, check the error message and:
1. Verify database connectivity
2. Check database user permissions
3. Ensure no other processes are using the table during migration

## Timeline Recommendation

1. **Day 1**: Run on preprod, test thoroughly
2. **Day 2-3**: Monitor preprod for any issues
3. **Day 4**: Deploy to production during low-traffic period
4. **Day 5**: Monitor production and verify everything works correctly

## Contact

If you encounter any issues during deployment, contact the development team with:
- Environment name (preprod/prod)
- Full error message from migration script
- Database logs if available
