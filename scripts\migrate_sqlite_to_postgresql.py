#!/usr/bin/env python3
"""
SQLite to PostgreSQL Migration Script

This script migrates existing data from SQLite to PostgreSQL database.
It preserves all existing data while setting up the new dual database system.

Usage:
    python scripts/migrate_sqlite_to_postgresql.py [--dry-run] [--batch-size=1000]

Options:
    --dry-run       Show what would be migrated without actually doing it
    --batch-size    Number of records to process in each batch (default: 1000)
"""

import sys
import os
import argparse
import logging
import json
import sqlite3
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Any

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.app.config import SQLITE_DB_PATH
from src.app.database.connection import db_manager, get_postgres_session
from src.app.database.models import PreBlOcr

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def check_sqlite_database():
    """Check if SQLite database exists and has data."""
    try:
        if not SQLITE_DB_PATH.exists():
            logger.error(f"❌ SQLite database not found at: {SQLITE_DB_PATH}")
            return False
        
        conn = sqlite3.connect(SQLITE_DB_PATH)
        conn.row_factory = sqlite3.Row
        c = conn.cursor()
        
        # Check if pre_bl_ocr table exists
        c.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='pre_bl_ocr'")
        if not c.fetchone():
            logger.error("❌ pre_bl_ocr table not found in SQLite database")
            conn.close()
            return False
        
        # Count records
        c.execute("SELECT COUNT(*) FROM pre_bl_ocr")
        count = c.fetchone()[0]
        logger.info(f"✅ Found {count} records in SQLite database")
        
        conn.close()
        return True
        
    except Exception as e:
        logger.error(f"❌ Error checking SQLite database: {e}")
        return False


def check_postgresql_database():
    """Check if PostgreSQL database is available and initialized."""
    try:
        health = db_manager.health_check()
        
        if not health['postgresql']['available']:
            logger.error(f"❌ PostgreSQL not available: {health['postgresql']['error']}")
            return False
        
        # Check if tables exist
        with db_manager.postgres_engine.connect() as conn:
            result = conn.execute("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public' AND table_name = 'pre_bl_ocr'
            """)
            if not result.fetchone():
                logger.error("❌ pre_bl_ocr table not found in PostgreSQL. Run init_postgresql.py first.")
                return False
        
        logger.info("✅ PostgreSQL database is ready")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error checking PostgreSQL database: {e}")
        return False


def get_sqlite_records(batch_size: int = 1000, offset: int = 0) -> List[Dict[str, Any]]:
    """Get records from SQLite database in batches."""
    try:
        conn = sqlite3.connect(SQLITE_DB_PATH)
        conn.row_factory = sqlite3.Row
        c = conn.cursor()
        
        c.execute("""
            SELECT * FROM pre_bl_ocr 
            ORDER BY ID_BL 
            LIMIT ? OFFSET ?
        """, (batch_size, offset))
        
        rows = c.fetchall()
        conn.close()
        
        records = []
        for row in rows:
            record = {
                'ID_BL': row['ID_BL'],
                'Content': json.loads(row['Content']) if row['Content'] else None,
                'ID_USER': row['ID_USER'],
                'status': row['status'] or 'EN_ATTENTE',
                'ID_TENANT': row['ID_TENANT'],
                'CODE_TENANT': row['CODE_TENANT'],
                'date': row['date'],
                'id_BL_origine': row['id_BL_origine'],
                'date_BL_origine': row['date_BL_origine'],
                'supplier_name': row['supplier_name'],
                'supplier_id': row['supplier_id'],
                'random_id': row['random_id']
            }
            records.append(record)
        
        return records
        
    except Exception as e:
        logger.error(f"❌ Error reading SQLite records: {e}")
        return []


def migrate_batch_to_postgresql(records: List[Dict[str, Any]], dry_run: bool = False) -> int:
    """Migrate a batch of records to PostgreSQL."""
    if dry_run:
        logger.info(f"[DRY RUN] Would migrate {len(records)} records")
        return len(records)
    
    try:
        migrated_count = 0
        
        with get_postgres_session() as session:
            for record in records:
                # Check if record already exists
                existing = session.query(PreBlOcr).filter(
                    PreBlOcr.ID_BL == record['ID_BL']
                ).first()
                
                if existing:
                    logger.debug(f"Record ID_BL={record['ID_BL']} already exists, skipping")
                    continue
                
                # Create new record
                pg_record = PreBlOcr.from_dict(record)
                # Preserve the original ID
                pg_record.ID_BL = record['ID_BL']
                
                session.add(pg_record)
                migrated_count += 1
            
            session.commit()
        
        return migrated_count
        
    except Exception as e:
        logger.error(f"❌ Error migrating batch to PostgreSQL: {e}")
        return 0


def verify_migration():
    """Verify that migration was successful."""
    try:
        # Count records in SQLite
        conn = sqlite3.connect(SQLITE_DB_PATH)
        c = conn.cursor()
        c.execute("SELECT COUNT(*) FROM pre_bl_ocr")
        sqlite_count = c.fetchone()[0]
        conn.close()
        
        # Count records in PostgreSQL
        with get_postgres_session() as session:
            pg_count = session.query(PreBlOcr).count()
        
        logger.info(f"SQLite records: {sqlite_count}")
        logger.info(f"PostgreSQL records: {pg_count}")
        
        if sqlite_count == pg_count:
            logger.info("✅ Migration verification successful - record counts match")
            return True
        else:
            logger.warning(f"⚠️  Record count mismatch: SQLite={sqlite_count}, PostgreSQL={pg_count}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error verifying migration: {e}")
        return False


def main():
    """Main migration function."""
    parser = argparse.ArgumentParser(description='Migrate SQLite data to PostgreSQL')
    parser.add_argument('--dry-run', action='store_true', 
                       help='Show what would be migrated without actually doing it')
    parser.add_argument('--batch-size', type=int, default=1000,
                       help='Number of records to process in each batch')
    
    args = parser.parse_args()
    
    logger.info("🚀 Starting SQLite to PostgreSQL migration...")
    
    if args.dry_run:
        logger.info("🔍 Running in DRY RUN mode - no data will be modified")
    
    # Check databases
    if not check_sqlite_database():
        sys.exit(1)
    
    if not check_postgresql_database():
        sys.exit(1)
    
    # Get total record count
    conn = sqlite3.connect(SQLITE_DB_PATH)
    c = conn.cursor()
    c.execute("SELECT COUNT(*) FROM pre_bl_ocr")
    total_records = c.fetchone()[0]
    conn.close()
    
    logger.info(f"📊 Total records to migrate: {total_records}")
    
    # Migrate in batches
    offset = 0
    total_migrated = 0
    
    while offset < total_records:
        logger.info(f"📦 Processing batch: {offset + 1} to {min(offset + args.batch_size, total_records)}")
        
        records = get_sqlite_records(args.batch_size, offset)
        if not records:
            break
        
        migrated = migrate_batch_to_postgresql(records, args.dry_run)
        total_migrated += migrated
        
        logger.info(f"✅ Migrated {migrated} records in this batch")
        
        offset += args.batch_size
    
    logger.info(f"📈 Total records migrated: {total_migrated}")
    
    if not args.dry_run:
        # Verify migration
        if verify_migration():
            logger.info("🎉 Migration completed successfully!")
        else:
            logger.warning("⚠️  Migration completed with warnings - please verify manually")
    else:
        logger.info("🔍 Dry run completed - use without --dry-run to perform actual migration")


if __name__ == "__main__":
    main()
