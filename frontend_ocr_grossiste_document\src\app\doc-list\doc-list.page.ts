import {
  Component,
  ViewChild,
  <PERSON><PERSON><PERSON>w<PERSON>nit,
  ElementRef,
  OnInit,
  inject,
} from '@angular/core';
import { NavController, LoadingController, AlertController } from '@ionic/angular';
import Swiper from 'swiper';
import { SignalService } from '../services/signal.service';
import { ProcessDocData } from 'src/models/ProcessDocData';
import { ApiService } from '../services/api.service';  // Import ApiService
import { ImageData } from 'src/models/ImageData';
import { TransformedDocData } from 'src/models/TransformedDocData';
import { NetworkService } from '../services/network.service';
import { WebSocketService } from '../services/websocket.service';
import { environment } from 'src/environments/environment';

enum OcrMode {
  STANDARD = 'STANDARD',
  MINDEE_ADVANCED = 'MINDEE_ADVANCED'
}

@Component({
  selector: 'app-doc-list',
  templateUrl: './doc-list.page.html',
  styleUrls: ['./doc-list.page.scss'],
})
export class DocListPage implements AfterViewInit, OnInit {
  @ViewChild('swiperContainer', { static: true }) swiperContainer:
    | ElementRef
    | undefined;
  swiper: Swiper | undefined;

  slidesData: ProcessDocData[] = [];
  navCtrl = inject(NavController);
  signalService = inject(SignalService);
  apiService = inject(ApiService);
  loadingController = inject(LoadingController);

  progress = 0;
  isLoading = false;
  isConnected = true; // Track network status
  jobId: string | undefined; // Add this line to store the job ID

  constructor(
    private alertController: AlertController,
    private webSocketService: WebSocketService,
    private networkService: NetworkService,
  ) {
    // this.jobId = this.apiService.generateJobId(); // Generate job ID once
  }

  async ngAfterViewInit() {
    this.initializeSwiper();
  }

  async ngOnInit() {
    if (this.signalService.getData() == null || this.signalService.getData() == undefined || this.signalService.getData().length == 0) {
      this.navCtrl.navigateRoot('/scan-bl');
    } else {
      this.slidesData = this.signalService.getData();
      this.swiperContainer?.nativeElement.swiper?.update();

      console.log('Slides data:', this.slidesData);
    }

    // Subscribe to the network status
    this.networkService.getNetworkStatus().subscribe((connected: boolean) => {
      this.isConnected = connected;
    });

    // // const jobId = this.apiService.generateJobId();  // Get the job ID
    // const websocketUrl = `${environment.webSocketUrl}/${this.jobId}`;
    // this.webSocketService.connect(websocketUrl, this.jobId);
    // this.webSocketService.onMessage(this.jobId).subscribe((message) => {
    //   // console.log('Received message:', message);
    //   if (message.progress !== undefined) {
    //     this.progress = message.progress;
    //   }

    //   console.log("progress __ :" , this.progress)

    // });
  }

  async initializeSwiper() {
    this.swiper = new Swiper(this.swiperContainer?.nativeElement, {
      effect: 'cards',
      grabCursor: true,
      on: {
        init: () => {
          console.log('Swiper initialized', this.swiper);
        }
      }
    });
  }

  data_bl() {
    this.navCtrl.navigateRoot('/data-bl'); 
  }
  scan_bl() {
    this.navCtrl.navigateRoot('/scan-bl');  
  }

  async renameCard(index: number) {
    const alert = await this.alertController.create({
      header: 'Renommer le document',
      inputs: [
        {
          name: 'newTitle',
          type: 'text',
          value: this.slidesData[index].title,
          placeholder: 'New Title',
        },
      ],
      buttons: [
        {
          text: 'Annuler',
          role: 'cancel',
          cssClass: 'custom-alert-button-rename-doc cancel',
          handler: () => {
            console.log('Confirm Cancel');
          },
        },
        {
          text: 'Renommer',
          cssClass: 'custom-alert-button-rename-doc rename',
          handler: (data) => {
            this.slidesData[index].title = data.newTitle;
          },
        },
      ],
    });

    await alert.present();
  }

  async confirmDeleteCard(index: number) {
    const alert = await this.alertController.create({
      header: 'Supprimer le document',
      message: `Êtes-vous sûr de vouloir supprimer ce document ?`,
      buttons: [
        {
          text: 'Annuler',
          role: 'cancel',
          cssClass: 'custom-alert-button cancel',
          handler: () => {
            console.log('Confirm Cancel');
          },
        },
        {
          text: 'Oui, Supprimer !',
          cssClass: 'custom-alert-button danger',
          handler: () => {
            this.removeSlide(index);
            if (this.slidesData.length == 0) {
              localStorage.removeItem('selectedSupplier');
              this.navCtrl.navigateRoot('/scan-bl');
            }
          },
        },
      ],
    });

    await alert.present();
  }

  async removeSlide(index: number) {
    const bottomSwiperInstance = this.swiperContainer?.nativeElement.swiper;
    bottomSwiperInstance.removeSlide(index);
    bottomSwiperInstance.update();
    bottomSwiperInstance.slideTo(index - 1) ?? index > 0
    this.slidesData.splice(index, 1);
    console.log('Slide removed and swiper updated', bottomSwiperInstance);
  }

  async removeDoc(index: number) {
    const alert = await this.alertController.create({
      header: 'Supprimer le document',
      message: `Êtes-vous sûr de vouloir supprimer ce document ?`,
      buttons: [
        {
          text: 'Annuler',
          role: 'cancel',
          cssClass: 'custom-alert-button cancel',
          handler: () => {
            console.log('Confirm Cancel');
          },
        },
        {
          text: 'Oui, Supprimer !',
          cssClass: 'custom-alert-button danger',
          handler: () => {
            this.signalService.removeData(index);
            this.slidesData = this.signalService.getData();
            this.swiperContainer?.nativeElement.swiper?.removeSlide(index);
            this.swiperContainer?.nativeElement.swiper?.update();
            if (this.slidesData.length == 0) {
              localStorage.removeItem('selectedSupplier');
              this.navCtrl.navigateRoot('/scan-bl');
            }

          },
        },
      ],
    });

    await alert.present();
  }

  async removeAllDoc() {
    const alert = await this.alertController.create({
      header: 'Supprimer le document',
      message: `Êtes-vous sûr de vouloir supprimer Tous les documents ?`,
      buttons: [
        {
          text: 'Annuler',
          role: 'cancel',
          cssClass: 'custom-alert-button cancel',
          handler: () => {
            console.log('Confirm Cancel');
          },
        },
        {
          text: 'Oui, Supprimer !',
          cssClass: 'custom-alert-button danger',
          handler: () => {
            this.signalService.removeAllData();
            this.slidesData = [];
            this.swiperContainer?.nativeElement.swiper?.removeAllSlides();
            this.swiperContainer?.nativeElement.swiper?.update();
            localStorage.removeItem('selectedSupplier');
            // redirect to scan-bl
            this.navCtrl.navigateRoot('/scan-bl');
          },
        },
      ],
    });

    await alert.present();
  }

  async processOcrMulti() {

    this.jobId = this.apiService.generateJobId(); // Generate job ID
    const websocketUrl = `${environment.webSocketUrl}/${this.jobId}`;
    console.log('WebSocket URL doc-list:', websocketUrl);

    this.webSocketService.connect(websocketUrl, this.jobId);

    this.webSocketService.onMessage(this.jobId).subscribe((message) => {
      if (message.progress !== undefined) {
        this.progress = message.progress;
        console.log("progress __ :", this.progress);
      }
    });

    // const loading = await this.presentLoading(); // Show loading spinner
    this.isLoading = true;
    const that = this;

    // check if data.supplier_name && data.random_id exists
    if (this.slidesData.some(data => data.supplier_name == undefined || data.random_id == undefined || data.supplier_name == '' || data.random_id == '')) {
      const errorMessage = `
      <h3>Le fournisseur ou l'image est incorrect</h3>
      <ul>
        <li>Verifier le founerisseur selectionné</li>
        <li>Verifier que l'image est un document</li>
      </ul>
      `;
      this.apiService.showErrorAlert(errorMessage);
      this.isLoading = false;
      return;
    }

    // Get random_id from the first image since all images have the same random_id
    const globalRandomId = (this.slidesData[0].random_id ?? '').toString();

    const imageDataArray: ImageData[] = this.slidesData.map(data => {
      // Determine model name
      let modelName: string;
      if (data.supplier_name) {
        modelName = data.supplier_name.toUpperCase();
        if (modelName === 'UNKNOWN') {
          modelName = (localStorage.getItem('selectedSupplier') && localStorage.getItem('selectedSupplier') != 'undefined' && localStorage.getItem('selectedSupplier') != 'AUTRE') ? '' + localStorage.getItem('selectedSupplier') : 'GLOBAL';
        }
      } else {
        modelName = (localStorage.getItem('selectedSupplier') && localStorage.getItem('selectedSupplier') != 'undefined' && localStorage.getItem('selectedSupplier') != 'AUTRE') ? '' + localStorage.getItem('selectedSupplier') : 'GLOBAL';
      }

      // get it from the local storage (force the supplier to be global for apply the Advanced OCR 'Mindee')
      let forceSupplierGlobal = localStorage.getItem('forceSupplierGlobal') == 'true' ? true : false;

      // modelName = 'UNKNOWN' // -- for test error handling

      // Determine image URL
      // const models = ['GPM', 'SOPHADIMS', 'COOPER']
      // const imageUrl = models.includes(data.supplier_name?.toLocaleUpperCase() ?? '')
      //   ? (data.cropped_image?.substring(data.cropped_image?.indexOf('smart_crop_output')) ?? '')
      //   : (data.filtered_image?.substring(data.filtered_image?.indexOf('magic_pro_filter_output')) ?? '');
      
      const imageUrl = (data.cropped_image?.substring(data.cropped_image?.indexOf('smart_crop_output')) ?? ''); // Force to use smart_crop_output image 
      console.log('supplier_name:', data.supplier_name?.toLocaleUpperCase());
      console.log('Image URL:', imageUrl);
      return {
        image: imageUrl, // ( case of ['GPM', 'SOPHADIMS', 'COOPER'] suppliers )
        // model_name: forceSupplierGlobal == true ? 'GLOBAL' : modelName, // force the supplier to be global for apply the Advanced OCR 'Mindee'
        model_name: modelName, // force the supplier to be global for apply the Advanced OCR 'Mindee'
        random_id: data.random_id
      };
    });

    console.log('Image data array:', JSON.stringify(imageDataArray));


    // Get the current OCR mode from localStorage or use default
    const currentOcrMode = (localStorage.getItem('ocrMode') as OcrMode) || OcrMode.MINDEE_ADVANCED;

    this.apiService.processOcrMulti(imageDataArray, this.jobId, globalRandomId, currentOcrMode).subscribe(
      (response) => {
        console.log('OCR Multi response:', response);
        try {
          this.signalService.transformAndSetData(this.transformAndSetData(response['responses']));
          // loading.dismiss();
          this.isLoading = false;
          // this.navCtrl.navigateForward('/data-bl', { state: { data: this.signalService.getTransformedData() } }); // Redirect to data-bl page
          this.navCtrl.navigateForward('/data-bl-success', { state: { BL_id: response['ID_BL'], supplier_name: imageDataArray[0].model_name } }); // Redirect to data-bl-success page

          // Disconnect WebSocket after completion
          this.webSocketService.close(this.jobId!);
        }
        catch (e) {
          const errorMessage = `
          <h3>Erreur de traitement du document</h3>
          <ul>
            <li>Verifier que l'image est un document</li>
            <li>Verifier le founerisseur selectionné</li>
            <li>Verifier le bon cadrage du document</li>
            <li>Verifier la qualité de l'image</li>
            <li>Supprimer les objets inutiles dans l'image </li>
          </ul>
          `;
          this.apiService.showErrorAlert(errorMessage);
          this.isLoading = false;
          this.progress = 0;
          this.navCtrl.navigateRoot('/request-error');
        }


      },
      (error) => {
        // loading.dismiss();
        this.isLoading = false;
        const errorMessage = `
        <h3>Erreur de traitement du document</h3>
        <ul>
          <li>Verifier que l'image est un document</li>
          <li>Verifier le founerisseur selectionné</li>
          <li>Verifier le bon cadrage du document</li>
          <li>Verifier la qualité de l'image</li>
          <li>Supprimer les objets inutiles dans l'image </li>
        </ul>
        `;
        this.apiService.showErrorAlert(errorMessage);
        console.error('API error:', error);
        console.error(errorMessage);

        // // Disconnect WebSocket after completion
        this.webSocketService.close(this.jobId!);
      });

    this.webSocketService.onMessage(this.jobId).subscribe((message) => {
      if (message.progress !== undefined) {
        this.progress = message.progress;
      }
    });
  }

  async presentLoading() {
    const loading = await this.loadingController.create({
      message: 'Chargement...',
      spinner: 'circles',
      // duration: 30000 // Optional: specify a timeout for the loading spinner
    });
    await loading.present();
    return loading;
  }


  transformAndSetData(responseData: any) {
    try {
      const transformedData: TransformedDocData[] = responseData.map((item: any, index: number) => {
        const general = item.data?.general;
        const products = item.data.table
          .filter((product: any) =>
            product.designation ||
            product.quantity ||
            product.date_per ||
            product.ppv ||
            product.pph ||
            product.total_ttc
          ) // Filter out products with all fields null
          .map((product: any) => ({
            designation: product.designation,
            quantity: product.quantity ? parseInt(product.quantity) : null,
            expiryDate: product.date_per || null,
            ppv: product.ppv ? parseFloat(product.ppv) : null,
            pph: product.pph ? parseFloat(product.pph) : null,
            total: product.total_ttc ? parseFloat(product.total_ttc) : null,
          }));
        return {
          image: general.images_url_path.origin,
          title: `Scan ${this.signalService.formatDate()}`,
          date: this.signalService.getFormattedDate(general.date_export),
          page_index: index + 1,
          products,
        };
      });

      return transformedData;

    } catch (e) {
      console.log('Error:', e);
      const errorMessage = `
      <h3>Erreur de traitement du document</h3>
      <ul>
        <li>Verifier que l'image est un document</li>
        <li>Verifier le founerisseur selectionné</li>
        <li>Verifier le bon cadrage du document</li>
        <li>Verifier la qualité de l'image</li>
        <li>Supprimer les objets inutiles dans l'image </li>
      </ul>
      `;
      this.apiService.showErrorAlert(errorMessage);
      this.isLoading = false;
      this.progress = 0;
      this.navCtrl.navigateRoot('/request-error');

      return [];
    }
  }
}
