# tests/test_ocr_multiple.py

import pytest
from pathlib import Path
from fuzzywuzzy import fuzz
from src.app.app import process_model_image_with_module
# from src.app.utils import constants, expected_results_unitTest_main
from src.app.utils import constants, expected_results_unitTest
import json

# Get the project root directory
PROJECT_ROOT = Path(__file__).resolve().parent.parent.parent.parent
DATA_DIR = PROJECT_ROOT / "data" / "origin_BL"
OUTPUT_DIR = Path(__file__).resolve().parent.parent.parent / "data" / "output_preprocessing"

print(f"PROJECT_ROOT: {PROJECT_ROOT}")

# --------------------------------------------------------------------
# 1) Define ALL your model-image pairs (already given in your snippet)
# --------------------------------------------------------------------
MODEL_IMAGE_PAIRS = [
            (constants.GPM,  str(DATA_DIR / "BLS_1.jpg")),
            (constants.SOPHADIMS,  str(DATA_DIR / "BLS_3.jpg")),
            (constants.COOPER_PHARMA_CASA,  str(DATA_DIR / "BLS_4.jpg")),
            (constants.SPR,  str(DATA_DIR / "BLS_10.jpg")),
            (constants.SOPHACA,  str(DATA_DIR / "BL_scanned_1_1_cs.jpg")),
            (constants.GPM,  str(DATA_DIR / "origin_BL/cropped_GPM_25122024.jpg")),
            (constants.SOPHADIMS,  str(DATA_DIR / "origin_BL/cropped_SOPHADIM_25122024.jpg")),
            (constants.COOPER_PHARMA_CASA,  str(DATA_DIR / "origin_BL/cropped_COOPER_25122024.jpg")),
        ]


# --------------------------------------------------------------------
# 2) For each (model, image_path), define the expected outcome.
#    - Example: name_fournisseur, table_length, rows, etc.
#    - You can store fuzzy references for designations & exact quantity.
# --------------------------------------------------------------------
# EXPECTED_RESULTS = expected_results_unitTest_main.EXPECTED_RESULTS
EXPECTED_RESULTS = expected_results_unitTest.EXPECTED_RESULTS

print("\nAvailable keys in EXPECTED_RESULTS:")
for key in EXPECTED_RESULTS.keys():
    print(f"- {key}")

@pytest.mark.parametrize("model,image_path", MODEL_IMAGE_PAIRS)
@pytest.mark.asyncio
async def test_ocr_multiple(model, image_path):
    """
    Parametrized test for multiple images.
    Verifies:
      1) general.model_name == expected_header_supplier (case-insensitive)
      2) header.name_fournisseur ~ same as #1
      3) length of table
      4) fuzzy >= 80% (50% for original text) for 'designation'
      5) exact match for 'quantity'
    """

    print(f"Testing: model={model}, image={Path(image_path).name}")

    # If the current (model, image_path) isn't in EXPECTED_RESULTS,
    # we can skip or just do a minimal check:
    if (model, image_path) not in EXPECTED_RESULTS:
        print(f"WARNING: No expected results for {model}, {Path(image_path).name}")
        pytest.skip(f"No expected results defined for {model}, {image_path}")

    expected_data = EXPECTED_RESULTS[(model, image_path)]
    expected_header_supplier = expected_data["expected_header_supplier"]
    expected_table_length = expected_data["expected_table_length"]
    expected_rows = expected_data["expected_rows"]

    # ---------------------------------------------------
    # 1) Perform the OCR pipeline call
    # ---------------------------------------------------

    # Perform OCR
    result = await process_model_image_with_module(
        model, image_path, OUTPUT_DIR, "default", last_run=False, isAPI=False, src_app='winpluspharma'
    )
    assert result is not None, "Result is None"
    assert isinstance(result, dict), f"Expected dictionary, got {type(result)}"
    assert all(key in result for key in ["general", "header", "table"]), "Missing required keys"

    # # Debug print the result
    # print("Content:")
    # # Pretty-print the result dictionary with indentation
    # print(json.dumps(result, indent=4, ensure_ascii=False))

    # Verify it's a dictionary
    assert isinstance(result, dict), f"Expected dictionary, got {type(result)}"

    # Verify required keys exist
    assert "general" in result, "Missing 'general' key in result"
    assert "header" in result, "Missing 'header' key in result"
    assert "table" in result, "Missing 'table' key in result"

    # ---------------------------------------------------
    # 2) Perform the checks
    # ---------------------------------------------------

    # Check general.model_name
    general_model_name = result["general"]["model_name"]
    if general_model_name.lower() != expected_header_supplier.lower():
        print(f"ERROR: Model name mismatch: Expected '{expected_header_supplier}', Got '{general_model_name}'")
        assert False, "Model name check failed"

    # Check header.name_fournisseur
    header_fournisseur = result["header"]["name_fournisseur"]
    if header_fournisseur.lower() != expected_header_supplier.lower():
        print(f"ERROR: Header supplier mismatch: Expected '{expected_header_supplier}', Got '{header_fournisseur}'")
        assert False, "Header supplier check failed"

    # Check table length
    # 2d) Fuzzy 80% (50% for original text) on 'designation'; exact match on 'quantity'
    # We'll iterate over the minimum of (actual length, expected length)
    # to avoid index errors if there's a mismatch.
    # Alternatively, since we asserted table length, they should match up exactly.
    table_rows = result["table"]
    # if len(table_rows) != expected_table_length:
    if len(table_rows) < expected_table_length:
        expected_designations = {row["designation"] for row in expected_rows}
        actual_designations = {row["designation"] for row in table_rows}
        missing_rows = expected_designations - actual_designations
        extra_rows = actual_designations - expected_designations
        error_msg = f"ERROR: Table length mismatch: Expected {expected_table_length}, Got {len(table_rows)}"
        if missing_rows:
            error_msg += f"\n  Missing rows: {', '.join(missing_rows)}"
        if extra_rows:
            error_msg += f"\n  Extra rows: {', '.join(extra_rows)}"
        print(error_msg)
        assert False, "Table length check failed"

    # Check designation (fuzzy) and quantity (exact)
    fuzzy_errors = []
    quantity_errors = []
    for i, (actual_row, exp_row) in enumerate(zip(table_rows, expected_rows), start=1):
        actual_designation = actual_row["designation"]
        expected_designation = exp_row["designation"]
        ratio = fuzz.ratio(actual_designation, expected_designation)
        if ratio < 50:  # (80% for the result simulates in a real test) and (50% for original text)
            fuzzy_errors.append(f"Row #{i}: '{actual_designation}' vs '{expected_designation}' (Fuzzy: {ratio}%)")

        actual_quantity = actual_row["quantity"]
        expected_quantity = exp_row["quantity"]
        if actual_quantity != expected_quantity:
            quantity_errors.append(f"Row #{i}: Quantity Expected '{expected_quantity}', Got '{actual_quantity}'")

    if fuzzy_errors or quantity_errors:
        error_msg = "ERROR:"
        if fuzzy_errors:
            error_msg += "\n  Fuzzy match failures:\n    " + "\n    ".join(fuzzy_errors)
        if quantity_errors:
            error_msg += "\n  Quantity match failures:\n    " + "\n    ".join(quantity_errors)
        print(error_msg)
        assert False, "Designation or quantity check failed"

    print(f"Test passed for: model={model}, image={Path(image_path).name}")
