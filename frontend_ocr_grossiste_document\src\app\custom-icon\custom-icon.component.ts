import { Component, Input, OnInit } from '@angular/core';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';

@Component({
  selector: 'app-custom-icon',
  template: '<img [src]="iconUrl" [alt]="name" class="custom-icon">',
  styleUrls: ['./custom-icon.component.scss'],
})
export class CustomIconComponent  implements OnInit {

  @Input() name: string | undefined;
  iconUrl: SafeResourceUrl | undefined;

  constructor(private sanitizer: DomSanitizer) {}

  ngOnInit() {
    this.iconUrl = this.sanitizer.bypassSecurityTrustResourceUrl(`assets/icons/${this.name}.svg`);
  }

}
