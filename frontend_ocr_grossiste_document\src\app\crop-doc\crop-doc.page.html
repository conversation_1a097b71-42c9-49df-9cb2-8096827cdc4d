<ion-header [ngClass]="{'loading': isLoading}">
  <ion-toolbar>
    <ion-title>Scanner votre BL</ion-title>
    <ion-buttons slot="start">
      <ion-button  (click)="clearCroppedImage()">
        <ion-icon name="chevron-back-outline"></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content [ngClass]="{'loading': isLoading}" class="crop-doc-content">
  <app-check-network></app-check-network> 
  <div #cropper id="cropper" style="position:absolute; top:0; left:0; right:0; bottom:0;">
    <div class="rotate-button-container">
      <button class="rotate-btn" [class.needs-rotation]="needs_rotation" (click)="rotateImage(90)">
        <ion-icon name="refresh-outline"></ion-icon>
      </button>
    </div>
    <!-- <image-cropper 
      style="--active-stroke:5;--inactive-stroke:4;" 
      [ngStyle]="getCropperStyles()"
      hidefooter="">
    </image-cropper> -->
    <app-image-cropper-custom
    #cropperComponent
    [imageUrl]="imageUrl"
    [initialCoordinates]="coordinates"
    [rotation]="rotationAngle"
    [ngStyle]="getCropperStyles()">
  </app-image-cropper-custom>
  </div>

</ion-content>

 

<div class="alert-progress" [ngClass]="{'loading': isLoading}">
  <app-custom-alert [progress]="progress"></app-custom-alert>
</div>

<ion-footer [ngClass]="{'loading': isLoading}">
  <ion-toolbar>
    <ion-buttons>
      <ion-button class="menu-button active" size="small" (click)="removeCroppedImage()">
        <app-custom-icon name="delete"></app-custom-icon>
      </ion-button>
      <ion-button class="menu-button-middle" (click)="getUpdatedCoordinates()">
        <app-custom-icon name="extract"></app-custom-icon>
        <span>VALIDER</span>
      </ion-button>
      <ion-button class="menu-button active" size="small" (click)="reTakePhoto()">
        <app-custom-icon name="re-scan-2"></app-custom-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-footer>
