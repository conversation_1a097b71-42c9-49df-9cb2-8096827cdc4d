<ion-header [ngClass]="{'loading': isLoading}">
  <ion-toolbar>
    <ion-buttons slot="start">
      <!-- <ion-back-button defaultHref="/scan-bl"></ion-back-button> -->
    </ion-buttons>
    <ion-title>Scanner Médicament</ion-title>
    <ion-buttons slot="start">
      <ion-button routerLink="/profile">
        <ion-icon slot="icon-only" name="menu-outline"></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content class="medicament-ocr-content" [ngClass]="{'loading': isLoading}">
  <div class="div-content">
    <app-check-network></app-check-network>

    <!-- <div class="instructions">
      <p class="instruction-text">
        Scannez l'étiquette ou la boîte du médicament pour obtenir des informations.
      </p>
    </div> -->

    <div class="scan-wrapper" *ngIf="!hasScannedImage">
      <div class="content">
        <h2>Aucun médicament scanné</h2>
        <p>
          Scannez l'étiquette ou la boîte d'un médicament pour obtenir des informations détaillées.
        </p>
      </div>

      <!-- New scan buttons when no image is displayed -->
      <div class="scan-buttons">
        <ion-button expand="block" class="camera-button" (click)="openCamera()">
          <ion-icon name="camera-outline" slot="start"></ion-icon>
          <span *ngIf="!isWeb">Caméra</span>
          <span *ngIf="isWeb">Scanner</span>
        </ion-button>
        
        <ion-button expand="block" class="gallery-button" (click)="openGallery()">
          <ion-icon name="images-outline" slot="start"></ion-icon>
          Galerie
        </ion-button>
      </div>
    </div>

    <!-- Display scanned image -->
    <div class="scanned-image-container" *ngIf="hasScannedImage">
      <h2 class="section-title">Image scannée</h2>
      <div class="image-preview">
        <img [src]="imagePreviewUrl" alt="Médicament scanné" />
      </div>
      
      <ion-button expand="block" class="rescan-button" (click)="resetScan()">
        <ion-icon name="refresh-outline" slot="start"></ion-icon>
        Scanner à nouveau
      </ion-button>
    </div>

    <!-- Display suggestions -->
    <div class="suggestions-container" *ngIf="suggestions.length > 0">
      <h2 class="section-title">Suggestions</h2>
      <div class="suggestion-list">
        <ion-card *ngFor="let suggestion of suggestions" class="suggestion-card">
          <ion-card-header>
            <ion-card-title>{{ suggestion.designation }}</ion-card-title>
            <ion-card-subtitle *ngIf="suggestion.laboratory">{{ suggestion.laboratory }}</ion-card-subtitle>
          </ion-card-header>
          <ion-card-content>
            <div class="suggestion-details">
              <div class="detail-item" *ngIf="suggestion.code_barre">
                <span class="label">Code Barre:</span>
                <span class="value">{{ suggestion.code_barre }}</span>
              </div>
              <div class="detail-item" *ngIf="suggestion.ppv">
                <span class="label">PPV:</span>
                <span class="value">{{ suggestion.ppv }} Dhs</span>
              </div>
              <div class="detail-item" *ngIf="suggestion.dosage">
                <span class="label">Dosage:</span>
                <span class="value">{{ suggestion.dosage }}</span>
              </div>
            </div>
          </ion-card-content>
        </ion-card>
      </div>
    </div>

    <!-- No results found message -->
    <div class="no-results" *ngIf="hasScannedImage && suggestions.length === 0 && !isLoading">
      <ion-icon name="alert-circle-outline"></ion-icon>
      <h3>Aucun résultat trouvé</h3>
      <p>Essayez de scanner à nouveau ou vérifiez que l'étiquette est bien visible.</p>
    </div>
  </div>
</ion-content>

<div class="alert-progress" [ngClass]="{'loading': isLoading}">
  <app-custom-alert [progress]="progress"></app-custom-alert>
</div>
