import { NgModule } from '@angular/core';
import { PreloadAllModules, RouterModule, Routes } from '@angular/router';
import { AuthGuard } from './interceptors/auth.guard';
import { OnboardingGuard } from './interceptors/onboardin.guard';
import { PublicGuard } from './interceptors/public.guard';

const routes: Routes = [
  {
    path: 'home',
    loadChildren: () => import('./welcome/welcome.module').then( m => m.WelcomePageModule)
  },
  {
    path: '',
    redirectTo: 'onboarding',
    pathMatch: 'full'
  },
  {
    path: 'onboarding',
    loadChildren: () => import('./onboarding/onboarding.module').then( m => m.OnboardingPageModule),
    canActivate: [OnboardingGuard, PublicGuard]
  },
  {
    path: 'welcome',
    loadChildren: () => import('./welcome/welcome.module').then( m => m.WelcomePageModule),
    canActivate: [PublicGuard]
  },
  {
    path: 'login',
    loadChildren: () => import('./login/login.module').then( m => m.LoginPageModule),
    canActivate: [PublicGuard]
  },
  {
    path: 'guide',
    loadChildren: () => import('./guide/guide.module').then( m => m.GuidePageModule),
    canActivate: [AuthGuard]
  },
  {
    path: 'scan-bl',
    loadChildren: () => import('./scan-bl/scan-bl.module').then( m => m.ScanBLPageModule), 
    canActivate: [AuthGuard]
  },
  {
    path: 'doc-list',
    loadChildren: () => import('./doc-list/doc-list.module').then( m => m.DocListPageModule), 
    canActivate: [AuthGuard]
  },
  {
    path: 'data-bl',
    loadChildren: () => import('./data-bl/data-bl.module').then( m => m.DataBLPageModule), 
    canActivate: [AuthGuard]
  },
  {
    path: 'crop-doc',
    loadChildren: () => import('./crop-doc/crop-doc.module').then( m => m.CropDocPageModule), 
    canActivate: [AuthGuard]
  },
  {
    path: 'process-doc',
    loadChildren: () => import('./process-doc/process-doc.module').then( m => m.ProcessDocPageModule), 
    canActivate: [AuthGuard]
  },
  {
    path: 'network-error',
    loadChildren: () => import('./network-error/network-error.module').then(m => m.NetworkErrorPageModule)
  },
  {
    path: 'request-error',
    loadChildren: () => import('./request-error/request-error.module').then( m => m.RequestErrorPageModule)
  },
  {
    path: 'realtime-contours',
    loadChildren: () => import('./realtime-contours/realtime-contours.module').then( m => m.RealtimeContoursPageModule),
    canActivate: [AuthGuard]
  },
  {
    path: 'data-bl-success',
    loadChildren: () => import('./data-bl-success/data-bl-success.module').then( m => m.DataBlSuccessPageModule)
  },
  {
    path: 'profile',
    loadChildren: () => import('./profile/profile.module').then( m => m.ProfilePageModule)
  },
  {
    path: 'medicament-ocr',
    loadChildren: () => import('./medicament-ocr/medicament-ocr.module').then( m => m.MedicamentOcrPageModule)
  }





];

@NgModule({
  imports: [
    RouterModule.forRoot(routes, { preloadingStrategy: PreloadAllModules })
  ],
  exports: [RouterModule]
})
export class AppRoutingModule { }
