import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ImageCropperCustomComponent } from './image-cropper-custom.component';
import { ChangeDetectorRef } from '@angular/core';

describe('ImageCropperCustomComponent', () => {
  let component: ImageCropperCustomComponent;
  let fixture: ComponentFixture<ImageCropperCustomComponent>;
  let changeDetectorRefSpy: jasmine.SpyObj<ChangeDetectorRef>;

  beforeEach(async () => {
    changeDetectorRefSpy = jasmine.createSpyObj('ChangeDetectorRef', ['detectChanges']);

    await TestBed.configureTestingModule({
      declarations: [ ImageCropperCustomComponent ],
      providers: [
        { provide: ChangeDetectorRef, useValue: changeDetectorRefSpy }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(ImageCropperCustomComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });
});
