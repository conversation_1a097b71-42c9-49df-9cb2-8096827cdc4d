@import url("https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap");

:host {
  // display: block;
  height: 100dvh;
  background-color: #f8f9fa; /* Example background color */
  z-index: 101; /* Example z-index */
  position: relative;
}
* {
  font-family: "Inter", sans-serif;
  font-optical-sizing: auto;
}

ion-content::part(scroll) {
  overflow-y: hidden !important;
  --overflow: hidden !important;
}

ion-header {
  height: 70px;
  --border: 0;
}
ion-content {
  --offset-top : 0px !important;
  background: #fff;
  // height: 80vh;
}
::ng-deep .swal2-container {
  height: 100vh !important;
}

::ng-deep .swal2-html-container{
  padding: 1em 0.6em 0.3em !important;
  ul li{
    text-align: left;
    padding: 13px 10px 0 0px;
  }
}

::ng-deep .swal2-footer {
  padding: 0 !important;
  margin: 1.5em 0.5rem 2rem;
  a{
    position: relative;
    font-size: 16px;
    font-weight: bold;
    text-decoration: none;
    padding: 20px 2%;
    background-color: #2f4fcd;
    color: #fff;
    line-height: 1.5;
    border-radius: 10px;
    margin-bottom: 10px;
    line-height: 1.5;
    box-shadow: 0 0 12px 0px #0053e5;
    animation: ping 1.2s infinite ease-out;

  }
  a::before {
    content: "";
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(47, 79, 205, 0.5);
    border-radius: 5px;
    transform: scale(1);
    opacity: 0;
    animation: _ngcontent-ng-c2504662896_ping-pulse 2s infinite;
  }
}



ion-header ion-toolbar {
  height: 100%;
  --border: 0;
  --border-width: 0;
  display: flex;
  justify-content: center;
  align-items: flex-end;
  flex-direction: row;
  --background: #fff !important;
}

ion-header ion-toolbar ion-title {
  font-size: 26px;
  font-weight: 700;
  color: #2f4fcd;
  text-align: center;
  width: 100%;
  padding: 0;
  // padding-left: 3.8rem;
}

ion-header ion-toolbar ion-icon {
  color: #505050;
  padding-left: 0.7rem;
}

.wrapper {
  background: url("/assets/bg-scan-bl.png") no-repeat center center fixed;
  background-size: cover;
  height: 83%;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  align-items: center;
  margin-top: 2rem;
}

ion-header ion-toolbar ion-icon {
  color: #101010;
  padding-right: 1rem;
}

/* --------------------------
  Dribbble
--------------------------- */

svg {
  margin: auto;
  display: block;
  height: 245px;
  width: 315px;
}

/* --------------------------
  Keyframes
--------------------------- */

@-webkit-keyframes starAnimation {
  0% {
    -webkit-transform: scale(1, 1);
    transform: scale(1, 1);
    opacity: 0.7;
  }
  30% {
    -webkit-transform: scale(1.05, 1.05);
    transform: scale(1.05, 1.05);
    opacity: 1;
  }
  100% {
    -webkit-transform: scale(1, 1);
    transform: scale(1, 1);
    opacity: 0.7;
  }
}

@keyframes starAnimation {
  0% {
    -webkit-transform: scale(1, 1);
    transform: scale(1, 1);
    opacity: 0.7;
  }
  30% {
    -webkit-transform: scale(1.05, 1.05);
    transform: scale(1.05, 1.05);
    opacity: 1;
  }
  100% {
    -webkit-transform: scale(1, 1);
    transform: scale(1, 1);
    opacity: 0.7;
  }
}

@-webkit-keyframes circlesAnimationTop {
  0% {
    -webkit-transform: translate(0px, -10px);
    transform: translate(0px, -10px);
  }
  30% {
    -webkit-transform: translate(0px, -5px);
    transform: translate(0px, -5px);
  }
  60% {
    -webkit-transform: translate(1px, 10px);
    transform: translate(1px, 10px);
  }
  100% {
    -webkit-transform: translate(0px, -10px);
    transform: translate(0px, -10px);
  }
}

@keyframes circlesAnimationTop {
  0% {
    -webkit-transform: translate(0px, -10px);
    transform: translate(0px, -10px);
  }
  30% {
    -webkit-transform: translate(0px, -5px);
    transform: translate(0px, -5px);
  }
  60% {
    -webkit-transform: translate(1px, 10px);
    transform: translate(1px, 10px);
  }
  100% {
    -webkit-transform: translate(0px, -10px);
    transform: translate(0px, -10px);
  }
}

@-webkit-keyframes circlesAnimationBottom {
  0% {
    -webkit-transform: translate(0px, 0px) rotate(0deg);
    transform: scale(1) translate(0px, 0px) rotate(0deg);
  }
  50% {
    -webkit-transform: translate(5px, 5px) rotate(285deg);
    transform: scale(1.5) translate(5px, 5px) rotate(285deg);
  }
  100% {
    -webkit-transform: translate(0px, 0px) rotate(0deg);
    transform: scale(1.2) translate(0px, 0px) rotate(0deg);
  }
}

@keyframes circlesAnimationBottom {
  0% {
    -webkit-transform: translate(0px, 0px) rotate(0deg);
    transform: scale(1) translate(0px, 0px) rotate(0deg);
  }
  50% {
    -webkit-transform: translate(5px, 5px) rotate(285deg);
    transform: scale(1.5) translate(5px, 5px) rotate(285deg);
  }
  100% {
    -webkit-transform: translate(0px, 0px) rotate(0deg);
    transform: scale(1.2) translate(0px, 0px) rotate(0deg);
  }
}

@-webkit-keyframes shadowLoop {
  0% {
    -webkit-transform: translate(0, -35px) scale(1.15, 0.25);
    transform: translate(0, -35px) scale(1.15, 0.25);
  }
  100% {
    -webkit-transform: translate(0, -35px) scale(0.8, 0.18);
    transform: translate(0, -35px) scale(0.8, 0.18);
  }
}

@keyframes shadowLoop {
  0% {
    -webkit-transform: translate(0, -35px) scale(1.15, 0.25);
    transform: translate(0, -35px) scale(1.15, 0.25);
  }
  100% {
    -webkit-transform: translate(0, -35px) scale(0.8, 0.18);
    transform: translate(0, -35px) scale(0.8, 0.18);
  }
}

@-webkit-keyframes dropFall {
  0% {
    -webkit-transform: translate(0, -25px);
    transform: translate(0, -25px);
  }
  100% {
    -webkit-transform: translate(0, 125px);
    transform: translate(0, 125px);
    opacity: 0;
  }
}

@keyframes dropFall {
  0% {
    -webkit-transform: translate(0, -25px);
    transform: translate(0, -25px);
  }
  100% {
    -webkit-transform: translate(0, 125px);
    transform: translate(0, 125px);
    opacity: 0;
  }
}

@-webkit-keyframes cloudLoop {
  0% {
    -webkit-transform: translate(0, 15px);
    transform: translate(0, 15px);
  }
  100% {
    -webkit-transform: translate(0, 0);
    transform: translate(0, 0);
  }
}
@keyframes cloudLoop {
  0% {
    -webkit-transform: translate(0, 15px);
    transform: translate(0, 15px);
  }
  100% {
    -webkit-transform: translate(0, 0);
    transform: translate(0, 0);
  }
}

/* --------------------------
  SVG Styles
--------------------------- */

#noConnection * {
  -webkit-animation-iteration-count: infinite;
  animation-iteration-count: infinite;
  -webkit-animation-timing-function: cubic-bezier(0, 0, 1, 1);
  animation-timing-function: cubic-bezier(0, 0, 1, 1);
}

.star,
.circlesBottom,
.circlesTop {
  -webkit-transform-origin: 50% 50%;
  transform-origin: 50% 50%;
  transform-box: fill-box;
  -webkit-animation-duration: 2s;
  animation-duration: 2s;
}

.star {
  -webkit-animation: starAnimation 0.8s cubic-bezier(0, 0, 1, 20);
  animation: starAnimation 0.8s cubic-bezier(0, 0, 1, 20);
}

.circlesBottom {
  -webkit-animation: circlesAnimationBottom;
  animation: circlesAnimationBottom;
}

.circlesTop {
  -webkit-animation: circlesAnimationTop;
  animation: circlesAnimationTop;
  -webkit-animation-delay: 1.5s;
  animation-delay: 1.5s;
}

.circlesBottom,
.circlesTop {
  -webkit-animation-duration: 4s;
  animation-duration: 4s;
  -webkit-animation-timing-function: ease-out;
  animation-timing-function: ease-out;
  -webkit-animation-fill-mode: backwards;
  animation-fill-mode: backwards;
}

.cloud {
  -webkit-animation: cloudLoop 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55)
    infinite alternate;
  animation: cloudLoop 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55) infinite
    alternate;
}

.shadow {
  background: #BDC4D7;
  opacity: 0.4;
  height: 55px;
  width: 75px;
  border-radius: 50px;
  margin: auto;
  -webkit-transform: translate(0, -35px) scale(1.35, 0.25);
  transform: translate(0, -35px) scale(1.35, 0.25);
  -webkit-animation: shadowLoop 0.8s ease infinite alternate;
  animation: shadowLoop 0.8s ease infinite alternate;
}

.rain {
  display: block;
  text-align: center;
  margin: auto;
  height: 90px;
  width: 100px;
  overflow: hidden;
  margin-top: -80px;
  z-index: 0;
}

.drop {
  display: inline-block;
  background: #A9C6F0;
  height: 25px;
  width: 4px;
  margin: 5px;
  border-radius: 25px;
  opacity: 0.85;
  -webkit-animation: dropFall 1s infinite;
  animation: dropFall 1s infinite;
}

.drop.fast {
  opacity: 0.75;
  -webkit-animation-duration: 0.5s;
  animation-duration: 0.5s;
}

.drop.faster {
  opacity: 0.5;
  -webkit-animation-duration: 0.35s;
  animation-duration: 0.35s;
}

.drop.slow {
  -webkit-animation-duration: 0.85s;
  animation-duration: 0.85s;
}

.lightning {
  opacity: 0.3;
  z-index: 10;
}

.cloud-svg{
  margin-top: 3rem;
}

/* --------------------------
  messge-error-network
--------------------------- */

.messge-error-network{
  font-family: "Poppins", sans-serif;
  font-size: 1.2rem;
  font-weight: 500;
  color: #505050;
  text-align: center;
  margin-top: 1rem;
  padding: 0 2rem;
  text-shadow: 0px 1px 1px rgba(0, 0, 0, 0.2);
}

ion-button::part(native) {
  background-color: #2f4fcd;
}

ion-button::part(native) {
  padding: 0 3rem !important;
  color: #fff !important;
}



::ng-deep ion-alert .alert-message{
  padding: 10px 5px !important;
}

::ng-deep ion-alert .alert-message h3 {
  font-size: 13px;
  color: red;
  text-shadow: 0 0 0 red;
  font-weight: bold;
  letter-spacing: 1px;
  padding: 0 2px;
}

::ng-deep ion-alert .alert-message ul {
  padding-left: 20px !important;
}
::ng-deep ion-alert .alert-message ul li {
  text-align: left;
  // padding-left: 20px;
  color: #333333;
  line-height: 1.6;
  font-size: 13px;
  font-weight: bold;
}


@media (prefers-color-scheme: dark) {
  ion-content {
    --background: #fff
  }
  ion-header ion-toolbar {
    --background: #fff !important;
  }
}

@media (prefers-color-scheme: light) {
  ion-content {
    --background: #fff
  }
  ion-header ion-toolbar {
    --background: #fff !important;
  }
}


@keyframes ping-pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  80% {
    transform: scale(1.2);
    opacity: 0;
  }
  100% {
    transform: scale(1.3);
    opacity: 0;
  }
}