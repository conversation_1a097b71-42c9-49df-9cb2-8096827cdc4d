# Local environment variables
# Application Environment
ENVIRONMENT=local
DEBUG=True
SYS_ARGV=main

# Tesseract OCR Configuration
TESSERACT_PATH=C:\Program Files\Tesseract-OCR\tesseract.exe

# API Configuration
API_URL=https://winproduit.sophatel.com:8001
TAP_URL=https://winproduit.sophatel.com:8005

# WinPlus ERP Integration
WINPLUS_AUTH_USER=https://vps5.sophatel.com:5305/api/user/auth
WINPLUS_AUTH_TENANT=https://vps5.sophatel.com:5305/api/user/auth-tenant
WINPLUS_URL=https://vps5.sophatel.com:5305

# Pharmalien Integration
PHARMALIEN_AUTH_URL=https://vps5.sophatel.com:4201/api/user/auth

# JWT Authentication
SECRET_KEY=paython-ocr-insecure-#8!7z!_
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=180 # 3 hours

# PostgreSQL Database Configuration (Local)
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=ocr_document_grossiste
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres
POSTGRES_SSL_MODE=disable

