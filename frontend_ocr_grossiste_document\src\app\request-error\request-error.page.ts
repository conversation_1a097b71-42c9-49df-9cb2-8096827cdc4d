import { Component, OnInit } from '@angular/core';
import { Location } from '@angular/common';

@Component({
  selector: 'app-request-error',
  templateUrl: './request-error.page.html',
  styleUrls: ['./request-error.page.scss'],
})
export class RequestErrorPage implements OnInit {

  constructor(private location: Location) {}

  isLogged = false;
  header_page_title = '';
  message_error = '';
  button_label = 'Retour';

  ngOnInit() {
    this.isLogged = !!localStorage.getItem('tokenUser') && !!localStorage.getItem('tokenTenant') && !!localStorage.getItem('token');
    this.header_page_title = this.isLogged ? 'Server Error' : 'Vous n\'êtes pas connecté';
    this.message_error = this.isLogged ? 'Une erreur est survenue lors de la requête au serveur. Veuillez réessayer plus tard. 🔄' : 'Vous n\'êtes pas connecté. Veuillez vous connecter pour accéder à cette page. 🔄';
    this.button_label = this.isLogged ? 'Retour' : 'Se connecter';
  }

  goBack() {
    this.location.back();
  }

}
