#!/usr/bin/env python3
"""
Diagnostic script to check the current setup and identify issues.
"""

import sys
import os
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def check_environment_files():
    """Check if environment files exist."""
    print("📁 Checking environment files...")
    
    env_files = ['.env.local', '.env.prod', '.env']
    found_files = []
    
    for env_file in env_files:
        if Path(env_file).exists():
            found_files.append(env_file)
            print(f"   ✅ Found: {env_file}")
        else:
            print(f"   ❌ Missing: {env_file}")
    
    if not found_files:
        print("   ⚠️  No environment files found!")
        print("   💡 Run: python scripts/setup_env.py")
        return False
    
    return True

def check_environment_variables():
    """Check if required environment variables are set."""
    print("\n🔧 Checking environment variables...")
    
    try:
        from src.app.config import (
            POSTGRES_HOST, POSTGRES_PORT, POSTGRES_DB,
            POSTGRES_USER, POSTGRES_PASSWORD, POSTGRES_URL
        )
        
        print(f"   POSTGRES_HOST: {POSTGRES_HOST}")
        print(f"   POSTGRES_PORT: {POSTGRES_PORT}")
        print(f"   POSTGRES_DB: {POSTGRES_DB}")
        print(f"   POSTGRES_USER: {POSTGRES_USER}")
        print(f"   POSTGRES_PASSWORD: {'*' * len(POSTGRES_PASSWORD) if POSTGRES_PASSWORD else 'NOT SET'}")
        
        if not POSTGRES_PASSWORD:
            print("   ⚠️  PostgreSQL password is not set!")
            return False
        
        print("   ✅ All PostgreSQL environment variables are set")
        return True
        
    except Exception as e:
        print(f"   ❌ Error loading environment variables: {e}")
        return False

def check_dependencies():
    """Check if required dependencies are installed."""
    print("\n📦 Checking dependencies...")
    
    required_packages = [
        'psycopg2',
        'psycopg2-binary', 
        'sqlalchemy'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"   ✅ {package}")
        except ImportError:
            print(f"   ❌ {package}")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"   ⚠️  Missing packages: {', '.join(missing_packages)}")
        print("   💡 Run: pip install -r requirements-postgresql.txt")
        return False
    
    return True

def check_database_connection():
    """Check database connection."""
    print("\n🔗 Checking database connection...")
    
    try:
        import psycopg2
        from src.app.config import (
            POSTGRES_HOST, POSTGRES_PORT, POSTGRES_DB,
            POSTGRES_USER, POSTGRES_PASSWORD
        )
        
        conn = psycopg2.connect(
            host=POSTGRES_HOST,
            port=POSTGRES_PORT,
            database=POSTGRES_DB,
            user=POSTGRES_USER,
            password=POSTGRES_PASSWORD,
            connect_timeout=10
        )
        conn.close()
        print("   ✅ PostgreSQL connection successful")
        return True
        
    except psycopg2.OperationalError as e:
        print(f"   ❌ PostgreSQL connection failed: {e}")
        print("   💡 Possible solutions:")
        print("      - Check if PostgreSQL is running")
        print("      - Verify credentials in .env.local")
        print("      - For local dev: docker-compose -f docker-compose.dev.yml up -d")
        return False
    except Exception as e:
        print(f"   ❌ Unexpected error: {e}")
        return False

def suggest_solutions():
    """Suggest solutions based on the diagnosis."""
    print("\n💡 Suggested Solutions:")
    print("=" * 50)
    
    if not Path('.env.local').exists():
        print("1. Create environment configuration:")
        print("   python scripts/setup_env.py")
        print()
    
    print("2. For local development, start PostgreSQL:")
    print("   docker-compose -f docker-compose.dev.yml up -d")
    print()
    
    print("3. Test the connection:")
    print("   python scripts/test_connection.py")
    print()
    
    print("4. Initialize PostgreSQL:")
    print("   python scripts/init_postgresql.py")
    print()
    
    print("5. Start the application:")
    print("   uvicorn src.api:app --host 0.0.0.0 --port 8088")

def main():
    print("🔍 OCR Document Grossiste - System Diagnosis")
    print("=" * 60)
    
    checks = [
        check_environment_files(),
        check_dependencies(),
        check_environment_variables(),
        check_database_connection()
    ]
    
    print("\n" + "=" * 60)
    
    if all(checks):
        print("🎉 All checks passed! Your system is ready.")
        print("Run: python scripts/init_postgresql.py")
    else:
        print("❌ Some checks failed. See suggestions below.")
        suggest_solutions()

if __name__ == "__main__":
    main()
