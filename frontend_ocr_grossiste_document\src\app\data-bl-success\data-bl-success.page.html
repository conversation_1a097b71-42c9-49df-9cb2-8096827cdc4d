<ion-header>
  <ion-toolbar>
    <ion-title>Envoyer les données</ion-title>
    <ion-buttons slot="end">
      <ion-button (click)="logout()">
        <ion-icon slot="icon-only" name="log-out-outline"></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>

</ion-header>

<ion-content>
  <div class="modal-overlay data-bl-wrapper">
    <div class="modal-container">
      <button class="close-button"></button>
      <h2 class="modal-title">Traitement terminé avec succès</h2>
      <p class="modal-message">
        Votre BL de <b>{{ supplier_name }}</b> a été traité et est prêt à être envoyé.
      </p>
      <ion-row>
        <ion-col>
          <ion-item lines="none" class="input-item">
            <div class="label-wrapper">
              <label for="id-bl">
                Numéro du BL <span class="required-star">*</span>
              </label>
              <ion-input
                #idBlInput
                id="id-bl"
                [clearInput]="true"
                placeholder="Numéro du BL"
                type="text"
                value="{{ id_BL_origine }}"
                [(ngModel)]="id_BL_origine"
                (click)="onInputClick($event)"
                autocomplete="off"
                autocorrect="off"
                enterkeyhint="next"
              ></ion-input>
            </div>
          </ion-item>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <ion-item lines="none" class="input-item">
            <div class="label-wrapper">
              <label for="date-bl">
                Date Bl
              </label>
              <ion-input
                id="date-bl"
                [clearInput]="true"
                placeholder="Date BL"
                type="date"
                value="{{ date_BL_origine }}"
                [(ngModel)]="date_BL_origine"
              ></ion-input>
            </div>
          </ion-item>
        </ion-col>
      </ion-row>
      <div class="modal-buttons">
        <button class="button button-cancel button-modal" (click)="IgnorerBL()">Ignorer</button>
        <button class="button button-success button-modal" (click)="sendBL()">Envoyer</button>
      </div>
    </div>
  </div>
</ion-content>

<ion-footer>
  <ion-toolbar>
    <ion-buttons>
      <ion-button class="menu-button active" size="small" (click)="EditCurrentBL()">
        <app-custom-icon name="files"></app-custom-icon>
      </ion-button>
      <ion-button class="menu-button-middle" (click)="NewBL()">
        <app-custom-icon name="extract"></app-custom-icon>
        <span>NOUVEAU</span>
      </ion-button>
      <ion-button class="menu-button" size="small" [routerLink]="['/profile']">
        <app-custom-icon name="settings"></app-custom-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-footer>
