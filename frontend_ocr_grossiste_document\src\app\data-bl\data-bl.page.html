<ion-header>
  <ion-toolbar>
    <ion-title>Extraction des données</ion-title>
    <!-- <ion-buttons slot="end">
      <ion-button>
        <ion-icon slot="icon-only" name="search-outline"></ion-icon>
      </ion-button>
    </ion-buttons> -->
  </ion-toolbar>
</ion-header>

<ion-content class="data-bl-wrapper">
  <h2 class="section-title">Pages</h2>
  <swiper-container
    effect="coverflow"
    grab-cursor="true"
    centered-slides="true"
    slides-per-view="auto"
    coverflow-effect-rotate="50"
    coverflow-effect-stretch="0"
    coverflow-effect-depth="100"
    coverflow-effect-modifier="1"
    coverflow-effect-slide-shadows="true"
    class="swiper top-swiper"
    #topSwiper
    (swiperslidechange)="onSlideChange()"
  >
    <swiper-slide *ngFor="let slide of slidesData" class="test">
      <ion-row>
        <ion-col size="12" class="slide-content">
          <ion-card class="card-doc">
            <img [src]="slide.image" class="slide-image" />
            <div class="content-global-card">
              <ion-card-header>
                <ion-card-subtitle>{{ slide.title }}</ion-card-subtitle>
              </ion-card-header>

              <ion-card-content>
                <span>{{ slide.date }}</span>
                <span>Page {{ slide.page_index }}</span>
              </ion-card-content>
            </div>
          </ion-card>
        </ion-col>
      </ion-row>
    </swiper-slide>
  </swiper-container>

  <ion-card class="card-data-bl">
    <ion-card-header>
      <app-custom-icon name="arrow-left" (click)="slidePrev()"></app-custom-icon>
      
      <div class="content-header">
        <ion-card-title>Produit {{ currentProductIndex }} / {{ currentSlideProducts.length }}</ion-card-title>
        <ion-card-subtitle>Page {{ currentPageIndex }}</ion-card-subtitle>
      </div>
      <app-custom-icon name="arrow-right" (click)="slideNext()"></app-custom-icon>

    </ion-card-header>

    <hr class="hr-card">

    <swiper-container class="bottom-swiper" #bottomSwiper>
      <swiper-slide *ngFor="let product of currentSlideProducts">
        <ion-card-content>
          <div class="data-bl">
            <ion-row>
              <ion-col>
                <ion-item lines="none" class="input-item">
                  <ion-input
                    label="Désignation"
                    labelPlacement="stacked"
                    [clearInput]="true"
                    placeholder="Enter la Désignation du produit ..."
                    value="{{ product.designation }}"
                  >
                  </ion-input>
                </ion-item>
              </ion-col>
            </ion-row>

            <ion-row>
              <ion-col>
                <ion-item lines="none" class="input-item">
                  <ion-input
                    label="Quantité Livré"
                    labelPlacement="stacked"
                    [clearInput]="true"
                    placeholder="Enter la Quantité livré"
                    type="number"
                    value="{{ product.quantity }}"
                  >
                  </ion-input>
                </ion-item>
              </ion-col>
              <ion-col>
                <ion-item lines="none" class="input-item input-item-date">
                  <ion-input
                    label="Date de péremption"
                    labelPlacement="stacked"
                    [clearInput]="true"
                    placeholder="Enter la Date de péremption"
                    type="date"
                    value="{{ product.expiryDate }}"
                  >
                  </ion-input>
                </ion-item>
              </ion-col>
            </ion-row>

            <ion-row>
              <ion-col>
                <ion-item lines="none" class="input-item">
                  <ion-input
                    label="Prix (PPV)"
                    labelPlacement="stacked"
                    [clearInput]="true"
                    placeholder="Enter le Prix (PPV)"
                    type="number"
                    value="{{ product.ppv }}"
                  >
                  </ion-input>
                </ion-item>
              </ion-col>
              <ion-col>
                <ion-item lines="none" class="input-item">
                  <ion-input
                    label="Prix (PPH)"
                    labelPlacement="stacked"
                    [clearInput]="true"
                    placeholder="Enter la Prix (PPH)"
                    type="number"
                    value="{{ product.pph }}"
                  >
                  </ion-input>
                </ion-item>
              </ion-col>
            </ion-row>

            <ion-row>
              <ion-col>
                <ion-item lines="none" class="input-item">
                  <ion-input
                    label="Total (TTC)"
                    labelPlacement="stacked"
                    [clearInput]="true"
                    placeholder="Enter le Total (TTC)"
                    type="number"
                    value="{{ product.total }}"
                  >
                  </ion-input>
                </ion-item>
              </ion-col>
            </ion-row>
          </div>
        </ion-card-content>
      </swiper-slide>
    </swiper-container>
  </ion-card>

</ion-content>

<ion-footer>
  <ion-toolbar>
    <ion-buttons>
      <ion-button class="menu-button active" size="small" (click)="EditCurrentBL()">
        <app-custom-icon name="files"></app-custom-icon>
      </ion-button>
      <ion-button class="menu-button-middle" (click)="NewBL()">
        <app-custom-icon name="extract"></app-custom-icon>
        <span>NOUVEAU</span>
      </ion-button>
      <ion-button class="menu-button" size="small" [routerLink]="['/profile']">
        <app-custom-icon name="settings"></app-custom-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-footer>
