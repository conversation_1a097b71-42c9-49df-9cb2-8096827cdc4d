import { Injectable } from '@angular/core';
import { webSocket, WebSocketSubject } from 'rxjs/webSocket';
import { environment } from '../../environments/environment';
import { Observable, timer } from 'rxjs';
import { retryWhen, delayWhen, tap } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class RealtimeProcessingService {
  private socket$: WebSocketSubject<any> | undefined;
  private pingInterval: any;

  constructor() {
    this.initializeWebSocket();
  }

  private initializeWebSocket() {
    console.log('Initializing WebSocket connection...');
    this.socket$ = webSocket({
      url: `${environment.webSockeRealTimetUrl}`,
      openObserver: {
        next: () => {
          console.log('WebSocket connection opened');
          this.startPing();
        }
      },
      closeObserver: {
        next: () => {
          console.log('WebSocket connection closed');
          this.stopPing();
        }
      }
    });
  }

  private startPing() {
    let that = this;
    this.pingInterval = setInterval(() => {
      that.socket$?.next({ type: 'ping' });
    }, 5000); // Send a ping every 5 seconds
  }

  private stopPing() {
    if (this.pingInterval) {
      clearInterval(this.pingInterval);
      this.pingInterval = null;
    }
  }

  sendFrame(imageData: string) {
    try {
      if (!imageData || imageData === 'data:,') {
        console.error('Invalid image data');
        return;
      }
      const message = { type: 'frame', image: imageData };
      this.socket$?.next(message);
      console.log('Sending frame to server:', JSON.stringify(message).substring(0, 50) + '...');
    } catch (error) {
      console.error('Error sending frame:', error);
    }
  }

  getProcessedData(): Observable<any> {
    if (!this.socket$) {
      return new Observable();
    }
    return this.socket$.asObservable().pipe(
      tap(data => {
        console.log('Received data from WebSocket:', data);
        if (data && data.contour) {
          console.log('Contour data received:', data.contour);
        } else {
          console.log('No contour data received');
        }
      }),
      retryWhen(errors =>
        errors.pipe(
          tap(error => console.error('WebSocket error:', error)),
          delayWhen(() => timer(1000)),
          tap(() => {
            console.log('Retrying WebSocket connection');
            this.initializeWebSocket();
          })
        )
      )
    );
  }

  isConnected(): boolean {
    if (!this.socket$) {
      return false;
    }
    return this.socket$ && !this.socket$.closed;
  }

  reconnect() {
    if (this.socket$?.closed) {
      this.initializeWebSocket();
      console.log('WebSocket reconnected');
    } else {
      console.log('WebSocket is already connected');
    }
  }
}