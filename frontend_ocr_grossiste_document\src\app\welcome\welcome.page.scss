@import url("https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap");

* {
  font-family: "Inter", sans-serif;
  font-optical-sizing: auto;
}

ion-content::part(scroll) {
  // overflow-y: hidden !important;
  // --overflow: hidden !important;

  overflow-y: auto;
}

ion-button::part(native) {
  --padding-top : 20px !important;
  --padding-bottom : 20px !important;
}

.platform-selection {
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 20px 20px 40px 20px ;
  gap: 20px;
}

.platform-button {
  flex: 1;
  height: 80px;
  --border-radius: 12px;
  --box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin: 0;
}

.platform-button::part(native) {
  padding: 0;
  background: white;
  border: 2px solid #e0e0e0;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.platform-button:hover::part(native) {
  border-color: #007bff;
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 123, 255, 0.2);
}

.platform-logo {
  width: 100%;
  height: 60px;
  object-fit: contain;
  padding: 10px;
}
.welcome-wrapper {
  background: url("/assets/bg-welcome.png") no-repeat center center fixed;
  background-size: cover;
  height: 100%;
}

ion-footer {
  position: relative;
  // bottom: -30px;
  width: 100%;
  padding: 10px;
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;
}

ion-toolbar {
  --background: transparent;
  --ion-color-primary: #2f4fcd;
}

ion-button {
  margin: 10px;
}

ion-button.welcome-button {
  --background: #1f41bb;
  --background-activated: #1f41bb;
  --border-radius: 8px;
  width: 65%;
  text-align: center;
  box-shadow: 0px 16px 20px rgba(203, 214, 255, 1); /* Add shadow */
  color: #fff;
  font-size: 20px;
  font-weight: bold;
}

ion-row {
  height: 100%;
  height: 85vh;
}

::ng-deep ion-row ion-col {
  //   padding-top: 0 !important;
  padding-bottom: 0 !important;
}

::ng-deep ion-col {
  display: flex !important;
  flex-direction: column;
  justify-content: space-evenly;
  align-items: center;
}

::ng-deep img {
  width: auto;
  max-width: 100%;
  height: auto;
  max-height: 100%;
}

::ng-deep .content-slide {
  text-align: left;
  padding: 0 20px;
}

::ng-deep .content-slide h2 {
  font-family: "Poppins", "Inter", sans-serif !important;
  font-weight: bold;
  font-style: normal;
  font-size: 30px;
  color: #1f41bb;
}

::ng-deep .slide-content h3 {
  font-family: "Poppins", "Inter", sans-serif !important;
  font-weight: bold;
  font-style: normal;
  font-size: 24px;
  color: #1f41bb;
  text-align: center;
  margin-bottom: 0;
}

::ng-deep .content-slide p {
  padding-right: 20px;
  margin-top: 40px;
  letter-spacing: 1.1px;
}
