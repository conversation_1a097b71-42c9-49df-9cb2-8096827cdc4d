// scanner-modal.component.scss
ion-card {
  margin: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 2px solid transparent;

  &.selected {
    border-color: var(--ion-color-primary);
    background-color: rgba(var(--ion-color-primary-rgb), 0.1);
    
    ion-checkbox {
      --border-color-checked: var(--ion-color-primary);
      --background-checked: var(--ion-color-primary);
    }
  }

  img {
    width: 100%;
    height: 200px;
    object-fit: cover;
  }
}

ion-checkbox {
  margin-right: 8px;
}

ion-item {
  --padding-start: 0;
  --inner-padding-end: 0;
}
