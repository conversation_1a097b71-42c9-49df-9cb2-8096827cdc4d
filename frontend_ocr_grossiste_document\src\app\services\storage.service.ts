import { Injectable } from '@angular/core';
import { Storage } from '@ionic/storage-angular'; // For Ionic Storage

@Injectable({
  providedIn: 'root',
})
export class StorageService {
  constructor(private storage: Storage) {}

  async init() {
    await this.storage.create();
  }

  async set(key: string, value: any): Promise<void> {
    await this.storage.set(key, value);
  }
  
  async get(key: string): Promise<any> {
    const value = await this.storage.get(key);
    return value;
  }

  async clear(): Promise<void> {
    console.log('Clearing all storage...');
    await this.storage.clear(); // Clear all stored data
  }
}