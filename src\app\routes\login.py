from fastapi import APIRouter, HTTPException, Request
import logging
from src.app.utils.models import LoginResponse, LoginRequest, TenantLoginRequest, TenantLoginResponse, PharmalienLoginRequest, PharmalienLoginResponse
import httpx
from src.app.config import WINPLUS_AUTH_TENANT, WINPLUS_AUTH_USER, ACCESS_TOKEN_EXPIRE_MINUTES, PHARMALIEN_AUTH_URL
from src.app.utils.jwt_utils import create_access_token
from datetime import timedelta
from src.app.config import ENVIRONMENT

router = APIRouter()

logger = logging.getLogger(__name__)


async def get_tenant_token(username: str, password: str):
    async with httpx.AsyncClient() as client:
        try:
            response = await client.post(
                WINPLUS_AUTH_TENANT,
                json={"username": username, "password": password}
            )
            response_data = response.json()

            if response.status_code == 200:
                return response_data
            else:
                error_message = response_data.get("message", "Tenant login failed")  # Extract backend message
                logging.error(f"Tenant login failed: {response_data}")
                raise HTTPException(status_code=response.status_code, detail=error_message)
        except Exception as e:
            logging.error(f"Error during tenant login: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


async def get_user_token(tenant_token: str, username: str, password: str):
    headers = {
        "AuthorizationTenant": f"BearerTenant {tenant_token}",
        "token-ignore-plv": "9a3c6342-1345-4d2c-b767-918427f5230a"
    }
    async with httpx.AsyncClient() as client:
        try:
            response = await client.post(
                WINPLUS_AUTH_USER,
                headers=headers,
                json={"username": username, "password": password}
            )
            response_data = response.json()

            if response.status_code == 200:
                return response_data
            else:
                error_message = response_data.get("message", "User login failed")  # Extract backend message
                logging.error(f"User login failed: {response_data}")
                raise HTTPException(status_code=response.status_code, detail=error_message)
        except Exception as e:
            logging.error(f"Error during user login: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.post("/tenant_login", response_model=TenantLoginResponse)
async def tenant_login(request: TenantLoginRequest):
    try:

        logger.info(f"Login Tenant request received: {request}")
        tenant_response = await get_tenant_token(request.username, request.password)

        # check the array tenant_response["listEnabledFeatures"] is contain 'OCR_BL'
        is_authorized_in_ocr_feature = False
        enabled_features = tenant_response.get("listEnabledFeatures", [])
        for feature in enabled_features:
            if feature.get("label") == "OCR_BL":
                is_authorized_in_ocr_feature = True
                break

        if not is_authorized_in_ocr_feature:
            raise HTTPException(status_code=403, detail="Vous n'avez pas les droits pour utiliser cette fonctionnalité.")

        return TenantLoginResponse(
            accessToken=tenant_response["accessToken"],
            message="Tenant login successful",
        )
    except HTTPException as e:
        logger.error(f"HTTPException in tenant_login: {str(e)}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error in tenant_login: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


async def get_pharmalien_token(username: str, password: str):
    async with httpx.AsyncClient() as client:
        try:
            response = await client.post(
                PHARMALIEN_AUTH_URL,
                json={"username": username, "password": password}
            )
            response_data = response.json()

            if response.status_code == 200:
                return response_data
            else:
                error_message = response_data.get("message", "Pharmalien login failed")
                logging.error(f"Pharmalien login failed: {response_data}")
                raise HTTPException(status_code=response.status_code, detail=error_message)
        except Exception as e:
            logging.error(f"Error during Pharmalien login: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.post("/pharmalien_login", response_model=PharmalienLoginResponse)
async def pharmalien_login(request: PharmalienLoginRequest):
    try:
        logger.info(f"Pharmalien login request received: {request}")
        pharmalien_response = await get_pharmalien_token(request.username, request.password)

        # Create local JWT token
        ACCESS_TOKEN_EXPIRE_MINUTES_int = int(ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES_int)
        access_token = create_access_token(data={"sub": request.username}, expires_delta=access_token_expires)

        logger.info(f"Pharmalien login successful for user: {request.username}")
        return PharmalienLoginResponse(
            user_data=pharmalien_response,
            local_token=access_token,
            message="Pharmalien login successful"
        )
    except HTTPException as e:
        logger.error(f"HTTPException in pharmalien_login: {str(e)}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error in pharmalien_login: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/login", response_model=LoginResponse)
async def login(request: LoginRequest, fastapi_request: Request):
    try:
        logger.info(f"Login request received: {request}")
        logger.info(f"Headers: {fastapi_request.headers}")

        if not request.tenant_token:
            raise HTTPException(status_code=400, detail="Tenant token is missing")

        user_response = await get_user_token(request.tenant_token, request.username, request.password)

        # Create local JWT token
        ACCESS_TOKEN_EXPIRE_MINUTES_int = int(ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES_int)
        access_token = create_access_token(data={"sub": request.username}, expires_delta=access_token_expires)

        logger.info(f"Login successful for user: {request.username}")
        return LoginResponse(
            tenant_data={"accessToken": request.tenant_token},
            user_data=user_response,
            local_token=access_token,
            message="Login successful"
        )
    except HTTPException as e:
        logger.error(f"HTTPException in login: {str(e)}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error in login: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")
