# Docker Compose for Development Environment
# This sets up a local PostgreSQL database for development and testing

version: '3.8'

services:
  # PostgreSQL Database for Development
  postgres:
    image: postgres:15-alpine
    container_name: ocr_postgres_dev
    environment:
      POSTGRES_DB: ocr_document_grossiste
      POSTGRES_USER: ocr_user
      POSTGRES_PASSWORD: ocr_password_dev
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init_db.sql:/docker-entrypoint-initdb.d/init_db.sql
    networks:
      - ocr_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ocr_user -d ocr_document_grossiste"]
      interval: 30s
      timeout: 10s
      retries: 3

  # pgAdmin for Database Management (Optional)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: ocr_pgadmin_dev
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin123
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "8080:80"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    networks:
      - ocr_network
    depends_on:
      - postgres
    restart: unless-stopped

  # Redis for Caching (Optional - for future use)
  redis:
    image: redis:7-alpine
    container_name: ocr_redis_dev
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - ocr_network
    restart: unless-stopped
    command: redis-server --appendonly yes

volumes:
  postgres_data:
    driver: local
  pgadmin_data:
    driver: local
  redis_data:
    driver: local

networks:
  ocr_network:
    driver: bridge
