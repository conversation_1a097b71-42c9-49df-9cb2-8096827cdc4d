@import url("https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap");

* {
  font-family: "Inter", sans-serif;
  font-optical-sizing: auto;
}

ion-content::part(scroll) {
  // overflow-y: hidden !important;
  // --overflow: hidden !important;
  scrollbar-width: none !important; 
  -ms-overflow-style: none !important;  
}
ion-content {
  --offset-top : 0px !important;
}

ion-header {
  height: 70px;
  --border: 0;
  display: flex;
  align-items: center;
}

ion-header ion-toolbar {
  // height: 100%;
  --border: 0;
  --border-width: 0;
  display: flex;
  justify-content: center;
  align-items: flex-end;
  flex-direction: row;
}

ion-header ion-toolbar ion-title {
  font-size: 26px;
  font-weight: 700;
  color: #2f4fcd;
  text-align: left;
  width: 100%;
  padding-left: 2rem;
}

::ng-deep ion-header ion-toolbar app-custom-icon .custom-icon{
  width: 40px !important;
  height: 40px !important;
  margin-right: 5px !important;
}

.doc-list-wrapper {
  background: url("/assets/bg-scan-bl.png") no-repeat center center fixed;
  background-size: cover;
  height: 80%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin-top: 2rem;
}

::ng-deep .scan-bl-wrapper .file-import-icon img {
  width: 150px !important;
  height: 150px !important;
}

::ng-deep .scan-bl-wrapper .arrow-bottom-icon {
  // margin-top: 50px;
  img {
    width: 100px !important;
    height: 100px !important;
  }
}

.scan-bl-wrapper .content {
  padding: 10px 50px 0 50px;
}

.scan-bl-wrapper .content h2 {
  color: #9a9a9a;
  font-size: 22px;
  font-weight: 700;
}

.scan-bl-wrapper .content p {
  color: #9a9a9a;
  font-size: 12px;
  text-align: justify;
  padding: 5px 10px;
}

.scan-bl-content {
  --background: #ffffff;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.document-icon {
  font-size: 100px;
  color: #c4c4c4;
}

ion-label{
  margin-bottom: 20px;
}

ion-label h3{
  margin-top: 20px;
  font-weight: bold;
  color: #404040;
  font-size: 14px;
}

ion-label p{
  margin-top: 20px;
  color: #888888;
  font-size: 14px;
  margin-top: 10px;
  margin-bottom: 10px;
  opacity: 0.5;
}

h2 {
  color: #555555;
  font-size: 18px;
  margin-top: 20px;
}

.arrow-icon {
  font-size: 30px;
  color: #3b82f6; // Adjust the color as needed
}

ion-footer {
  background-color: #e5e7eb; // Adjust the background color as needed
}

.camera-button {
  --background: #3b82f6; // Adjust the color as needed
  --background-activated: #2563eb; // Adjust the color as needed
  border-radius: 50%;
  width: 60px;
  height: 60px;
  margin-top: -30px; // Position the button above the toolbar
}

ion-toolbar {
  --background: transparent;
  --ion-color-primary: #3b82f6; // Adjust the color as needed
}

ion-button {
  --color: #3b82f6; // Adjust the color as needed
}

ion-button[slot="icon-only"] {
  --color: #3b82f6; // Adjust the color as needed
}

ion-icon {
  font-size: 24px;
}

ion-footer {
  position: relative;
  background-color: #dddbff;
  height: 110px;
  // border-top: 1px solid #2563eb;
  width: 100%;

  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: row;
}

ion-footer ion-toolbar {
  --border-width: 0;
}

ion-footer ion-toolbar ion-buttons,
ion-footer ion-toolbar ion-buttons ion-button {
  display: flex;
  justify-content: space-evenly;
  align-items: center;
  flex-direction: row;
}

::ng-deep ion-button.menu-button app-custom-icon img {
  width: 30px !important;
  height: 30px !important;
  color: #000;
}

::ng-deep .menu-button.active app-custom-icon img {
  color: #2f4fcd;
}

::ng-deep .menu-button-middle {
  background-color: #2f4fcd;
  padding: 2px 12px;
  border-radius: 14px;
  width: 160px;
  height: 60px;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: row;
  margin-bottom: 15px;
  app-custom-icon img {
    width: 35px !important;
    height: 35px !important;
    color: #fff;
  }
  span{
    color: #fff;
    font-weight: 500;
    font-size: 16px;
    padding-left: 10px;
  }
}

.document-card {
  margin: 14px;
  border-radius: 16px;
  // box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  box-shadow: none;
  border: 1px solid #e5ebfd;
  border-width: 0.55px;
  width: auto;
}

.doc-list{
  overflow-y: auto !important;
  --overflow: auto !important;
}

ion-item {
  // --padding-start: 0;
}

ion-thumbnail {
  --border-radius: 2px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  height: 80px;
}

.page-count {
  font-size: 14px;
  color: #888;
  margin-left: auto;
  padding-right: 8px;
}

ion-item::part(native){
  border-style: auto !important;
  --border-style : auto !important;
}

.section-title {
  margin-top: 20px;
  margin-bottom: 0;
  color: #4b4b4b;
  font-size: 16px;
  margin-left: 16px;
  opacity: 0.5;
}

swiper-container ion-col{
  padding: 0;
  margin: 0;
  border-radius: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  // align-items: flex-start;
  justify-content: center;
  margin-top: 10px;
}

.card-doc{
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  width: 60%;
  height: 100%;
  box-shadow: none;
  margin: 0;
  padding: 0;
  border: 2px solid #e5ebfd;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.card-doc img {
  width: 100%;
  height: 150px;
  object-fit: cover;
}

.card-doc ion-card-header{
  width: 100%;
  padding: 10px 20px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;

  ion-card-subtitle{
    width: 100%;
    font-size: 14px;
    text-align: left;
    font-weight: 600;
    color: #202020;
  }
}

.content-global-card{
  width: 100%;
  background-color: #fff;
  border-top: 1px solid #e5ebfd;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.card-doc ion-card-content{
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding-bottom: 10px;

  span:nth-child(1n){
    color: #4b4b4b;
    font-size: 14px;
    opacity: 0.5;
  }
  span:nth-child(2n){
    color: #070707;
    font-size: 14px;
    font-weight: 500;
    opacity: 1;
  }
}

ion-item-option{
  border-radius: 12px;
  width: 90px;
  margin-left: 5px;
}
.content-item-option{
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  ion-icon{
    margin-bottom: 6px;
    font-size: 26px;
    font-weight: bold;
  }
  span{
    color: #fff;
    font-size: 13px;
    font-weight: 500;
  }
}

.renameCard{
  background-color: #cccbcb;
}

.confirmDeleteCard{
  background-color: #FFA5A5;
}


/***  Swiper  ***/

.swiper {
  width: 100%;
  // height: 45%;
}

// ::ng-deep swiper-slide{
//   width: 100% !important;
// }
::ng-deep swiper-slide .swiper-slide-shadow{
  background: none !important;
  --background: none !important;
}

.card-doc{
  left: 0px !important;
}


// / *** Styles alert dialog ***/
::ng-deep .custom-alert-button, custom-alert-button-rename-doc {
  display: inline-block;
  text-align: center;
  font-size: 14px !important;
  font-weight: bold;
}

::ng-deep .custom-alert-button.cancel, ::ng-deep .custom-alert-button-rename-doc.cancel {
  color: #2563eb;
  width: 48%;
}
::ng-deep .custom-alert-button-rename-doc.cancel {
  font-size: 16px;
}
::ng-deep .custom-alert-button.danger {
  color: red;
  width: 50%;
}
::ng-deep .custom-alert-button-rename-doc.rename {
  font-size: 16px;
  color: #535353;
}



/**
* Loading alert START
*/

::ng-deep .loading:not(.alert-progress) {
  opacity: 0.5;
  pointer-events: none; /* Makes the page unclickable */
  --background: rgba(0, 0, 0, 0.1);
}

// ion-modal
::ng-deep ion-modal {
  height: 23%;
  width: 90%;
  position: absolute;
  top: 35%;
  left: 5%;
  --background:none;
  --backdrop-opacity: var(--ion-backdrop-opacity, 0);
}

::ng-deep .alert-progress{
  position: absolute;
  width: 100%;
  top: 40%;
  // display: flex;
  justify-content: center;
  align-items: center;
}

::ng-deep .alert-progress app-custom-alert{
  width: 90%;
}

::ng-deep .alert-progress{
  display: none;
}
::ng-deep .alert-progress.loading{
  display: flex;
}


/**
* Loading alert  END
*/