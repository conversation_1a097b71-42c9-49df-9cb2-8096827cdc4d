pipeline {
    agent any
    parameters {
        string(name: 'VERSION', defaultValue: '1', description: 'Version for the new Docker image')
    }
    environment {
        IMAGE_NAME = 'windoc_base'
    }
    stages {
        stage('Clean Up Old Images of Windoc API') {
            steps {
                script {
                    // Remove images that start with 'winproduits-delta:V'
                    sh """
                        docker images -q '${IMAGE_NAME}:v*' | xargs -r docker rmi -f
                    """
                }
            }
        }
        stage('Build The Base Image of Windoc API') {
            steps {
                script {
                    sh "docker build -t ${IMAGE_NAME}:v${params.VERSION} -f Dockerfile.base ."
                }
            }
        }
    }
}