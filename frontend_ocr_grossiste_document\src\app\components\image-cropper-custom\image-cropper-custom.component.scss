.cropper-container {
  position: relative;
  overflow: visible; /* Changed from hidden to allow handles to extend */
  background: #fff;
  display: flex; /* Optional: centers the SVG if parent is larger */
  justify-content: center;
  align-items: center;
}

  .cropper-svg {
    width: 100%;
    height: 100%;
    touch-action: none;
    -webkit-user-select: none;
    user-select: none;
    display: block;
    path{
        fill: rgba(0, 0, 0, 0);
        d: path("M 0 0 H 3060 V 4080 H 0 Z M 32.6 922.1 L 3374.65 826.047 L 2717.3 3631.2 L -49878 3533.3 Z");
  
    }
  }

//   .handle-point {
//     fill: white;
//     stroke: #29BF9C;
//     stroke-width: 15;
//     cursor: move;
//     z-index: 1000;
//     touch-action: none;
//     -webkit-tap-highlight-color: transparent;
//     pointer-events: all;
//   }

.handle-point {
    fill: white;
    stroke: #29BF9C;
    stroke-width: 15;
    pointer-events: none;
    transition: transform 0.1s, stroke-width 0.1s;
    
    &.touch-active {
      fill: #29BF9C;
      transform: scale(1);
      stroke-width: 15;
    }
  }


  .crop-outline {
    fill: none;
    stroke: #29BF9C;
    stroke-width: 15;
    cursor: move;
    touch-action: none;
    pointer-events: all;
  }

  .handle-point.active {
    fill: #29BF9C;
  }

  .overlay {
    pointer-events: none;
  }
  
  .handle-point.touch-active {
    transform: scale(1);
    transition: transform 0.1s;
  }


  .handle-touch-area {
    fill: transparent;
    stroke: none;
    cursor: pointer;
    touch-action: none;
    pointer-events: all;
    -webkit-tap-highlight-color: transparent;
  }
  
  .handle-point {
    fill: white;
    stroke: #29BF9C;
    stroke-width: 15;
    pointer-events: none;
    transition: transform 0.1s ease-out, stroke-width 0.1s ease-out;
    
    &.touch-active {
      transform: scale(1);
      stroke-width: 15;
      fill: #29BF9C;
    }
  }
  
  // Add touch feedback styles
  .handle-touch-area:active + .handle-point {
    transform: scale(1);
    fill: #29BF9C;
  }
  
  // Prevent text selection during drag
  .cropper-svg {
    width: 100%;
    height: 100%;
    touch-action: none;
    -webkit-user-select: none;
    user-select: none;
    -webkit-touch-callout: none;
    -webkit-tap-highlight-color: transparent;
  }
  

  .magnifier-crop-outline {
    fill: none;
    stroke: #29BF9C;
    stroke-width: 30; // Increased width for better visibility in magnifier
    pointer-events: none;
  }
  
  .magnifier-handle-point {
    fill: white;
    stroke: #29BF9C;
    stroke-width: 160; // Increased width for better visibility in magnifier
    pointer-events: none;
    
    &.touch-active {
      fill: #29BF9C;
    }
  }


  .magnifier-intersection-line {
    stroke: #29BF9C;
    stroke-width: 8;
    stroke-dasharray: 20, 20; // Creates a dashed line
    pointer-events: none;
  }
  
  .magnifier-center-point {
    fill: white;
    stroke: #29BF9C;
    stroke-width: 8;
    pointer-events: none;
  }
  
  // Update existing magnifier styles
  .magnifier-background {
    fill: white;
    opacity: 0.9; // Slightly more opaque for better contrast
  }
  
  .magnifier-border {
    fill: none;
    stroke: #29BF9C;
    stroke-width: 15;
  }
  
  .magnifier {
    pointer-events: none;
  }



  .crop-area-container {
    pointer-events: none;
  }
  
  .handle-container {
    pointer-events: all;
  }
  
  .handle-point-background {
    fill: white;
    stroke: none;
    pointer-events: none;
  }
  
  .handle-point {
    fill: white;
    stroke: #29BF9C;
    stroke-width: 10;
    pointer-events: none;
    transition: transform 0.1s ease-out, stroke-width 0.1s ease-out;
    
    &.touch-active {
      transform: scale(1);
      stroke-width: 15;
      fill: #29BF9C;
    }
  }
  
  .handle-touch-area {
    fill: transparent;
    stroke: none;
    cursor: pointer;
    touch-action: none;
    pointer-events: all;
    -webkit-tap-highlight-color: transparent;
  }
  
  .crop-outline {
    fill: none;
    stroke: #29BF9C;
    stroke-width: 15;
    cursor: move;
    touch-action: none;
    pointer-events: all;
  }
  
  .overlay {
    pointer-events: none;
    fill: rgba(0, 0, 0, 0.5);
  }


  
  
  .midpoint-handle {
    fill: white;
    stroke: #29BF9C;
    stroke-width: 15;
    cursor: move;
    pointer-events: all;
    transition: all 0.2s ease;
    
    &.touch-active {
      fill: #29BF9C;
      stroke: white;
      transform: scale(1);
    }
  }
  