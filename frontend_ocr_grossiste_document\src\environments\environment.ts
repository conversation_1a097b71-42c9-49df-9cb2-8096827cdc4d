// This file can be replaced during build by using the `fileReplacements` array.
// `ng build` replaces `environment.ts` with `environment.prod.ts`.
// The list of file replacements can be found in `angular.json`.

export const environment = {
  production: true,
  platform: 'mobile', // 'web' or 'mobile'


  // webSocketUrl: 'ws://192.168.101.176:8085/ws',
  // apiUrl: 'http://192.168.101.176:8085',  // Adjust to your FastAPI local URL
  // webSockeRealTimetUrl: 'ws://192.168.101.176:8085/websocket/realtime_processing',

  // webSocketUrl: 'ws://192.168.101.176:8088/ws',
  // apiUrl: 'http://192.168.101.176:8088',  // Adjust to your FastAPI local URL
  // webSockeRealTimetUrl: 'ws://192.168.101.176:8088/websocket/realtime_processing'

  webSocketUrl: 'wss://winproduit.sophatel.com:8001/ws',
  apiUrl: 'https://winproduit.sophatel.com:8001',  // Adjust to your FastAPI local URL
  webSockeRealTimetUrl: 'wss://winproduit.sophatel.com:8001/websocket/realtime_processing'

  // webSocketUrl: 'wss://windoc-api.sophatel.com/ws',
  // apiUrl: 'https://windoc-api.sophatel.com',  // Adjust to your FastAPI local URL
  // webSockeRealTimetUrl: 'wss://windoc-api.sophatel.com/websocket/realtime_processing'

  // webSocketUrl: 'wss://winproduit.sophatel.com:8000/ws',
  // apiUrl: 'https://winproduit.sophatel.com:8000',  // Adjust to your FastAPI local URL
  // webSockeRealTimetUrl: 'wss://winproduit.sophatel.com:8000/websocket/realtime_processing'


  // webSocketUrl: 'wss://ocr-api.abdohero.com/ws', // Secure WebSocket URL
  // apiUrl: 'https://ocr-api.abdohero.com',  // Adjust to your FastAPI server URL
  // webSockeRealTimetUrl: 'wss://ocr-api.abdohero.com/websocket/realtime_processing'
};

/*
 * For easier debugging in development mode, you can import the following file
 * to ignore zone related error stack frames such as `zone.run`, `zoneDelegate.invokeTask`.
 *
 * This import should be commented out in production mode because it will have a negative impact
 * on performance if an error is thrown.
 */
// import 'zone.js/plugins/zone-error';  // Included with Angular CLI.