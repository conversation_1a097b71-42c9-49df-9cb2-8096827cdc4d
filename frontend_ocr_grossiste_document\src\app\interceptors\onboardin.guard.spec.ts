import { TestBed } from '@angular/core/testing';
import { CanActivateFn } from '@angular/router';

import { onboardinGuard } from './onboardin.guard';

describe('onboardinGuard', () => {
  const executeGuard: CanActivateFn = (...guardParameters) => 
      TestBed.runInInjectionContext(() => onboardinGuard(...guardParameters));

  beforeEach(() => {
    TestBed.configureTestingModule({});
  });

  it('should be created', () => {
    expect(executeGuard).toBeTruthy();
  });
});
