#!/usr/bin/env python3
"""
Simple PostgreSQL Database Initialization Script

This is a simplified version that focuses on just creating the tables.
"""

import sys
import os
import logging
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def main():
    """Simple initialization function."""
    logger.info("🚀 Starting simple PostgreSQL database initialization...")
    
    try:
        # Import after adding to path
        from src.app.database.models import Base
        from sqlalchemy import create_engine, text
        from src.app.config import DATABASE_URL
        
        logger.info(f"Database URL: {DATABASE_URL.split('@')[0]}@***")

        # Create engine
        engine = create_engine(DATABASE_URL)
        
        # Test connection
        logger.info("Testing connection...")
        with engine.connect() as conn:
            result = conn.execute(text("SELECT version()"))
            version = result.fetchone()[0]
            logger.info(f"✅ Connected to: {version}")
        
        # Create tables
        logger.info("Creating tables...")
        Base.metadata.create_all(bind=engine)
        logger.info("✅ Tables created successfully")
        
        # Verify tables
        logger.info("Verifying tables...")
        with engine.connect() as conn:
            result = conn.execute(text("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public'
                ORDER BY table_name
            """))
            tables = [row[0] for row in result]
            logger.info(f"✅ Created tables: {', '.join(tables)}")
        
        logger.info("🎉 PostgreSQL database initialization completed successfully!")
        
    except Exception as e:
        logger.error(f"❌ Initialization failed: {e}")
        logger.error("💡 Try running: python scripts/diagnose.py")
        sys.exit(1)

if __name__ == "__main__":
    main()
