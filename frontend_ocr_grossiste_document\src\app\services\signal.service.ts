import { Injectable, signal, computed } from '@angular/core';
import { ImageData } from 'src/models/ImageData';
import { ProcessDocData } from 'src/models/ProcessDocData';
import { TransformedDocData } from 'src/models/TransformedDocData';

@Injectable({
  providedIn: 'root',
})
export class SignalService {
  private data = signal<ProcessDocData[]>([]);
  private imageData = signal<ProcessDocData[]>([]);
  private transformedData = signal<TransformedDocData[]>([]);

  // Create a computed signal for the empty state
  private readonly isEmpty = computed(() => this.data().length === 0);

  // Signal of ProcessDocData ------------------------------------------------------------
  
  setData(newData: ProcessDocData) {
    newData.title = `Scan ${this.formatDate()}`;

    this.data.update((data) => {
      const index = data.length;
      newData.page_index = index + 1;
      const formattedDate_ToDay = this.getDateAsToday();
      data.push({ ...newData, date: formattedDate_ToDay });
      
      // Handle forceSupplierGlobal here
      if (data.length === 0) {
        localStorage.removeItem('forceSupplierGlobal');
      }
      
      return data;
    });
  }

  getData(): ProcessDocData[] {
    const currentData = this.data();
    // Handle forceSupplierGlobal automatically when data is empty
    if (currentData.length === 0) {
      localStorage.removeItem('forceSupplierGlobal');
    }
    return currentData;
  }


  removeLastIndex() {
    this.data.update((data) => {
      // 1. Store the original length for comparison
      const originalLength = data.length;
  
      // 2. Remove the item
      data.pop();
  
      // 3. Check if the array is now empty after removal
      if (originalLength === 1) {
        localStorage.removeItem('forceSupplierGlobal');
      }
      return data;
    });
  }

  
  removeData(index: number) {
    this.data.update((data) => {
      // 1. Store the original length for comparison
      const originalLength = data.length;
  
      // 2. Remove the item
      data.splice(index, 1);
  
      // 3. Check if the array is now empty after removal
      if (originalLength === 1) {
        localStorage.removeItem('forceSupplierGlobal');
      }
  
      // 4. Reassign page_index for remaining items
      data.forEach((item, i) => (item.page_index = i + 1));
  
      return data;
    });
  }

  checkDataisEmpty() {
    this.data.update((data) => {
      if (data.length === 0) {
        localStorage.removeItem('forceSupplierGlobal');
        return data;
      }
      return data;
    });
  }

  removeAllData() {
    this.data.update((data) => {
      data = [];
      return data;
    });
    localStorage.removeItem('forceSupplierGlobal');
  }

  getDateAsDDD_MMM() {
    // Get the date of the newData or use the current date if not provided
    const newDataDate = new Date();

    // Format the date as "ddd/MMM"
    const daysOfWeek = ['Dim', 'Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam'];
    const monthsOfYear = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

    const formattedDate = `${daysOfWeek[newDataDate.getDay()]}/${monthsOfYear[newDataDate.getMonth()]}`;
    return formattedDate
  }

  getDateAsToday() {
    // Get the current date
    const currentDate = new Date();
    // Get the date of the newData or use the current date if not provided
    const newDataDate = currentDate;

    // Calculate the difference in days between the current date and newData's date
    const diffInDays = Math.floor((currentDate.getTime() - newDataDate.getTime()) / (1000 * 60 * 60 * 24));

    let formattedDate;
    if (diffInDays === 0) {
      // If the difference is 0, it means it's today
      formattedDate = "Aujourd'hui";
    } else if (diffInDays === 1) {
      // If the difference is 1, it means it's yesterday
      formattedDate = "Hier";
    } else {
      // For other dates, you can use your original date formatting logic
      // Format data dd/mm
      formattedDate = `${newDataDate.getDate()}/${newDataDate.getMonth() + 1}`;
    }

    return formattedDate;
  }

  formatDate(): string {
    const date = new Date();
    const day = this.padNumber(date.getDate());
    const month = this.padNumber(date.getMonth() + 1); // Months are zero-based
    const year = date.getFullYear();
    const hours = this.padNumber(date.getHours());
    const minutes = this.padNumber(date.getMinutes());
    const seconds = this.padNumber(date.getSeconds());
    return `${day}:${month}:${year} ${hours}:${minutes}:${seconds}`;
  }

  padNumber(num: number): string {
    return num.toString().padStart(2, '0');
  }

  get firstEntry() {
    return this.data().length === 1;
  }

  private extractTitleFromUrl(url: string): string {
    if (!url) return '';
    const parts = url.split('/');
    return parts[parts.length - 1].split('.')[0]; // Extracts the file name without extension
  }

  getFormattedDate(dateStr: string): string {
    const currentDate = new Date();
    const date = new Date(dateStr);
    const diffInDays = Math.floor((currentDate.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));

    if (diffInDays === 0) {
      return "Aujourd'hui";
    } else if (diffInDays === 1) {
      return 'Hier';
    } else {
      return `${date.getDate()}/${date.getMonth() + 1}`;
    }
  }

  // Signal of ImageData ------------------------------------------------------------
  
  setImageData(newData: ImageData){
    this.imageData.update((imageData) => {
      const index = imageData.length;
      newData.page_index = index + 1;
      return imageData;
    });
  }

  getImageData() {
    return this.imageData();
  }

  // Signal of TransformedDocData ------------------------------------------------------------

  transformAndSetData(data: any) {
    this.transformedData.set(data)
  }

  getTransformedData() {
    return this.transformedData();
  }
}
