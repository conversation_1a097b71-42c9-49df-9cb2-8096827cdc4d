import{r as t,c as i,h as s,F as e,H as h}from"./p-bc4d54f8.js";const n=":host{--active-color:orange;--inactive-color:orange;--active-stroke:4;--inactive-stroke:4;--main-background:transparent;display:block;position:relative;top:0;left:0;width:100%;height:100%}*{user-select:none;-webkit-user-select:none;-moz-user-select:none}.container{display:flex;justify-content:center;background:var(--main-background);overflow:hidden}.absolute{position:absolute;top:0;left:0;width:100%;height:100%}.cropper-controls{stroke:var(--active-color)}.footer{position:absolute;left:0;bottom:0;height:100px;width:100%;pointer-events:none}.items{box-sizing:border-box;display:flex;width:100%;height:100%;align-items:center;justify-content:center;padding:2.0em}.items .item{flex:1;text-align:center}.items .item:first-child{text-align:left}.items .item:last-child{text-align:right}.accept-use img{width:2.5em;height:2.5em;pointer-events:all;cursor:pointer}.accept-cancel img{width:2.5em;height:2.5em;pointer-events:all;cursor:pointer}.cropper-svg{align-self:center;touch-action:none;cursor:grab}.cropper-svg polygon{cursor:move}.cropper-svg rect{cursor:grab}.hidden-canvas{display:none}.cropper-svg .inactive-selection{stroke:var(--inactive-color);cursor:pointer}.dashed{stroke-dasharray:10,10}.magnifier{position:absolute;width:100px;height:100px;left:0;top:0;border:1px solid #00bceb;border-radius:50%;overflow:hidden;display:none;pointer-events:none;background-size:100%;background-repeat:no-repeat}";const o=n;const r=class{constructor(s){t(this,s);this.confirmed=i(this,"confirmed",7);this.canceled=i(this,"canceled",7);this.selectionClicked=i(this,"selectionClicked",7);this.handlers=[0,1,2,3,4,5,6,7];this.polygonMouseDown=false;this.polygonMouseDownPoint={x:0,y:0};this.previousDistance=undefined;this.svgMouseDownPoint=undefined;this.handlerMouseDownPoint={x:0,y:0};this.originalPoints=undefined;this.usingTouchEvent=false;this.usingQuad=false;this.img=undefined;this.rect=undefined;this.quad=undefined;this.license=undefined;this.hidefooter=undefined;this.handlersize=undefined;this.inactiveSelections=undefined;this.viewBox="0 0 1280 720";this.activeStroke=2;this.rotation=0;this.inActiveStroke=4;this.selectedHandlerIndex=-1;this.points=undefined;this.offsetX=0;this.offsetY=0;this.scale=1}componentDidLoad(){this.containerElement.addEventListener("touchmove",(t=>{this.onContainerTouchMove(t)}));this.containerElement.addEventListener("touchend",(()=>{this.previousDistance=undefined;this.hideMagnifier()}))}watchImgPropHandler(t){if(t){console.log("watchImgPropHandler triggered with newValue:",t);this.resetStates();this.viewBox=`0 0 ${t.naturalWidth} ${t.naturalHeight}`;console.log("viewBox set to:",this.viewBox);if(this.root){const t=parseInt(this.root.style.getPropertyValue("--inactive-stroke"));const i=parseInt(this.root.style.getPropertyValue("--active-stroke"));console.log("inActiveStroke:",t,"activeStroke:",i);if(t){this.inActiveStroke=t}if(i){this.activeStroke=i}}}}watchRectPropHandler(t){if(t){this.usingQuad=false;let i=this.getPointsFromRect(t);if(this.img){this.restrainPointsInBounds(i,this.img.naturalWidth,this.img.naturalHeight)}this.points=i}}getPointsFromRect(t){const i={x:t.x,y:t.y};const s={x:t.x+t.width,y:t.y};const e={x:t.x+t.width,y:t.y+t.height};const h={x:t.x,y:t.y+t.height};return[i,s,e,h]}watchQuadPropHandler(t){if(t){this.usingQuad=true;let i=t.points;if(this.img){this.restrainPointsInBounds(i,this.img.naturalWidth,this.img.naturalHeight)}this.points=t.points}}onCanceled(){if(this.canceled){this.canceled.emit()}}onConfirmed(){if(this.confirmed){this.confirmed.emit()}}getPointsData(){if(this.points){let t=this.points[0].x+","+this.points[0].y+" ";t=t+this.points[1].x+","+this.points[1].y+" ";t=t+this.points[2].x+","+this.points[2].y+" ";t=t+this.points[3].x+","+this.points[3].y;return t}return""}renderFooter(){if(this.hidefooter===""){return""}return s("div",{class:"footer"},s("section",{class:"items"},s("div",{class:"item accept-cancel",onClick:()=>this.onCanceled()},s("img",{src:"data:image/svg+xml,%3Csvg version='1.1' id='Layer_1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' x='0px' y='0px' viewBox='0 0 512 512' enable-background='new 0 0 512 512' xml:space='preserve'%3E%3Ccircle fill='%23727A87' cx='256' cy='256' r='256'/%3E%3Cg id='Icon_5_'%3E%3Cg%3E%3Cpath fill='%23FFFFFF' d='M394.2,142L370,117.8c-1.6-1.6-4.1-1.6-5.7,0L258.8,223.4c-1.6,1.6-4.1,1.6-5.7,0L147.6,117.8 c-1.6-1.6-4.1-1.6-5.7,0L117.8,142c-1.6,1.6-1.6,4.1,0,5.7l105.5,105.5c1.6,1.6,1.6,4.1,0,5.7L117.8,364.4c-1.6,1.6-1.6,4.1,0,5.7 l24.1,24.1c1.6,1.6,4.1,1.6,5.7,0l105.5-105.5c1.6-1.6,4.1-1.6,5.7,0l105.5,105.5c1.6,1.6,4.1,1.6,5.7,0l24.1-24.1 c1.6-1.6,1.6-4.1,0-5.7L288.6,258.8c-1.6-1.6-1.6-4.1,0-5.7l105.5-105.5C395.7,146.1,395.7,143.5,394.2,142z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E"})),s("div",{class:"item accept-use",onClick:()=>this.onConfirmed()},s("img",{src:"data:image/svg+xml,%3Csvg version='1.1' id='Layer_1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' x='0px' y='0px' viewBox='0 0 512 512' enable-background='new 0 0 512 512' xml:space='preserve'%3E%3Ccircle fill='%232CD865' cx='256' cy='256' r='256'/%3E%3Cg id='Icon_1_'%3E%3Cg%3E%3Cg%3E%3Cpath fill='%23FFFFFF' d='M208,301.4l-55.4-55.5c-1.5-1.5-4-1.6-5.6-0.1l-23.4,22.3c-1.6,1.6-1.7,4.1-0.1,5.7l81.6,81.4 c3.1,3.1,8.2,3.1,11.3,0l171.8-171.7c1.6-1.6,1.6-4.2-0.1-5.7l-23.4-22.3c-1.6-1.5-4.1-1.5-5.6,0.1L213.7,301.4 C212.1,303,209.6,303,208,301.4z'/%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/svg%3E"}))))}rendenInactiveSelections(){if(!this.inactiveSelections){return""}return s(e,null,this.inactiveSelections.map(((t,i)=>s("polygon",{points:this.getPointsDataFromSelection(t),class:"inactive-selection dashed","stroke-width":this.inActiveStroke*this.getRatio(),fill:"transparent",onMouseUp:()=>this.onSelectionClicked(i),onTouchStart:()=>this.onSelectionClicked(i)}))))}onSelectionClicked(t){if(this.selectionClicked){this.selectionClicked.emit(t)}}getPointsDataFromSelection(t){let i=[];if("width"in t){i=this.getPointsFromRect(t)}else{i=t.points}let s=i[0].x+","+i[0].y+" ";s=s+i[1].x+","+i[1].y+" ";s=s+i[2].x+","+i[2].y+" ";s=s+i[3].x+","+i[3].y;return s}renderHandlers(){if(!this.points){return s("div",null)}return s(e,null,this.handlers.map((t=>s("rect",{x:this.getHandlerPos(t,"x"),y:this.getHandlerPos(t,"y"),width:this.getHandlerSize(),height:this.getHandlerSize(),class:"cropper-controls","stroke-width":t===this.selectedHandlerIndex?this.activeStroke*2*this.getRatio():this.activeStroke*this.getRatio(),fill:"transparent",onMouseDown:i=>this.onHandlerMouseDown(i,t),onMouseUp:t=>this.onHandlerMouseUp(t),onTouchStart:i=>this.onHandlerTouchStart(i,t),onPointerDown:i=>this.onHandlerPointerDown(i,t)}))))}getHandlerPos(t,i){let s=0;let e=this.getHandlerSize();if(t===0){s=this.points[0][i]}else if(t===1){s=this.points[0][i]+(this.points[1][i]-this.points[0][i])/2}else if(t===2){s=this.points[1][i]}else if(t===3){s=this.points[1][i]+(this.points[2][i]-this.points[1][i])/2}else if(t===4){s=this.points[2][i]}else if(t===5){s=this.points[3][i]+(this.points[2][i]-this.points[3][i])/2}else if(t===6){s=this.points[3][i]}else if(t===7){s=this.points[0][i]+(this.points[3][i]-this.points[0][i])/2}s=s-e/2;return s}getHandlerSize(){let t=this.getRatio();let i=20;if(this.handlersize){try{i=parseInt(this.handlersize)}catch(t){console.log(t)}}return Math.ceil(i*t)}onSVGTouchStart(t){this.usingTouchEvent=true;this.svgMouseDownPoint=undefined;this.previousDistance=undefined;let i=this.getMousePosition(t,this.svgElement);if(t.touches.length>1){this.selectedHandlerIndex=-1}else{if(this.selectedHandlerIndex!=-1){this.originalPoints=JSON.parse(JSON.stringify(this.points));this.handlerMouseDownPoint.x=i.x;this.handlerMouseDownPoint.y=i.y}else{this.svgMouseDownPoint={x:i.x,y:i.y};this.polygonMouseDown=true;this.polygonMouseDownPoint={x:i.x,y:i.y};this.originalPoints=JSON.parse(JSON.stringify(this.points))}}}onSVGTouchEnd(){this.svgMouseDownPoint=undefined}onSVGTouchMove(t){t.stopPropagation();t.preventDefault();if(t.touches.length===2){this.pinchAndZoom(t)}else{if(this.svgMouseDownPoint){this.panSVG(t)}else if(this.polygonMouseDown){this.handleMoveEvent(t)}else{this.handleMoveEvent(t)}}}pinchAndZoom(t){const i=this.getDistanceBetweenTwoTouches(t.touches[0],t.touches[1]);if(this.previousDistance){if(i-this.previousDistance>0){this.scale=Math.min(10,this.scale+.02)}else{this.scale=Math.max(.1,this.scale-.02)}this.previousDistance=i}else{this.previousDistance=i}}getDistanceBetweenTwoTouches(t,i){const s=t.clientX-i.clientX;const e=t.clientY-i.clientY;const h=s*s+e+e;return h}onContainerMouseUp(){this.svgMouseDownPoint=undefined;if(!this.usingTouchEvent){this.selectedHandlerIndex=-1;this.polygonMouseDown=false;this.hideMagnifier()}}onSVGMouseDown(t){if(!this.usingTouchEvent){let i=this.getMousePosition(t,this.svgElement);this.svgMouseDownPoint={x:i.x,y:i.y}}}onContainerWheel(t){if(t.deltaY<0){this.scale=this.scale+.1}else{this.scale=Math.max(.1,this.scale-.1)}t.preventDefault()}onContainerTouchMove(t){t.preventDefault();if(t.touches.length===2){this.pinchAndZoom(t)}}getPanAndZoomStyle(){if(this.img){return`scale(1.0)  rotate(${this.rotation}deg)`}else{return"scale(1.0)"}}onSVGMouseMove(t){if(this.svgMouseDownPoint){this.panSVG(t)}else{this.handleMoveEvent(t)}}panSVG(t){let i=this.getMousePosition(t,this.svgElement);let s=i.x-this.svgMouseDownPoint.x;let e=i.y-this.svgMouseDownPoint.y;this.offsetX=this.offsetX+s;this.offsetY=this.offsetY+e}handleMoveEvent(t){if(this.polygonMouseDown){let i=this.getMousePosition(t,this.svgElement);let s=i.x-this.polygonMouseDownPoint.x;let e=i.y-this.polygonMouseDownPoint.y;let h=JSON.parse(JSON.stringify(this.originalPoints));for(const t of h){t.x=t.x+s;t.y=t.y+e;if(t.x<0||t.y<0||t.x>this.img.naturalWidth||t.y>this.img.naturalHeight){console.log("reach bounds");return}}this.points=h;this.showMagnifier();this.updateMagnifier(i)}if(this.selectedHandlerIndex>=0){let i=this.getPointIndexFromHandlerIndex(this.selectedHandlerIndex);let s=this.getMousePosition(t,this.svgElement);let e=s.x-this.handlerMouseDownPoint.x;let h=s.y-this.handlerMouseDownPoint.y;let n=JSON.parse(JSON.stringify(this.originalPoints));if(i!=-1){let t=n[i];t.x=this.originalPoints[i].x+e;t.y=this.originalPoints[i].y+h;if(this.usingQuad===false){if(i===0){n[1].y=t.y;n[3].x=t.x}else if(i===1){n[0].y=t.y;n[2].x=t.x}else if(i===2){n[1].x=t.x;n[3].y=t.y}else if(i===3){n[0].x=t.x;n[2].y=t.y}}}else{if(this.selectedHandlerIndex===1){n[0].y=this.originalPoints[0].y+h;n[1].y=this.originalPoints[1].y+h}else if(this.selectedHandlerIndex===3){n[1].x=this.originalPoints[1].x+e;n[2].x=this.originalPoints[2].x+e}else if(this.selectedHandlerIndex===5){n[2].y=this.originalPoints[2].y+h;n[3].y=this.originalPoints[3].y+h}else if(this.selectedHandlerIndex===7){n[0].x=this.originalPoints[0].x+e;n[3].x=this.originalPoints[3].x+e}}if(this.img){this.restrainPointsInBounds(n,this.img.naturalWidth,this.img.naturalHeight)}this.points=n;this.showMagnifier();this.updateMagnifier(s)}}restrainPointsInBounds(t,i,s){for(let e=0;e<t.length;e++){const h=t[e];h.x=Math.max(0,h.x);h.x=Math.min(h.x,i);h.y=Math.max(0,h.y);h.y=Math.min(h.y,s)}}onPolygonMouseDown(t){t.stopPropagation();this.originalPoints=JSON.parse(JSON.stringify(this.points));this.polygonMouseDown=true;let i=this.getMousePosition(t,this.svgElement);this.polygonMouseDownPoint.x=i.x;this.polygonMouseDownPoint.y=i.y;this.showMagnifier()}onPolygonMouseUp(t){t.stopPropagation();if(!this.usingTouchEvent){this.selectedHandlerIndex=-1;this.polygonMouseDown=false;this.hideMagnifier()}}onPolygonTouchStart(t){this.usingTouchEvent=true;t.stopPropagation();this.selectedHandlerIndex=-1;this.polygonMouseDown=true;this.originalPoints=JSON.parse(JSON.stringify(this.points));let i=this.getMousePosition(t,this.svgElement);this.polygonMouseDownPoint={x:i.x,y:i.y};this.showMagnifier()}onPolygonTouchEnd(t){t.stopPropagation();this.selectedHandlerIndex=-1;this.polygonMouseDown=false;this.hideMagnifier()}onHandlerMouseDown(t,i){t.stopPropagation();let s=this.getMousePosition(t,this.svgElement);this.originalPoints=JSON.parse(JSON.stringify(this.points));this.handlerMouseDownPoint.x=s.x;this.handlerMouseDownPoint.y=s.y;this.selectedHandlerIndex=i}onHandlerMouseUp(t){t.stopPropagation();if(!this.usingTouchEvent){this.selectedHandlerIndex=-1;this.hideMagnifier()}}onHandlerTouchStart(t,i){this.usingTouchEvent=true;t.stopPropagation();this.polygonMouseDown=false;let s=this.getMousePosition(t,this.svgElement);this.originalPoints=JSON.parse(JSON.stringify(this.points));this.handlerMouseDownPoint.x=s.x;this.handlerMouseDownPoint.y=s.y;this.selectedHandlerIndex=i}onHandlerPointerDown(t,i){if(t.pointerType!="mouse"&&!this.usingTouchEvent){this.onHandlerMouseDown(t,i);t.preventDefault()}}getPointIndexFromHandlerIndex(t){if(t===0){return 0}else if(t===2){return 1}else if(t===4){return 2}else if(t===6){return 3}return-1}getMousePosition(t,i){let s=i.getScreenCTM();if(!s){return{x:0,y:0}}let e,h;if(t.targetTouches){e=t.targetTouches[0].clientX;h=t.targetTouches[0].clientY}else{e=t.clientX;h=t.clientY}let n=s.a*s.d-s.b*s.c;if(n===0){return{x:0,y:0}}let o={a:s.d/n,b:-s.b/n,c:-s.c/n,d:s.a/n,e:(s.c*s.f-s.d*s.e)/n,f:(s.b*s.e-s.a*s.f)/n};return{x:(e-s.e)*o.a+(h-s.f)*o.c,y:(e-s.e)*o.b+(h-s.f)*o.d}}getRatio(){if(this.img){return this.img.naturalWidth/750}else{return 1}}async resetStates(){this.scale=1;this.offsetX=0;this.offsetY=0}async getAllSelections(t){let i=[];for(let s=0;s<this.inactiveSelections.length;s++){let e=this.inactiveSelections[s];if(t){if("width"in e&&t==="quad"){e={points:this.getPointsFromRect(e)}}else if(!("width"in e)&&t==="rect"){e=this.getRectFromPoints(e.points)}}i.push(e)}let s=true;if(t){if(t==="rect"){s=false}}else{if(!this.usingQuad){s=false}}if(s){const t=await this.getQuad();i.push(t)}else{const t=await this.getRect();i.push(t)}return i}async getPoints(){return this.points}async getQuad(){return{points:this.points}}async getRect(){return this.getRectFromPoints(this.points)}getRectFromPoints(t){let i;let s;let e;let h;for(const n of t){if(!i){i=n.x;e=n.x;s=n.y;h=n.y}else{i=Math.min(n.x,i);s=Math.min(n.y,s);e=Math.max(n.x,e);h=Math.max(n.y,h)}}i=Math.floor(i);e=Math.floor(e);s=Math.floor(s);h=Math.floor(h);return{x:i,y:s,width:e-i,height:h-s}}async getImageFromBlob(t){return new Promise(((i,s)=>{let e=new FileReader;e.readAsDataURL(t);e.onloadend=function(){let t=e.result;let h=document.createElement("img");h.onload=function(){i(h)};h.onerror=function(){s()};h.src=t}}))}async getImageFromDataURL(t){return new Promise(((i,s)=>{let e=document.createElement("img");e.onload=function(){i(e)};e.onerror=function(){s()};e.src=t}))}async detect(){}getSVGWidth(){if(this.img&&this.svgElement){this.svgElement.style.height="100%";let t=this.img.naturalWidth/this.img.naturalHeight;let i=this.svgElement.clientHeight*t;if(i>this.svgElement.parentElement.clientWidth){i=this.svgElement.parentElement.clientWidth;this.svgElement.style.height=i/t+"px"}return i}return"100%"}onSVGPointerMove(t){if(t.pointerType!="mouse"&&!this.usingTouchEvent){t.stopPropagation();t.preventDefault();this.onSVGMouseMove(t)}}onSVGPointerDown(t){if(t.pointerType!="mouse"&&!this.usingTouchEvent){this.onSVGMouseDown(t)}}onSVGPointerUp(t){if(t.pointerType!="mouse"&&!this.usingTouchEvent){this.svgMouseDownPoint=undefined;this.selectedHandlerIndex=-1}}onPolygonPointerDown(t){if(t.pointerType!="mouse"&&!this.usingTouchEvent){this.onPolygonMouseDown(t)}}onPolygonPointerUp(t){t.stopPropagation();this.selectedHandlerIndex=-1;this.polygonMouseDown=false}render(){return s(h,{key:"595f27ab2301ab62f8c537808aa019f6f79f1251",ref:t=>this.root=t},s("div",{key:"d63a11a015426e38634f37f9667abbdfbd30ebe9",class:"container absolute",ref:t=>this.containerElement=t,onWheel:t=>this.onContainerWheel(t),onMouseUp:()=>this.onContainerMouseUp()},s("canvas",{key:"c30385425546a260f7719127903c2508bb208230",ref:t=>this.canvasElement=t,class:"hidden-canvas"}),s("svg",{key:"7e40859a03b51447449a7e99d7d0b6588489a5f1",version:"1.1",ref:t=>this.svgElement=t,class:"cropper-svg",xmlns:"http://www.w3.org/2000/svg",viewBox:this.viewBox,width:this.getSVGWidth(),style:{transform:this.getPanAndZoomStyle()},onMouseMove:t=>this.onSVGMouseMove(t),onMouseDown:t=>this.onSVGMouseDown(t),onTouchStart:t=>this.onSVGTouchStart(t),onTouchEnd:()=>this.onSVGTouchEnd(),onTouchMove:t=>this.onSVGTouchMove(t),onPointerMove:t=>this.onSVGPointerMove(t),onPointerDown:t=>this.onSVGPointerDown(t),onPointerUp:t=>this.onSVGPointerUp(t)},s("image",{key:"cb601391032d66a4eb65c3d6921e3dbdced81fdd",href:this.img?this.img.src:""}),this.rendenInactiveSelections(),s("polygon",{key:"1300cb3f72439dce8b274107fc10399048ccb08a",points:this.getPointsData(),class:"cropper-controls dashed","stroke-width":this.activeStroke*this.getRatio(),fill:"transparent",onMouseDown:t=>this.onPolygonMouseDown(t),onMouseUp:t=>this.onPolygonMouseUp(t),onTouchStart:t=>this.onPolygonTouchStart(t),onTouchEnd:t=>this.onPolygonTouchEnd(t),onPointerDown:t=>this.onPolygonPointerDown(t),onPointerUp:t=>this.onPolygonPointerUp(t)}),this.renderHandlers()),this.renderFooter(),s("div",{key:"f2f25691123c576a87ece3a6bbebd2396b8e14dc",class:"magnifier",ref:t=>this.magnifierElement=t}),s("slot",{key:"bb0a853af01edaf23c59192eb82465e3ecf5f313"})))}showMagnifier(){if(this.magnifierElement){this.magnifierElement.style.display="block"}}hideMagnifier(){if(this.magnifierElement){this.magnifierElement.style.display="none"}}updateMagnifier(t){if(!this.magnifierElement||!this.img)return;const i=this.getRectFromPoints(this.points);const s=100;const e=t.x-s;const h=t.y-s;const n=this.svgElement;if(n.getScreenCTM&&n.createSVGPoint){const t=n.getScreenCTM();const i=n.createSVGPoint();i.x=e;i.y=h;const s=i.matrixTransform(t);this.magnifierElement.style.left=`${s.x-40}px`;this.magnifierElement.style.top=`${s.y-210}px`}else{this.magnifierElement.style.left=`${e}px`;this.magnifierElement.style.top=`${h}px`}const o=.5;const r=Math.max(0,i.x+(t.x-i.x)/this.scale-s/o/2);const l=Math.max(0,i.y+(t.y-i.y)/this.scale-s/o/2);const c=s/o;const a=s/o;const f=0;const d=0;const u=s;const g=s;const p=document.createElement("canvas");p.width=s;p.height=s;const w=p.getContext("2d");w.drawImage(this.img,r,l,c,a,f,d,u,g);w.scale(o,o);w.strokeStyle="orange";w.lineWidth=this.activeStroke/o;w.beginPath();w.moveTo(this.points[0].x-r,this.points[0].y-l);for(let t=1;t<this.points.length;t++){w.lineTo(this.points[t].x-r,this.points[t].y-l)}w.closePath();w.stroke();this.magnifierElement.style.backgroundImage=`url(${p.toDataURL()})`}static get watchers(){return{img:["watchImgPropHandler"],rect:["watchRectPropHandler"],quad:["watchQuadPropHandler"]}}};r.style=o;export{r as image_cropper};
//# sourceMappingURL=p-de106293.entry.js.map